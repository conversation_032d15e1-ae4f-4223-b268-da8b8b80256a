package com.example.rrworkmanager

import androidx.annotation.NonNull

import cn.mtjsoft.lib_encryption.SM3.SM3Util
import cn.mtjsoft.lib_encryption.utils.Util
import com.example.rrworkmanager.util.SM2Util
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result

/** RRWorkManagerPlugin */
class RRWorkManagerPlugin: FlutterPlugin, MethodCallHandler {
  /// The MethodChannel that will the communication between Flutter and native Android
  ///
  /// This local reference serves to register the plugin with the Flutter Engine and unregister it
  /// when the Flutter Engine is detached from the Activity
  private lateinit var channel : MethodChannel

  override fun onAttachedToEngine(@NonNull flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
    channel = MethodChannel(flutterPluginBinding.binaryMessenger, "rrworkmanager")
    channel.setMethodCallHandler(this)
  }

  override fun onMethodCall(@NonNull call: MethodCall, @NonNull result: Result) {
    if (call.method == "getPlatformVersion") {
      result.success("Android ${android.os.Build.VERSION.RELEASE}")
    } else if (call.method == "SM2Encrypt") {
      val gPubkey = call.argument<String>("gPubkey")
      val contentStr = call.argument<String>("contentStr")

      val encryptedStr = SM2Util.encrypt(Util.hexStr2Bytes(gPubkey),
        contentStr?.toByteArray()
      )
      result.success(Util.byte2HexStr(encryptedStr))
    } else if (call.method == "SM3Signature") {
      val dataJson = call.argument<String>("dataJson")
      val signature = SM3Util.encryptInner(dataJson)
      result.success(signature)
    } else {
      result.notImplemented()
    }
  }

  override fun onDetachedFromEngine(@NonNull binding: FlutterPlugin.FlutterPluginBinding) {
    channel.setMethodCallHandler(null)
  }
}
