import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:rrworkmanager/base/BaseViewModel.dart';
import 'package:rrworkmanager/config/StaticFieldConfig.dart';
import 'package:rrworkmanager/net/HttpUtils.dart';
import 'package:rrworkmanager/page/records/m/feedbackRecordsApiBean.dart';
import 'package:rrworkmanager/rrworkmanagerplugin.dart';
import 'package:rrworkmanager/utils/ToastUtils.dart';

class FeedbackRecordsViewModel extends BaseViewModel {
  //工单类型
  String feedbackType = "";

  //工单状态
  String feedbackStates = "";

  //反馈记录
  List<FeedbackRecordBean> records = [];

  //分页
  int pageNum = 1;

  //条数限制
  int pageSize = 10;

  void setFeedbackType(String value) {
    feedbackType = value;
    notifyListeners();
  }

  void setFeedbackStates(String value) {
    feedbackStates = value;
    notifyListeners();
  }

  ///工单记录列表
  void requestWorkOrderRecordList(BuildContext context,
      {String workOrderType = "",
      String status = "",
      required RefreshController refreshController}) {
    pageNum = 1;
    String submitterType = "A";
    if (RRWorkManagerPlugin.platform == "Merchant") {
      submitterType = "M";
    } else if (RRWorkManagerPlugin.platform == "User") {
      submitterType = "C";
    }

    String workType = "";
    if (workOrderType == "2") {
      workType = "PS";
    } else if (workOrderType == "3") {
      workType = "TC";
    } else if (workOrderType == "1") {
      workType = "AF";
    }

    HttpUtils.getInstance().posHttp<FeedbackRecordsApiBean>(
        context, UrlConfig.workOrderRecordsQuery, {
      RequestParamConfig.submitterIdOrName:
          RRWorkManagerPlugin.userInfo["userId"],
      RequestParamConfig.submitterTypeList: [submitterType],
      RequestParamConfig.statusList: status.isNotEmpty ? [status] : [],
      RequestParamConfig.workOrderType: workType,
      "pageNum": pageNum.toString(),
      "pageSize": pageSize.toString()
    }, (data) {
      FeedbackRecordsApiBean apibean = data;
      FeedbackRecordsDataBean dataBean = apibean.data!;
      records = dataBean.rows!;

      notifyListeners();
      refreshController.refreshCompleted();
      if (records.length >= dataBean.total!) {
        refreshController.loadNoData();
      } else {
        refreshController.loadComplete();
      }
    }, (error, code) {
      ToastUtils.showToast(error);
      refreshController.refreshCompleted();
      refreshController.loadComplete();
    }, isShowLoading: false);
  }

  ///账户余额记录列表
  void requestWorkOrderRecordListMore(BuildContext context,
      {String workOrderType = "",
      String status = "",
      required RefreshController refreshController}) {
    pageNum += 1;
    String submitterType = "A";
    if (RRWorkManagerPlugin.platform == "Merchant") {
      submitterType = "M";
    } else if (RRWorkManagerPlugin.platform == "User") {
      submitterType = "C";
    }

    String workType = "";
    if (workOrderType == "2") {
      workType = "PS";
    } else if (workOrderType == "3") {
      workType = "TC";
    } else if (workOrderType == "1") {
      workType = "AF";
    }
    HttpUtils.getInstance().posHttp<FeedbackRecordsApiBean>(
        context, UrlConfig.workOrderRecordsQuery, {
      RequestParamConfig.submitterIdOrName:
          RRWorkManagerPlugin.userInfo["userId"],
      RequestParamConfig.submitterTypeList: [submitterType],
      RequestParamConfig.statusList: status.isNotEmpty ? [status] : [],
      RequestParamConfig.workOrderType: workType,
      "pageNum": pageNum.toString(),
      "pageSize": pageSize.toString()
    }, (data) {
      FeedbackRecordsApiBean apibean = data;
      FeedbackRecordsDataBean dataBean = apibean.data!;
      records.addAll(dataBean.rows!);
      notifyListeners();
      refreshController.refreshCompleted();
      if (records.length >= dataBean.total!) {
        refreshController.loadNoData();
      } else {
        refreshController.loadComplete();
      }
    }, (error, code) {
      ToastUtils.showToast(error);
      refreshController.refreshCompleted();
      refreshController.loadComplete();
    }, isShowLoading: false);
  }
}
