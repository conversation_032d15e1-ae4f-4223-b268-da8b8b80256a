Flutter crash report.
Please report a bug at https://github.com/flutter/flutter/issues.

## command

flutter --verbose assemble --no-version-check --output=/Users/<USER>/Library/Developer/Xcode/DerivedData/xWalletPro_iOS-dtmtwlrejjusjkauqzrbqwurqgpy/Build/Products/Release-iphonesimulator/ -dTargetPlatform=ios -dTargetFile=lib/main.dart -dBuildMode=release -dConfiguration=Release -dIosArchs=arm64 x86_64 -dSdkRoot=/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk -dSplitDebugInfo= -dTreeShakeIcons=false -dTrackWidgetCreation=true -dDartObfuscation=false -dAction=build -dFrontendServerStarterPath= --ExtraGenSnapshotOptions= --DartDefines= --ExtraFrontEndOptions= -dSrcRoot=/Users/<USER>/Downloads/xwallet-cusomer-ios-master 2/xWalletPro_iOS -dTargetDeviceOSVersion=18.2 -dCodesignIdentity=- release_ios_bundle_flutter_assets

## exception

_TypeError: type 'Null' is not a subtype of type 'String' in type cast

```
#0      Plugin._getDefaultPackageForPlatform (package:flutter_tools/src/plugins.dart:374:71)
#1      new Plugin._fromMultiPlatformYaml (package:flutter_tools/src/plugins.dart:181:38)
#2      new Plugin.fromYaml (package:flutter_tools/src/plugins.dart:80:21)
#3      _pluginFromPackage (package:flutter_tools/src/flutter_plugins.dart:77:17)
<asynchronous suspension>
#4      findPlugins (package:flutter_tools/src/flutter_plugins.dart:113:28)
<asynchronous suspension>
#5      generateMainDartWithPluginRegistrant (package:flutter_tools/src/flutter_plugins.dart:1726:32)
<asynchronous suspension>
#6      DartPluginRegistrantTarget.build (package:flutter_tools/src/build_system/targets/dart_plugin_registrant.dart:45:5)
<asynchronous suspension>
#7      _BuildInstance._invokeInternal (package:flutter_tools/src/build_system/build_system.dart:873:9)
<asynchronous suspension>
#8      Future.wait.<anonymous closure> (dart:async/future.dart:525:21)
<asynchronous suspension>
#9      _BuildInstance.invokeTarget (package:flutter_tools/src/build_system/build_system.dart:811:32)
<asynchronous suspension>
#10     Future.wait.<anonymous closure> (dart:async/future.dart:525:21)
<asynchronous suspension>
#11     _BuildInstance.invokeTarget (package:flutter_tools/src/build_system/build_system.dart:811:32)
<asynchronous suspension>
#12     Future.wait.<anonymous closure> (dart:async/future.dart:525:21)
<asynchronous suspension>
#13     _BuildInstance.invokeTarget (package:flutter_tools/src/build_system/build_system.dart:811:32)
<asynchronous suspension>
#14     Future.wait.<anonymous closure> (dart:async/future.dart:525:21)
<asynchronous suspension>
#15     _BuildInstance.invokeTarget (package:flutter_tools/src/build_system/build_system.dart:811:32)
<asynchronous suspension>
#16     FlutterBuildSystem.build (package:flutter_tools/src/build_system/build_system.dart:629:16)
<asynchronous suspension>
#17     AssembleCommand.runCommand (package:flutter_tools/src/commands/assemble.dart:346:32)
<asynchronous suspension>
#18     FlutterCommand.run.<anonymous closure> (package:flutter_tools/src/runner/flutter_command.dart:1551:27)
<asynchronous suspension>
#19     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#20     CommandRunner.runCommand (package:args/command_runner.dart:212:13)
<asynchronous suspension>
#21     FlutterCommandRunner.runCommand.<anonymous closure> (package:flutter_tools/src/runner/flutter_command_runner.dart:494:9)
<asynchronous suspension>
#22     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#23     FlutterCommandRunner.runCommand (package:flutter_tools/src/runner/flutter_command_runner.dart:431:5)
<asynchronous suspension>
#24     run.<anonymous closure>.<anonymous closure> (package:flutter_tools/runner.dart:98:11)
<asynchronous suspension>
#25     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#26     main (package:flutter_tools/executable.dart:102:3)
<asynchronous suspension>
```

## flutter doctor

```
[!] Flutter (Channel stable, 3.32.0, on macOS 15.5 24F74 darwin-arm64, locale en-ZW) [1,174ms]
    • Flutter version 3.32.0 on channel stable at /Users/<USER>/development/flutter
    ! The flutter binary is not on your path. Consider adding /Users/<USER>/development/flutter/bin to your path.
    ! The dart binary is not on your path. Consider adding /Users/<USER>/development/flutter/bin to your path.
    • Upstream repository https://github.com/flutter/flutter.git
    • Framework revision be698c48a6 (8 weeks ago), 2025-05-19 12:59:14 -0700
    • Engine revision 1881800949
    • Dart version 3.8.0
    • DevTools version 2.45.1
    • If those were intentional, you can disregard the above warnings; however it is recommended to use "git" directly to perform update checks and upgrades.

[✓] Android toolchain - develop for Android devices (Android SDK version 35.0.0) [5.5s]
    • Android SDK at /Users/<USER>/Library/Android/sdk
    • Platform android-35, build-tools 35.0.0
    • Java binary at: /Users/<USER>/Applications/Android Studio.app/Contents/jbr/Contents/Home/bin/java
      This is the JDK bundled with the latest Android Studio installation on this machine.
      To manually set the JDK path, use: `flutter config --jdk-dir="path/to/jdk"`.
    • Java version OpenJDK Runtime Environment (build 17.0.11+0-17.0.11b1207.24-11852314)
    • All Android licenses accepted.

[!] Xcode - develop for iOS and macOS (Xcode 16.2) [2.2s]
    • Xcode at /Applications/Xcode.app/Contents/Developer
    • Build 16C5032a
    ✗ CocoaPods not installed.
        CocoaPods is a package manager for iOS or macOS platform code.
        Without CocoaPods, plugins will not work on iOS or macOS.
        For more info, see https://flutter.dev/to/platform-plugins
      For installation instructions, see https://guides.cocoapods.org/using/getting-started.html#installation

[✓] Chrome - develop for the web [10ms]
    • Chrome at /Applications/Google Chrome.app/Contents/MacOS/Google Chrome

[✓] Android Studio (version 2024.1) [9ms]
    • Android Studio at /Users/<USER>/Applications/Android Studio.app/Contents
    • Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    • Dart plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/6351-dart
    • Java version OpenJDK Runtime Environment (build 17.0.11+0-17.0.11b1207.24-11852314)

[✓] VS Code (version 1.100.3) [5ms]
    • VS Code at /Applications/Visual Studio Code.app/Contents
    • Flutter extension version 3.112.0

[✓] Connected device (4 available) [7.3s]
    • SM S901E (mobile)                    • R5CT816D5NF               • android-arm64  • Android 15 (API 35)
    • Panashe’s iPhone (wireless) (mobile) • 00008110-00090C981A03801E • ios            • iOS 18.5 22F76
    • macOS (desktop)                      • macos                     • darwin-arm64   • macOS 15.5 24F74 darwin-arm64
    • Chrome (web)                         • chrome                    • web-javascript • Google Chrome 138.0.7204.101
    ! Error: Browsing on the local area network for panashe’s Apple Watch. Ensure the device is unlocked and discoverable via Bluetooth. (code -27)

[✓] Network resources [1,350ms]
    • All expected network resources are available.

! Doctor found issues in 2 categories.
```
