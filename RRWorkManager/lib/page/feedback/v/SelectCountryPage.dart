import 'package:azlistview/azlistview.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:rrworkmanager/common_widget/AppBarWidget.dart';
import 'package:rrworkmanager/page/feedback/m/SelectCountryBean.dart';
import 'package:rrworkmanager/page/feedback/vm/SelectCountryViewModel.dart';
import 'package:rrworkmanager/rrworkmanagerConfig.dart';
import 'package:rrworkmanager/utils/Global.dart';
import 'package:rrworkmanager/utils/StringUtils.dart';

class SelectCountryPage extends StatefulWidget {
  final String? areaCode;
  final String? mobileNationId;
  final String? country;
  final String? isShowCode;

  const SelectCountryPage(
      {Key? key, this.areaCode, this.mobileNationId, this.country, this.isShowCode}) : super(key: key);

  @override
  _SelectCountryPage createState() => _SelectCountryPage();
}

class _SelectCountryPage extends State<SelectCountryPage> {
  final SelectCountryViewModel _model = SelectCountryViewModel();
  TextEditingController searchController = TextEditingController();
  FocusNode searchNode = FocusNode();

  @override
  void initState() {
    super.initState();
    print("object ${widget.isShowCode}");
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _model.requestAllNationQuery(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<SelectCountryViewModel>(
      create: (_) => _model,
      child: Scaffold(
        appBar: AppBarUtils(
            title: trans("common_label_country"),
            callback: () { 
              Navigator.pop(context, {
                  "areaCode": widget.areaCode,
                  "mobileNationId": widget.areaCode,
                  "country": widget.country
                });
                }).build(context),
        resizeToAvoidBottomInset: false,
        body: _body(context),
      ),
    );
  }

  Widget _body(BuildContext context) {
    return Consumer<SelectCountryViewModel>(builder: (context, model, child) {
      return Column(
        children: [
          recordListWidget(context)   
        ],
      );
    });
  }

  ///国家列表接口
  Widget recordListWidget(BuildContext context) {
    if (_model.allNationList != null && _model.allNationList!.isNotEmpty) {
      return Expanded(
        child: AzListView(
          data: _model.allNationList!,
          itemCount: _model.allNationList!.length,
          itemBuilder: (BuildContext context, int index) {
            CountryBean receiver = _model.allNationList![index];
            return GestureDetector(
              onTap: () {
                Navigator.pop(context, {
                  "areaCode": receiver.areaCode,
                  "mobileNationId": receiver.key,
                  "country": receiver.value
                });
              },
              child: receiverItem(context, receiver),
            );
          },
          physics: const BouncingScrollPhysics(),
          susItemBuilder: (context, index) {
            CountryBean receiver = _model.allNationList![index];
            return Container(
                alignment: Alignment.centerLeft,
                width: 1.sw,
                color: Global.lightBackGroundColor,
                padding: EdgeInsets.symmetric(horizontal: 42.w),
                height: 64.w,
                child: Text(
                  receiver.tagIndex == "★"
                      ? "★ " + tr("login_text_commonly_used")
                      : receiver.tagIndex,
                  softWrap: false,
                  style:
                      TextStyle(color: Global.mainTextColor, fontSize: 26.sp),
                ));
          },
          indexBarData: ['★', ..._model.allNationTagList!],
          indexBarOptions: IndexBarOptions(
            needRebuild: true,
            ignoreDragCancel: true,
            textStyle: TextStyle(fontSize: 22.sp, color: RRWorkManagerConfig.mainColor),
            downTextStyle: TextStyle(fontSize: 22.sp, color: Colors.white),
            downItemDecoration:
                BoxDecoration(shape: BoxShape.circle, color: RRWorkManagerConfig.mainColor),
          ),
        ),
      );
    } else {
      return Container();
    }
  }

  Widget receiverItem(BuildContext context, CountryBean receiver) {
    return Container(
      height: 80.w,
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 42.w),
      child: Row(
        children: [
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(right: 10.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Spacer(),
                  Container(
                    padding: EdgeInsets.only(top: 5.w),
                    child: Row(
                      children: [
                        Container(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            receiver.value!,
                            style: TextStyle(
                                color: Global.mainTextColor, fontSize: 28.sp),
                          ),
                        ),
                        SizedBox(width: 80.w),
                        Container(
                          child: widget.isShowCode != null &&
                                  widget.isShowCode!.isNotEmpty
                              ? Container()
                              : Text(
                                  "+" + receiver.areaCode!,
                                  style: TextStyle(
                                      color: Global.mainTextColor,
                                      fontSize: 26.sp),
                                ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  Container(
                    color: Global.lineColor,
                    height: 1,
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
