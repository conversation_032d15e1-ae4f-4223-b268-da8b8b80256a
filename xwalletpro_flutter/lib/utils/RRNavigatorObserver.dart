import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

//
const platform = const MethodChannel('com.cpkj.xwalletPro');

class RRNavigatorObserver extends NavigatorObserver {
  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    bool canPop = route.navigator?.canPop() ?? false;
    platform.invokeMethod('canPopFlutterPage', canPop);
  }

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    if (!route.isFirst) {
      platform.invokeMethod('canPopFlutterPage', true);
    }
  }
}
