import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:html_unescape/html_unescape.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xwalletpro_flutter/utils/DemoLocalizations.dart';
import 'package:xwalletpro_flutter/utils/Global.dart';
import 'package:xwalletpro_flutter/utils/Model/News.dart';
// //翻译
// import 'package:flutter_translate/flutter_translate.dart';

class newsListDetails extends StatefulWidget {
  // newsListDetails({Key key}) : super(key: key);
  final News newsOne;

  newsListDetails(this.newsOne);

  _newsListDetailsState createState() => _newsListDetailsState();
}

class _newsListDetailsState extends State<newsListDetails> {
  static const platform = const MethodChannel('com.cpkj.xwalletPro');

  //去到原生页面
  Future<void> _backToNative() async {
    try {
      final int result =
          await platform.invokeMethod('backToNative', {'test': 'from flutter'});
      print(result);
    } on PlatformException {}
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(

        ///屏幕适配
        designSize: const Size(750, 1334),
        builder: () {
          return Scaffold(
            appBar: AppBar(
              elevation: 0.0,
              centerTitle: true,
              title: Text(
                DemoLocalizations.of(context).msgcenter_title_msgDetails,
                style: TextStyle(
                  fontSize: ScreenUtil().setWidth(36),
                  color: Color(0xFF0F0F0F),
                  fontWeight: FontWeight.w500,
                ),
              ),
              leading: IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: Colors.black,
                ),
                onPressed: () {
                  //返回消息列表页面
                  // Navigator.pop(context);
                  // _backToNative();
                  popPage(context);
                },
              ),
              backgroundColor: Colors.white,
            ),
            body: SingleChildScrollView(
              // color: Color(0xFFFFFFFF),
              // decoration: BoxDecoration(

              // ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Container(
                    padding: EdgeInsets.only(
                      left: ScreenUtil().setWidth(30),
                      right: ScreenUtil().setWidth(34),
                      top: ScreenUtil().setHeight(21),
                      bottom: ScreenUtil().setHeight(10),
                    ),
                    child: Text(
                      widget.newsOne.title,
                      style: TextStyle(
                        color: Color(0xFF404042),
                        fontSize: ScreenUtil().setWidth(32),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.only(
                      left: ScreenUtil().setWidth(30),
                      bottom: ScreenUtil().setHeight(10),
                    ),
                    child: Text(
                      widget.newsOne.sendTime,
                      style: TextStyle(
                        color: Color(0xFF777777),
                        fontSize: ScreenUtil().setWidth(24),
                      ),
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                      left: ScreenUtil().setWidth(30),
                      right: ScreenUtil().setWidth(34),
                      bottom: ScreenUtil().setWidth(10),
                    ),
                    height: ScreenUtil().setHeight(1.0),
                    color: Color(0xFFC9CACB),
                  ),
                  Container(
                    padding: EdgeInsets.only(
                      left: ScreenUtil().setWidth(30),
                      right: ScreenUtil().setWidth(34),
                      bottom: ScreenUtil().setHeight(60),
                    ),
                    child: Builder(
                      builder: (context) {
                        final unescape = HtmlUnescape();
                        final cleanedContent =
                            unescape.convert(widget.newsOne.text);
                        return Text(
                          cleanedContent,
                          style: TextStyle(fontSize: 14, height: 1.4),
                        );
                      },
                    ),
                    // HtmlView(
                    //   data: widget.newsOne.text,
                    //   scrollable: false,
                    //   onLaunchFail: (url) {
                    //     // optional, type Function
                    //     print("launch $url failed");
                    //   },
                    // ),
                    // Text(
                    //   widget.newsOne.text,
                    //   style: TextStyle(
                    //     color: Color(0xFF777777),
                    //     fontSize: ScreenUtil.getInstance().setWidth(24),
                    //   ),
                    // ),
                  ),
                ],
              ),
            ),
          );
        });
  }
}
