import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:rrworkmanager/base/BaseViewModel.dart';
import 'package:rrworkmanager/common_widget/LoadingDialog.dart';
import 'package:rrworkmanager/config/StaticFieldConfig.dart';
import 'package:rrworkmanager/net/HttpFileUril.dart';
import 'package:rrworkmanager/net/HttpUtils.dart';
import 'package:rrworkmanager/page/feedback/m/UploadImageBean.dart';
import 'package:rrworkmanager/page/feedback/m/feedbackComplaintNoticeBean.dart';
import 'package:rrworkmanager/rrworkmanagerplugin.dart';
import 'package:rrworkmanager/utils/ToastUtils.dart';

class FeedbackInputViewModel extends BaseViewModel {

    ///国家代码
  String countryCode = "86";

  ///国家代码编号
  String mobileNationId = "1";

  ///国家名称
  String country = "China";

  void setCountryCode(value) {
    print("countryCode=========$value");
    countryCode = value;
    notifyListeners();
  }

  //投诉描述
  String complaintDesc = "";

  //凭证图片
  List evidenceList = [];

  //联系电话
  String telephone = "";

  //是否同意投诉须知
  bool isAgree = false;

  //是否可以提交
  bool isCanSubmit = false;

  //须知
  String? notice;

  void setComplaintDesc(String value) {
    complaintDesc = value;
    validateInput();
    notifyListeners();
  }

  void addPicture(String path) {
    evidenceList.add(path);
    notifyListeners();
  }

  void deletePicture(int index) {
    // if (evidenceList.contains(value)) {
    evidenceList.removeAt(index);
    // }
    notifyListeners();
  }

  void setTelephone(String value) {
    telephone = value;
    validateInput();
    notifyListeners();
  }

  void setIsAgree(bool value) {
    isAgree = value;
    validateInput();
    notifyListeners();
  }

  void validateInput() {
    if (complaintDesc.isNotEmpty && evidenceList.isNotEmpty) {
      isCanSubmit = true;
    } else {
      isCanSubmit = false;
    }
  }

  //提交工单信息
  void requestWorkOrderInfoCreate(
      BuildContext context, {String? workOrderType, 
      String? workOrderSubType,
       Success? success, Fail? fail}) {
    String submitterType = "A";
    if (RRWorkManagerPlugin.platform == "Merchant") {
      submitterType = "M";
    } else if (RRWorkManagerPlugin.platform == "User") {
      submitterType = "C";
    }
    String workType = "AF";
    Map? tradeInfo;
    if (workOrderType == "product") {
      workType = "PS";
    } else if (workOrderType == "transaction") {
      workType = "TC";
      tradeInfo = RRWorkManagerPlugin.userInfo["tradeInfo"];
    }

    Map<String,dynamic> json = {
      RequestParamConfig.workOrderType: workType,
      RequestParamConfig.workOrderSubType: workOrderSubType ?? "",
      RequestParamConfig.description: complaintDesc,
      RequestParamConfig.voucherList: evidenceList,
      RequestParamConfig.phoneAreaCode: countryCode,
      RequestParamConfig.customerPhone: telephone,
      RequestParamConfig.submitterType: submitterType,
      RequestParamConfig.submitterId: RRWorkManagerPlugin.userInfo["userId"],
      RequestParamConfig.submitterName: RRWorkManagerPlugin.userInfo["userName"]
    };

    if(workType == "TC") {
      json[RequestParamConfig.transType] = tradeInfo!["tradeType"];
      json[RequestParamConfig.orderNo] = tradeInfo["orderNo"];
    }

    HttpUtils.getInstance().posHttp(
        context, UrlConfig.workOrderCreate, json, (data) {
      if(success != null)success(data);
      notifyListeners();
    }, (error, code) {
      if(fail != null)fail(error, code);
      ToastUtils.showToast(error);
    }, isShowLoading: true);
  }

  //上传所有图片
  Future<bool> uploadAllImage(BuildContext context) async {
    List localList = [];
    localList.addAll(evidenceList);
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const LoadingDialog();
        });
    for (int i = 0; i < localList.length; i++) {
      String path = localList[i];
      if (!path.contains("http")) {
        dynamic value = await uploadLocalPhoto(
          context,
          filePath: path,
          success: (data) {
            UpLoadImageBean imageBean = data;
            evidenceList[i] = imageBean.fileUrl;
          },
          fail: (error, code) {
            evidenceList.remove(path);
          },
        );
        if (value != null) {}
      }
    }
    Navigator.of(context).pop();
    notifyListeners();
    return evidenceList.length == localList.length;
  }

  //上传图片
  Future<dynamic>? uploadLocalPhoto(BuildContext context,
      {required String filePath, Success? success, Fail? fail}) async {
    Response? value = await HttpFileUtils.getInstance()
        .uploadImage<UpLoadImageBean>(context, UrlConfig.workOrderUploadImage, {
      "file": filePath,
    }, (data) {
      if (success != null) {
        success(data);
      }
    }, (error, code) {
      if (fail != null) {
        fail(error, code);
      }
      ToastUtils.showToast(error);
    }, true, false);
    return value;
  }

  //协议须知
  void requestTransactionComplaintNotice(
      BuildContext context, Success success) {
    String lanuage = "1";
    if(RRWorkManagerPlugin.currentLocale.languageCode == "zh") {
      lanuage = "2";
    }
    HttpUtils.getInstance()
        .posHttp<FeedbackComplaintNoticeBean>(context, UrlConfig.requestTransactionComplaintNotice, {
      RequestParamConfig.noticeId: lanuage,
    }, (data) {
      FeedbackComplaintNoticeBean result = data;
      notice = result.data;
    }, (error, code) {
      ToastUtils.showToast(error);
    }, isShowLoading: true);
  }
}
