# RRXNNetwork

[![CI Status](https://img.shields.io/travis/<PERSON>/RRXNNetwork.svg?style=flat)](https://travis-ci.org/Shirley/RRXNNetwork)
[![Version](https://img.shields.io/cocoapods/v/RRXNNetwork.svg?style=flat)](https://cocoapods.org/pods/RRXNNetwork)
[![License](https://img.shields.io/cocoapods/l/RRXNNetwork.svg?style=flat)](https://cocoapods.org/pods/RRXNNetwork)
[![Platform](https://img.shields.io/cocoapods/p/RRXNNetwork.svg?style=flat)](https://cocoapods.org/pods/RRXNNetwork)

## Example

To run the example project, clone the repo, and run `pod install` from the Example directory first.

## Requirements

## Installation

RRXNNetwork is available through [CocoaPods](https://cocoapods.org). To install
it, simply add the following line to your Podfile:

```ruby
pod 'RRXNNetwork'
```

## 私有库流程

1. 验证.podSpec文件
pod spec lint --use-libraries --allow-warnings --verbose --skip-import-validation 

2.检查下本地repo 查看私有的名称（XNSpecs）
pod repo list

3. 发布私有库
pod repo push XNSpecs RRXNNetwork.podspec --verbose --use-libraries --allow-warnings  --skip-import-validation

4. 搜索私有库
pod search RRXNNetwork


## Author

Shirley, <EMAIL>

## License


RRXNNetwork is available under the MIT license. See the LICENSE file for more info.
