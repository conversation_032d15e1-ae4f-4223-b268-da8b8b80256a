// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXAggregateTarget section */
		08A0C7AD2171E1B59DB7361465394C73 /* RRXNNetwork */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 9CDB0F6920843124AE017576184187EC /* Build configuration list for PBXAggregateTarget "RRXNNetwork" */;
			buildPhases = (
			);
			dependencies = (
				87A0AD161B20CB5A1427EF8D798E8C9C /* PBXTargetDependency */,
			);
			name = RRXNNetwork;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		0077E2C1E1042512F36A3204CFD42BEF /* NSObject+MJClass.m in Sources */ = {isa = PBXBuildFile; fileRef = 790EE0A342A68982BB7057E7702329F9 /* NSObject+MJClass.m */; };
		008A37B2A63BC1F3E33557FEF5BEE189 /* NSValue+Expecta.h in Headers */ = {isa = PBXBuildFile; fileRef = 62ED963B1CD4FEC6A33B440EE6BEF5B9 /* NSValue+Expecta.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00B3EF135DE4634D6FA1E5EF15239824 /* EXPMatchers+endWith.h in Headers */ = {isa = PBXBuildFile; fileRef = 2C6CEE05F9FE06AF8F93CF8E6D2B252F /* EXPMatchers+endWith.h */; settings = {ATTRIBUTES = (Public, ); }; };
		01E3E1FB1F9A0FA56C79608F8139A32F /* MJExtensionConst.m in Sources */ = {isa = PBXBuildFile; fileRef = D4686DF0C3941D9F01AB9FEEB4F9649C /* MJExtensionConst.m */; };
		033484819373AB7E020C0026CAE0F7BF /* MJFoundation.m in Sources */ = {isa = PBXBuildFile; fileRef = 7915A784D2E660A7BB7C7DC4D4C4B69D /* MJFoundation.m */; };
		05452B06007B8F1969A23C3C9D494CBD /* MJExtension-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 3136DFC41B471B914D1BAC52A9C902D0 /* MJExtension-dummy.m */; };
		05BCAED08DADF67CC4711A102262D9AB /* EXPMatchers+equal.m in Sources */ = {isa = PBXBuildFile; fileRef = 1E609CE69D35EF1788F2E2DB64CDB112 /* EXPMatchers+equal.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		05CFF6618B87EDE2069D4C5D3DDFF9EB /* EXPMatchers+beginWith.m in Sources */ = {isa = PBXBuildFile; fileRef = 2C8B863FB4605DE883491B7AD5B2F23A /* EXPMatchers+beginWith.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		07288680C9E7EDC1E240E0E3C50EE65C /* NSObject+MJProperty.m in Sources */ = {isa = PBXBuildFile; fileRef = 6A1F40A04EDE5FD6D04DF5A5BA054627 /* NSObject+MJProperty.m */; };
		08D035ADB73D33A2CCF38D93822D6BE0 /* SPTCompiledExample.m in Sources */ = {isa = PBXBuildFile; fileRef = 9DE12AA56844865807C910E6A2DB67DC /* SPTCompiledExample.m */; };
		0DE0AA588BC61692033B67CFCE4EC742 /* EXPMatchers+beSubclassOf.h in Headers */ = {isa = PBXBuildFile; fileRef = E86E5AB1F1BD5EBAD7738359C4A97FA6 /* EXPMatchers+beSubclassOf.h */; settings = {ATTRIBUTES = (Public, ); }; };
		104534994BF3B515BB9EE2C0073FCA88 /* SPTSpec.m in Sources */ = {isa = PBXBuildFile; fileRef = 55148F07A3CCF3189A0FE587174DB24D /* SPTSpec.m */; };
		114C2163DAE74196CAF6FA2E5FB633D7 /* NSObject+Expecta.h in Headers */ = {isa = PBXBuildFile; fileRef = 1DE2C277B1EF85D0F6A415CE4CE4B873 /* NSObject+Expecta.h */; settings = {ATTRIBUTES = (Public, ); }; };
		12C14FE4834134BC916F8DD42B875D3B /* EXPMatchers+beLessThanOrEqualTo.h in Headers */ = {isa = PBXBuildFile; fileRef = 5C9611D112094F627DC674556E32314E /* EXPMatchers+beLessThanOrEqualTo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		140394F97EEF0A95AB171649249F211C /* EXPMatchers+beTruthy.m in Sources */ = {isa = PBXBuildFile; fileRef = 4AEB4843988BFC2C608B9C8919888641 /* EXPMatchers+beTruthy.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		15C0054801F96BA3329545B1FAA6A85E /* EXPMatchers+haveCountOf.m in Sources */ = {isa = PBXBuildFile; fileRef = 92AF96027F720C52EE67405BFC819F7A /* EXPMatchers+haveCountOf.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		15E1E0CCB36191445EE86E9DD876417C /* UIApplication+StrictKeyWindow.m in Sources */ = {isa = PBXBuildFile; fileRef = 49EA7E87FDF87EB5362D1E5121884E7B /* UIApplication+StrictKeyWindow.m */; };
		188A55E8570219DC971027460C2BCF1B /* EXPMatchers+beSubclassOf.m in Sources */ = {isa = PBXBuildFile; fileRef = FBBFB1E7F2D1C774CE9333F2AE2410E9 /* EXPMatchers+beSubclassOf.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		19621E59AF2C3772FF6290740DB6EAB4 /* NSValue+Expecta.m in Sources */ = {isa = PBXBuildFile; fileRef = 5DF53864163311D68B642E1BA39355AE /* NSValue+Expecta.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		19F41D9168054C85C99DAE77A9A0B511 /* FBSnapshotTestCase-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 772A7558994548E0D163E8B1768A22D3 /* FBSnapshotTestCase-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1CF5E91F1AE39E4CA72E7CB9DC066BC5 /* EXPMatchers+raiseWithReason.h in Headers */ = {isa = PBXBuildFile; fileRef = 211D588A9D3D15479209B49542F7F4EB /* EXPMatchers+raiseWithReason.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1EDA4657EEF6B795B643063E05E8DC57 /* EXPMatchers+respondTo.m in Sources */ = {isa = PBXBuildFile; fileRef = D8412CD5B3B1711B0BD52FBF3B83451B /* EXPMatchers+respondTo.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		20471DE376ACB94F0E144A26EFFCFD21 /* EXPMatchers+beSupersetOf.m in Sources */ = {isa = PBXBuildFile; fileRef = 611347B410CCB816184E3E0E4886BCE9 /* EXPMatchers+beSupersetOf.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		24495C552C108DB6DC2205A79C64F72F /* XCTestCase+Specta.m in Sources */ = {isa = PBXBuildFile; fileRef = 39C56C939D311409F5101A635FBCED9D /* XCTestCase+Specta.m */; };
		244D8552B11F2D1B417EBB82BBE9E938 /* EXPMatchers+beKindOf.m in Sources */ = {isa = PBXBuildFile; fileRef = 0B7771453228448A78CA58C11A19D31B /* EXPMatchers+beKindOf.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		25C2905BD04D69AB3CCA7BCC93D07AC7 /* EXPMatchers+beGreaterThanOrEqualTo.h in Headers */ = {isa = PBXBuildFile; fileRef = 30254E1B137E2ED6644C6C0CAC0013B0 /* EXPMatchers+beGreaterThanOrEqualTo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		25E03213AA01D83EB415A04635B34F1A /* NSObject+MJKeyValue.h in Headers */ = {isa = PBXBuildFile; fileRef = 419662C188F678151E58A4D4CEE9F1E2 /* NSObject+MJKeyValue.h */; settings = {ATTRIBUTES = (Public, ); }; };
		28F23E68E55462EBC24ABF2F2F131255 /* EXPMatchers+beLessThan.h in Headers */ = {isa = PBXBuildFile; fileRef = 12900C7322B3E85024F5F93519B50068 /* EXPMatchers+beLessThan.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2A46DD69C90BDFA0AD55A31D0F16A78A /* EXPMatchers+beSupersetOf.h in Headers */ = {isa = PBXBuildFile; fileRef = 2C9E2C816EA54CEED9D2BE834C78784F /* EXPMatchers+beSupersetOf.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2A93B4FF56A270D64F8CCBF8D2DD0A6D /* EXPMatchers+FBSnapshotTest.m in Sources */ = {isa = PBXBuildFile; fileRef = 4207BEF6E1A7A56893CA9D7E43AAC21B /* EXPMatchers+FBSnapshotTest.m */; };
		2CCA1E306E93380C2AE4F1043872FDA5 /* SPTSpec.h in Headers */ = {isa = PBXBuildFile; fileRef = 1E5ECCB90FD628C17B917A3DA93A26DD /* SPTSpec.h */; settings = {ATTRIBUTES = (Public, ); }; };
		313E7D422D5BD3F636611D3D66E2685E /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D60C285E57263F5A3465E9DFFAE18CC6 /* Foundation.framework */; };
		32762E6201C8909C32899F1EA489913B /* EXPMatchers+beInstanceOf.h in Headers */ = {isa = PBXBuildFile; fileRef = 0467EAA73FA5BB16ED77C7C52778426C /* EXPMatchers+beInstanceOf.h */; settings = {ATTRIBUTES = (Public, ); }; };
		32A3818AA4621A9A9BD3ECD5CCFCEDA8 /* XCTest+Private.h in Headers */ = {isa = PBXBuildFile; fileRef = 8986979A7DC636A325D78F94E99DDBE2 /* XCTest+Private.h */; settings = {ATTRIBUTES = (Public, ); }; };
		32F37AC2805D9AFF29F79D97F6FB7D1D /* Pods-RRXNNetwork_Example-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = A8F18D79748C64DEE0B1C23EB39E28B9 /* Pods-RRXNNetwork_Example-dummy.m */; };
		336CECBF15D84BEED991129EB1D216FD /* Specta.h in Headers */ = {isa = PBXBuildFile; fileRef = 9A6898141C5F8EEC97ACFD67E1E054B7 /* Specta.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3372164C22B301D4D5F1C9C7C6DDB25D /* EXPUnsupportedObject.m in Sources */ = {isa = PBXBuildFile; fileRef = 9C7FECDD0AC2DBF2B664B164A9B0C274 /* EXPUnsupportedObject.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		3681AC7A2C3E52B5F6514D1560E13191 /* EXPMatchers+beKindOf.h in Headers */ = {isa = PBXBuildFile; fileRef = 3B264869160F6204DB704C92D4E45ADD /* EXPMatchers+beKindOf.h */; settings = {ATTRIBUTES = (Public, ); }; };
		38A289C0550F25C9EDBF6A54BABC5A18 /* EXPMatcher.h in Headers */ = {isa = PBXBuildFile; fileRef = 23219833AF1C111856F8975D22831ECD /* EXPMatcher.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3B2A59E466E5E952E51862D462BD0A6C /* EXPFloatTuple.h in Headers */ = {isa = PBXBuildFile; fileRef = 502989E6789122B273CD537861110F78 /* EXPFloatTuple.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3BAA6DDBE44238B40D6A72A16EF41814 /* MJProperty.m in Sources */ = {isa = PBXBuildFile; fileRef = F8DCA7A0EFF925F62ABFBEF2C385F3CA /* MJProperty.m */; };
		3C82710DF39F627A44AE7067B65F92C7 /* EXPMatchers+raise.h in Headers */ = {isa = PBXBuildFile; fileRef = BA6AE39182A5BF94E99130932C2054E6 /* EXPMatchers+raise.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3DB6607AA00EDF662D4328EA4F9C44EC /* EXPBlockDefinedMatcher.m in Sources */ = {isa = PBXBuildFile; fileRef = 891ED75679737218671A0645BFDAF90C /* EXPBlockDefinedMatcher.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		3E7DE43360ABFE1981835683FE47148F /* UIImage+Compare.m in Sources */ = {isa = PBXBuildFile; fileRef = 89DAABCCD5C00031E687F7D7289DDF72 /* UIImage+Compare.m */; };
		416D3BE841B08CED03D16649957ECB10 /* ExpectaSupport.h in Headers */ = {isa = PBXBuildFile; fileRef = 368784BEFB46BE221C570440A1BA20A2 /* ExpectaSupport.h */; settings = {ATTRIBUTES = (Public, ); }; };
		41B490A5CB6565458F1F507A8AD0FB45 /* EXPMatchers.h in Headers */ = {isa = PBXBuildFile; fileRef = 4FC668DC53B958DA7BC78FC3A8ADF375 /* EXPMatchers.h */; settings = {ATTRIBUTES = (Public, ); }; };
		41BBD20F99D3D0EFD9D38337503D4C1F /* MJExtension-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 386610DE489D9E6B673E2D00D0936D99 /* MJExtension-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		421E2E02871F45F6FC44045DF1144A54 /* ExpectaObject+FBSnapshotTest.h in Headers */ = {isa = PBXBuildFile; fileRef = 601BEE6CD013420E87C8A1A13E06D406 /* ExpectaObject+FBSnapshotTest.h */; settings = {ATTRIBUTES = (Public, ); }; };
		460F529F2FDF0685EFA31047BAA5D628 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9948276A8B9FABEC11ED2C7A978A2187 /* UIKit.framework */; };
		464BE5F294EA7CC26AE101D0A24EFB3C /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D60C285E57263F5A3465E9DFFAE18CC6 /* Foundation.framework */; };
		467DD58C550CA69A89271EE938C59199 /* SPTExcludeGlobalBeforeAfterEach.h in Headers */ = {isa = PBXBuildFile; fileRef = 983975F382C85EA85B4F4169E49A72CE /* SPTExcludeGlobalBeforeAfterEach.h */; settings = {ATTRIBUTES = (Public, ); }; };
		46ACAF12638940470981FA6689B0469F /* EXPMatchers+beGreaterThan.m in Sources */ = {isa = PBXBuildFile; fileRef = C56D1E3E73F0558B85D4A119ACB3A953 /* EXPMatchers+beGreaterThan.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		4A03F7E68886AE6A13DFC35E9A9B942A /* EXPMatchers+beTruthy.h in Headers */ = {isa = PBXBuildFile; fileRef = 4CE0D22C1421BE79643D46D170D35689 /* EXPMatchers+beTruthy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4A1B41B33D507DA09EF085F49492AAD1 /* SPTTestSuite.m in Sources */ = {isa = PBXBuildFile; fileRef = 0F572CBC8DE244ED0672B8D060E8C32E /* SPTTestSuite.m */; };
		4CD00C2DDD1130268D57492C92768C2D /* EXPDoubleTuple.h in Headers */ = {isa = PBXBuildFile; fileRef = DFB54509BE077BC68194651FF6B93EF4 /* EXPDoubleTuple.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4D48EA2D9F127FF844FE61016D25DDE0 /* ExpectaObject.h in Headers */ = {isa = PBXBuildFile; fileRef = 9F4AE9657AA253B4CC7CAE98DA1C8B05 /* ExpectaObject.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4E7928EF9169E1DE84CBF429162A4128 /* EXPMatchers+beGreaterThanOrEqualTo.m in Sources */ = {isa = PBXBuildFile; fileRef = CA580C56ED4C80A1C254D02C0C982513 /* EXPMatchers+beGreaterThanOrEqualTo.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		4EA3C56AE53AD37BC0273D80017164F0 /* EXPMatchers+beginWith.h in Headers */ = {isa = PBXBuildFile; fileRef = 41DA50AC2939028B8CEA80599DB1E1EF /* EXPMatchers+beginWith.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4F10A7AA65CECC8F35127673D0695B7B /* SPTExample.h in Headers */ = {isa = PBXBuildFile; fileRef = 98D37D597C20F78BCDE04426D7C648F5 /* SPTExample.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4FD8D4A9E3DFCABB9A26AA22FABA018B /* EXPMatchers+beInTheRangeOf.h in Headers */ = {isa = PBXBuildFile; fileRef = D70D8599F0BF3E221E7ACAB6BF8550C9 /* EXPMatchers+beInTheRangeOf.h */; settings = {ATTRIBUTES = (Public, ); }; };
		506200204CAFAE6387207AEF27513195 /* FBSnapshotTestController.h in Headers */ = {isa = PBXBuildFile; fileRef = DC25EC7AF2A9861136C55449797A246E /* FBSnapshotTestController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		51E7A17D617310CE81444F5D315EAEF0 /* NSString+MJExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = FE41DBBACACD729135DFC43B547E6E68 /* NSString+MJExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		521CCD99D4003505D4B65A4131BAFB22 /* EXPDefines.h in Headers */ = {isa = PBXBuildFile; fileRef = 7C48E54E864BC0C91C7C014FD2F2F7CF /* EXPDefines.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5242CFE182637D4BCCAC9E0BCC4483C4 /* EXPBlockDefinedMatcher.h in Headers */ = {isa = PBXBuildFile; fileRef = E3327E8AD1C78F4E329C96B6CE7AC818 /* EXPBlockDefinedMatcher.h */; settings = {ATTRIBUTES = (Public, ); }; };
		59D34E8065607E956AC2DC046E76B743 /* EXPMatchers+conformTo.h in Headers */ = {isa = PBXBuildFile; fileRef = 37917FF47AC9E5A0F3E31DFFFEAF18DB /* EXPMatchers+conformTo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5C1CA24C739C943A7A9A525438E3E2A8 /* UIImage+Diff.h in Headers */ = {isa = PBXBuildFile; fileRef = A7E2BDBE39E9CFE315B13E75F19E07AE /* UIImage+Diff.h */; settings = {ATTRIBUTES = (Private, ); }; };
		5C6754FDD1DFAD63B3689EE5A7897F01 /* EXPMatchers+beCloseTo.h in Headers */ = {isa = PBXBuildFile; fileRef = 29D444D4612EFAFF94A50A389D3F5173 /* EXPMatchers+beCloseTo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5DD71838737687E6FA4583D98B08D7D4 /* EXPMatchers+beInTheRangeOf.m in Sources */ = {isa = PBXBuildFile; fileRef = 2A5DF5119E0065B4D021C2A91582E9F3 /* EXPMatchers+beInTheRangeOf.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		5E3B7A0571C1949090A34F9C7871AD42 /* XCTestCase+Specta.h in Headers */ = {isa = PBXBuildFile; fileRef = 212794D7A73C01B2BE55BE10930CE17A /* XCTestCase+Specta.h */; settings = {ATTRIBUTES = (Public, ); }; };
		61CA94EF2639807466427720DA2091C6 /* EXPDoubleTuple.m in Sources */ = {isa = PBXBuildFile; fileRef = 7EF3FAA9224F8A8229C44844741467DC /* EXPDoubleTuple.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		62C82C06E44453A6F10D76B4F1C9A8EB /* SPTSharedExampleGroups.h in Headers */ = {isa = PBXBuildFile; fileRef = 4F47101339D7DAC052A6468FD3D93E33 /* SPTSharedExampleGroups.h */; settings = {ATTRIBUTES = (Public, ); }; };
		62E551E2D06D6CC46FEA244D299BE2A4 /* Expecta+Snapshots-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = A782737B791EBEB2970838B0FBCE5C64 /* Expecta+Snapshots-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		66096613585721460B95DA2C43D07E18 /* SPTCompiledExample.h in Headers */ = {isa = PBXBuildFile; fileRef = D73ACE5D872599443EB28233EEBB224E /* SPTCompiledExample.h */; settings = {ATTRIBUTES = (Public, ); }; };
		660F79D9086A7962E1FFFA101802E8E0 /* EXPMatchers+match.h in Headers */ = {isa = PBXBuildFile; fileRef = 9E195CF6EC3CE2F10FE01161112613A9 /* EXPMatchers+match.h */; settings = {ATTRIBUTES = (Public, ); }; };
		66ABBD694E306E452A0D3DB3972FA429 /* EXPMatchers+beIdenticalTo.h in Headers */ = {isa = PBXBuildFile; fileRef = 43C7DAEED4A26D0C3078E2BA1B83B1BA /* EXPMatchers+beIdenticalTo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6760CBEEEF3F3D63CFEC0F6CAA5E874E /* FBSnapshotTestCase.m in Sources */ = {isa = PBXBuildFile; fileRef = 3B1067DB6C8A9D3DDD83223CA265454F /* FBSnapshotTestCase.m */; };
		69A41F3EBD91571F24C3047B901FB519 /* UIImage+Snapshot.m in Sources */ = {isa = PBXBuildFile; fileRef = 3F054BA0A9243BC9BF84E14F820AE28E /* UIImage+Snapshot.m */; };
		6B575D5C03624D5A253D6C128F3768DC /* EXPMatchers+beGreaterThan.h in Headers */ = {isa = PBXBuildFile; fileRef = D848762F45FD6F8FDC89FD0C477176FF /* EXPMatchers+beGreaterThan.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6E8A45EF6CD46D90A634E1E9163E562E /* EXPMatchers+beFalsy.m in Sources */ = {isa = PBXBuildFile; fileRef = 32E8133C1375E4A872337CFAEC020A39 /* EXPMatchers+beFalsy.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		6EC015330CAC3D60745A20B01928B8DB /* NSObject+MJCoding.h in Headers */ = {isa = PBXBuildFile; fileRef = 2FB71647BF31584DB75C2AD6212C96AC /* NSObject+MJCoding.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7155607EADA71F37CFE188D866B7E40A /* MJPropertyKey.m in Sources */ = {isa = PBXBuildFile; fileRef = 8B73A651DC88E3BA3EEDCA4BBA4EC445 /* MJPropertyKey.m */; };
		73387C9F80146B32C2C6A6912385157E /* SpectaDSL.h in Headers */ = {isa = PBXBuildFile; fileRef = 55E2B922E82C59540A02DF9D77901AA1 /* SpectaDSL.h */; settings = {ATTRIBUTES = (Public, ); }; };
		76930156A1D3E8B1CA29285999690F6C /* Pods-RRXNNetwork_Tests-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = E1DF883F7B22C901CCC6813193F842E8 /* Pods-RRXNNetwork_Tests-dummy.m */; };
		7709CF64039955B3BAF42CDA38E0F96F /* EXPMatchers+contain.m in Sources */ = {isa = PBXBuildFile; fileRef = 072CC0B14503CE31B8875E17FFA70071 /* EXPMatchers+contain.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		777DB2CAF42F78F06C61F9A4AA3AD09C /* EXPUnsupportedObject.h in Headers */ = {isa = PBXBuildFile; fileRef = 01AEC93B1B10F81FCEFBC47535542C1D /* EXPUnsupportedObject.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7967AB2E31F5D064244EB23735D0B3E8 /* ExpectaSupport.m in Sources */ = {isa = PBXBuildFile; fileRef = 881F1109DD764A2172DAE18897CC9654 /* ExpectaSupport.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		7BE71B62F38458AC771581F60006F9CE /* NSObject+MJCoding.m in Sources */ = {isa = PBXBuildFile; fileRef = CF2A9C07FF2F529D95D33D74101BB22A /* NSObject+MJCoding.m */; };
		7D22935560DBA8F2EE62C3609A9EC3F1 /* XCTest.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 264BACBF8E31580B57DC70B9E73A944E /* XCTest.framework */; };
		7E241629D4A2B42D8BD26BC1007CCF82 /* SpectaUtility.h in Headers */ = {isa = PBXBuildFile; fileRef = 102C20B67141BCE0A013903F84EF6C47 /* SpectaUtility.h */; settings = {ATTRIBUTES = (Public, ); }; };
		821F025A006CB157497E83C0EC102052 /* Expecta-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = F70501C0FB9F8131A5D7AD1534353377 /* Expecta-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8431178BE956AD1EAD03D5A155661A49 /* SpectaTypes.h in Headers */ = {isa = PBXBuildFile; fileRef = 74B9B16385F7011FD061861BE966435F /* SpectaTypes.h */; settings = {ATTRIBUTES = (Public, ); }; };
		850E38ABD196F6844F7BCF89586C1A65 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D60C285E57263F5A3465E9DFFAE18CC6 /* Foundation.framework */; };
		86BEB7586600AA84390E9281D76EF3C9 /* Specta-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 294A242FE22C146084141E637C157592 /* Specta-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		873165886F20870B205EBE37F8185546 /* EXPMatchers+beNil.h in Headers */ = {isa = PBXBuildFile; fileRef = 3D8092219D1A603A113D45B96BB428C4 /* EXPMatchers+beNil.h */; settings = {ATTRIBUTES = (Public, ); }; };
		87602C676D877CE47FD5F9D1813EAA96 /* XCTest.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 264BACBF8E31580B57DC70B9E73A944E /* XCTest.framework */; };
		8A2E1E487FBFC3DF55C12C056BFA36A1 /* MJPropertyType.m in Sources */ = {isa = PBXBuildFile; fileRef = BE85E66E07EB210EA1ACD44595CEF7DF /* MJPropertyType.m */; };
		8B8CB2F1361DD1EA4711D54F596569F4 /* EXPMatcherHelpers.h in Headers */ = {isa = PBXBuildFile; fileRef = 622D44842BCF1066557675DA74D84E77 /* EXPMatcherHelpers.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8E30B7DA0041E761CA840C538EC05152 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D60C285E57263F5A3465E9DFFAE18CC6 /* Foundation.framework */; };
		8FA1B04566FB2C0BB648058E80B26F35 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 208BAA193E3DBC090FB7DBAD81A67EB5 /* QuartzCore.framework */; };
		912758556B9AB1DEEABE189955C5B8F2 /* MJExtensionConst.h in Headers */ = {isa = PBXBuildFile; fileRef = B5E8936F710B1628D3CDBC8E627F04F2 /* MJExtensionConst.h */; settings = {ATTRIBUTES = (Public, ); }; };
		91F08081D0286CC0733C2D5A9B4A8F32 /* EXPMatchers+raise.m in Sources */ = {isa = PBXBuildFile; fileRef = E813254CCBCBE38CE3E20CC3A8A56A23 /* EXPMatchers+raise.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		96BC54118F99CACBCEDE0B64784E0737 /* Expecta-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 07113274D27DE2F2912B7899EAF4DCB6 /* Expecta-dummy.m */; };
		9796F9FAC1941A992E8B17F31D62B2E4 /* MJExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = DC669F3D73A900F252DA646BF63557CF /* MJExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		97D9A825385A35B046B1DFF01F24136F /* Expecta.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D1B0D3E57843600BA627CA7E43E3AA9 /* Expecta.h */; settings = {ATTRIBUTES = (Public, ); }; };
		99BF63084CB0EDEA0A9CBB77E8315E6E /* NSObject+MJKeyValue.m in Sources */ = {isa = PBXBuildFile; fileRef = 4137DDCE24A99B879F4B6136CDD8A92C /* NSObject+MJKeyValue.m */; };
		9AC1A3902FC9162C9B432B411928B215 /* EXPMatchers+beLessThanOrEqualTo.m in Sources */ = {isa = PBXBuildFile; fileRef = 2B91EBE018915AC5893E7664DF7D73BE /* EXPMatchers+beLessThanOrEqualTo.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		9CBBE07B6B6DD81BF799043AF0C2432B /* XCTest.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 264BACBF8E31580B57DC70B9E73A944E /* XCTest.framework */; };
		A1E07AC96433D5912BC3075D3FF0C207 /* SPTCallSite.m in Sources */ = {isa = PBXBuildFile; fileRef = 9E5ED21AD446EF506FA61C5B1C442469 /* SPTCallSite.m */; };
		A47472F50A3A7869A644789159571A5C /* MJPropertyKey.h in Headers */ = {isa = PBXBuildFile; fileRef = 6259D0E1511B4BB6CCA4126CD2CF6DEC /* MJPropertyKey.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AAD41575D71560C5BE9D3BEC532028F7 /* EXPMatchers+FBSnapshotTest.h in Headers */ = {isa = PBXBuildFile; fileRef = D7F3C9A23A37CC31AEA639F1B67E8B17 /* EXPMatchers+FBSnapshotTest.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AC5F9E839FB9F882601510390E523747 /* SPTSharedExampleGroups.m in Sources */ = {isa = PBXBuildFile; fileRef = 10AA0332DC8304347E64E2C275E780B2 /* SPTSharedExampleGroups.m */; };
		ACBDE7479CEDFD9F3261E26D6B1015FC /* EXPMatchers+haveCountOf.h in Headers */ = {isa = PBXBuildFile; fileRef = 6F59FBE37C815536ABB90249D7231F9B /* EXPMatchers+haveCountOf.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AF493331C1B8B62E4FB770EE9C144E22 /* Expecta.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 47A46A19691546953C5903961C3E1388 /* Expecta.framework */; };
		B0502CA7E2E35CB490EFAF55702B626C /* UIImage+Diff.m in Sources */ = {isa = PBXBuildFile; fileRef = 65678EBF223B84DBAF92801A83F6F8B3 /* UIImage+Diff.m */; };
		B1E009C09736B0D8150B3FF489A1B627 /* ExpectaObject+FBSnapshotTest.m in Sources */ = {isa = PBXBuildFile; fileRef = A33ADB8F10C23D6DC7566FE1D2FB05E9 /* ExpectaObject+FBSnapshotTest.m */; };
		B24C0951E730C8D7B6539598555A0126 /* EXPMatchers+match.m in Sources */ = {isa = PBXBuildFile; fileRef = 2AD6710C63F6E766E5112AB8ACFAF0C7 /* EXPMatchers+match.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		B31E1ABB0DF9F6E7C3B8BEC90C04FCBE /* EXPMatchers+beLessThan.m in Sources */ = {isa = PBXBuildFile; fileRef = 16AFD8BC14616D7DCB84913F08038101 /* EXPMatchers+beLessThan.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		B5596C58EE23B7C7BCE850A4C6679F1C /* EXPMatchers+beInstanceOf.m in Sources */ = {isa = PBXBuildFile; fileRef = E5C7BFA922B49E10736ED1893E9759FB /* EXPMatchers+beInstanceOf.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		B628F2BB909FBE9D533C0C6D382BAA9E /* ExpectaObject.m in Sources */ = {isa = PBXBuildFile; fileRef = 79D936ABC02A3D14848CBC3266356835 /* ExpectaObject.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		B94AD428EA4EFD7BA79BCF57680D390A /* MJPropertyType.h in Headers */ = {isa = PBXBuildFile; fileRef = FD0E58C98AE5D9A1083E12EBF616A809 /* MJPropertyType.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B999DEBC999F335CC8E6C471F942F93D /* EXPExpect.m in Sources */ = {isa = PBXBuildFile; fileRef = 5D8F0C127E4BBCF78962592FB946C801 /* EXPExpect.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		C01879F2C9DEAB70FB7C5CFB3B7C4647 /* SPTExample.m in Sources */ = {isa = PBXBuildFile; fileRef = 431A32406DCF7180C1321BCFFAC70B28 /* SPTExample.m */; };
		C0C8961B2EAF90DA7244B427C569FCFD /* NSObject+MJProperty.h in Headers */ = {isa = PBXBuildFile; fileRef = F21E831B60DD880B6CC15B7EA09B9DB0 /* NSObject+MJProperty.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C18FBD6CD221CAFD1AF15CC99A48CB84 /* EXPMatchers+beFalsy.h in Headers */ = {isa = PBXBuildFile; fileRef = 96003F8C7A01676C3384A0A3A81C9D21 /* EXPMatchers+beFalsy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C3576E9AC95E9412AF6CD051A4E8E55E /* XCTest.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 264BACBF8E31580B57DC70B9E73A944E /* XCTest.framework */; };
		C42994AEEDDC8243740ED31DB9007807 /* SpectaDSL.m in Sources */ = {isa = PBXBuildFile; fileRef = 55A1A7755805EF535958531DDC9AF1BD /* SpectaDSL.m */; };
		C4632B64DD0F3ADB3BB829A6EBC322E6 /* EXPMatcherHelpers.m in Sources */ = {isa = PBXBuildFile; fileRef = 80BF039FD3648587C5C1183C7973D0FD /* EXPMatcherHelpers.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		C692F5BF6AA4421A622E9080EA79F7FA /* SPTExampleGroup.h in Headers */ = {isa = PBXBuildFile; fileRef = F9AC5F1248A1159E01F1C799EDA2A961 /* SPTExampleGroup.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C6A9508A7FD62A641E84BA24EAE1221D /* Pods-RRXNNetwork_Example-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 81F5733C6B17B7D42263FE8ABC147497 /* Pods-RRXNNetwork_Example-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C8D5363E37368C0CFE0DA66E1F5DD75D /* Specta.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6867F62D06A185737C966370974491E5 /* Specta.framework */; };
		C94EA6C4EA7695C4966E661E387C02FE /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D60C285E57263F5A3465E9DFFAE18CC6 /* Foundation.framework */; };
		CABEED8EF629A236023F097BB06F5162 /* Pods-RRXNNetwork_Tests-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 8547D62E2B32EFE850778FD8277DFAE2 /* Pods-RRXNNetwork_Tests-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		CAD2539BE3C4AB2F1FD1DF30D7629238 /* MJFoundation.h in Headers */ = {isa = PBXBuildFile; fileRef = E7F1041A2732068BD7222489683E42F5 /* MJFoundation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		CAEE0E44BC37459BD49C0B1D626B0477 /* EXPMatchers+postNotification.m in Sources */ = {isa = PBXBuildFile; fileRef = 79F54D243C8437938281AD840EF379A4 /* EXPMatchers+postNotification.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		CBC2D78B7950ADE46E54AB83721F1ADF /* SPTTestSuite.h in Headers */ = {isa = PBXBuildFile; fileRef = 274871DD1E844FF2523BB9FCE73472F1 /* SPTTestSuite.h */; settings = {ATTRIBUTES = (Public, ); }; };
		CDB51044D4EE9EB628D098CF53F75985 /* SwiftSupport.swift in Sources */ = {isa = PBXBuildFile; fileRef = 402A2673376D8C5B96DA384361EF5486 /* SwiftSupport.swift */; };
		D07B657936CAAF7E35236DF1A85E4C36 /* SPTGlobalBeforeAfterEach.h in Headers */ = {isa = PBXBuildFile; fileRef = 208F9A37FD362DA6240E8C1CA770D6AE /* SPTGlobalBeforeAfterEach.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D327EEB8522F6389513EB84ACF12F242 /* EXPMatchers+endWith.m in Sources */ = {isa = PBXBuildFile; fileRef = 4954EF1FA93DB1C8247D9B4E5017290C /* EXPMatchers+endWith.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		D4472453A8147DF973BAAB33AE7320E0 /* Expecta+Snapshots-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 618B79E532D687B28CCBB20EDF93F806 /* Expecta+Snapshots-dummy.m */; };
		D5030AF577970FF20BFA041AB9D7FA9F /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D60C285E57263F5A3465E9DFFAE18CC6 /* Foundation.framework */; };
		D5C3E91A2121AE27F99028292B9436A8 /* EXPMatchers+postNotification.h in Headers */ = {isa = PBXBuildFile; fileRef = 455AC5279955ABC2065FBBF2E3111664 /* EXPMatchers+postNotification.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D6DC25726EA495CE08EFA81AF65B81B0 /* FBSnapshotTestCasePlatform.m in Sources */ = {isa = PBXBuildFile; fileRef = 7897438240A4B99F78C7801FDE0A4675 /* FBSnapshotTestCasePlatform.m */; };
		D7E0395840A93502D928B2B392479C5E /* FBSnapshotTestCasePlatform.h in Headers */ = {isa = PBXBuildFile; fileRef = FBB26F7A12B2E3089BBE83452DDAD08A /* FBSnapshotTestCasePlatform.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DBFF33BEB222CF58005C06FF0CD556DF /* EXPExpect.h in Headers */ = {isa = PBXBuildFile; fileRef = 7F13AE7BE9A65D76B00177E02A0D438A /* EXPExpect.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DE7FC5D8F81CE893DB6EDF99865350C5 /* EXPMatchers+conformTo.m in Sources */ = {isa = PBXBuildFile; fileRef = 1D7AD6BB9802C647F6046ADA2DDA180A /* EXPMatchers+conformTo.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		E2058F4A0E7638E420DE96F8DC17AAA9 /* EXPMatchers+raiseWithReason.m in Sources */ = {isa = PBXBuildFile; fileRef = A15DFDE56F9FD14F3F2F033433A91221 /* EXPMatchers+raiseWithReason.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		E6775033B3F0080B56E29AB395A08867 /* NSString+MJExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = 59876E021357905508D9A38EE03701B9 /* NSString+MJExtension.m */; };
		E791EEB9E7DA8C5DBDFF80A2BA7A6A4F /* EXPMatchers+respondTo.h in Headers */ = {isa = PBXBuildFile; fileRef = 49FEB6D42274C6EDD572165950C20F56 /* EXPMatchers+respondTo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E969F8D679A40C630D2E855651A49D90 /* EXPMatchers+contain.h in Headers */ = {isa = PBXBuildFile; fileRef = B43C5FB7E24ADDE8FF6EB9E4656206B7 /* EXPMatchers+contain.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EA52D7D80F55EA982F56D85B1878F70C /* UIImage+Compare.h in Headers */ = {isa = PBXBuildFile; fileRef = 7C5C955AD707E98A05704CEBDF2B001E /* UIImage+Compare.h */; settings = {ATTRIBUTES = (Private, ); }; };
		EB37F8D1C24FE29BC22691ED1C0FDAD3 /* SPTCallSite.h in Headers */ = {isa = PBXBuildFile; fileRef = 2D2155790E45F071C8893E3CAF573BD9 /* SPTCallSite.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EBDF90B06D57C3007BE2C2E41EA38715 /* FBSnapshotTestCase-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 7E2CD51A7DE4DBBFF42C8C7FC784B874 /* FBSnapshotTestCase-dummy.m */; };
		EC57F974317D76B799CFE2A018B5F9E2 /* EXPMatchers+beNil.m in Sources */ = {isa = PBXBuildFile; fileRef = 5D8999290497BEA07403FE4F0A2969B8 /* EXPMatchers+beNil.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		EEC477D6B17E9AB60D60728946BA8D1D /* FBSnapshotTestCase.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4A330379CAB29C00FEA5BBD48341C045 /* FBSnapshotTestCase.framework */; };
		F07956640E1263458BD4CC074C68FA9B /* UIApplication+StrictKeyWindow.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D6310B7A96B3658889C1A8A3B1C9AA3 /* UIApplication+StrictKeyWindow.h */; settings = {ATTRIBUTES = (Project, ); }; };
		F0EF96DAB145D1B2247C01B3517B54FE /* NSObject+MJClass.h in Headers */ = {isa = PBXBuildFile; fileRef = 31B427E04B6036ED2404CDE9B088D7BE /* NSObject+MJClass.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F123A8E2BCE34F84A7C42887F33116E2 /* SPTExampleGroup.m in Sources */ = {isa = PBXBuildFile; fileRef = BC15168025F815F9FEEF2A78613859EE /* SPTExampleGroup.m */; };
		F31CCBB90D75D9E5A83545FD95954FFA /* EXPMatchers+beCloseTo.m in Sources */ = {isa = PBXBuildFile; fileRef = 4A10012CDEF18B2D00F3DAED085A1CD6 /* EXPMatchers+beCloseTo.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		F346958A5DC33C5043C2BF36C6D49CE9 /* FBSnapshotTestController.m in Sources */ = {isa = PBXBuildFile; fileRef = C1F2E0FD3E71C714E9A0278673E2B486 /* FBSnapshotTestController.m */; };
		F3542D58505D8D67A9DA62BF0816F759 /* SpectaUtility.m in Sources */ = {isa = PBXBuildFile; fileRef = 2BDD457D5FEDFB4E52AA67296865F6B2 /* SpectaUtility.m */; };
		F3EDE6D241EE2A6D0992A24ACDDBAB35 /* EXPFloatTuple.m in Sources */ = {isa = PBXBuildFile; fileRef = 76624D5BA62353EB67AE8738A0C7E9F8 /* EXPFloatTuple.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		F4591B9C7F70268A3F94B40ADDBA5202 /* EXPMatchers+beIdenticalTo.m in Sources */ = {isa = PBXBuildFile; fileRef = 969AEDC1AA3588046CDDBA95234B6BE2 /* EXPMatchers+beIdenticalTo.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		F4BD31D7FFFC8EC2102581C790B8AD57 /* UIImage+Snapshot.h in Headers */ = {isa = PBXBuildFile; fileRef = 0975CAAFFF8F6C46E61131940FDA9DB1 /* UIImage+Snapshot.h */; settings = {ATTRIBUTES = (Private, ); }; };
		F4E585B2607D1B6F6FB3198A40888F5E /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D60C285E57263F5A3465E9DFFAE18CC6 /* Foundation.framework */; };
		F5D444B578A69550DFF8BE1C6A5D2CFC /* Specta-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D4DCBEB5FA53C63CD1C7924BFC72248 /* Specta-dummy.m */; };
		F77F788A1F6AC791644AB70E5AA31B60 /* FBSnapshotTestCase.h in Headers */ = {isa = PBXBuildFile; fileRef = 1B8DCCC0586CCA186A9B441AC83F65B2 /* FBSnapshotTestCase.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FAB7CF107E7E3EE6FA7949CF18B8BA12 /* MJProperty.h in Headers */ = {isa = PBXBuildFile; fileRef = 334651540DDF81475209AB218B269345 /* MJProperty.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FBC9734FF84807AE5347B2196D7EBC6D /* EXPMatchers+equal.h in Headers */ = {isa = PBXBuildFile; fileRef = C99C1D255FA392B89C170C89F77EF4CA /* EXPMatchers+equal.h */; settings = {ATTRIBUTES = (Public, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		0E9A3D3EFBE113FE99878CA2AA08FA7B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 4D3BA58D0583DF37575CACAB3DDADC85;
			remoteInfo = MJExtension;
		};
		10F417E8A0F1AE38B3C8BB75F0A066A7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BEB305119BDB853F0B6A905DA436143F;
			remoteInfo = "Pods-RRXNNetwork_Example";
		};
		349380DC267AD294183C697ACA4321DC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F8676010755CF1530FC02DA9A0D8822B;
			remoteInfo = Specta;
		};
		4515D7A38A49166269D9AB4E98CDC7D4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DC371B7477C88184274EC6710690F97C;
			remoteInfo = Expecta;
		};
		61EDDB15091045A9C90F0DE1423B4B38 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8B98F09738742E4D780D1B20B468CD95;
			remoteInfo = "Expecta+Snapshots";
		};
		99B96CE6B0894A063CA71E74CE287DD6 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DC371B7477C88184274EC6710690F97C;
			remoteInfo = Expecta;
		};
		A0DA94BE05F7A760579BA78E0968BDB7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 08A0C7AD2171E1B59DB7361465394C73;
			remoteInfo = RRXNNetwork;
		};
		ADFBE44D73599FEEC8C991F9CFC627C2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F8676010755CF1530FC02DA9A0D8822B;
			remoteInfo = Specta;
		};
		CB43A4E21BE74BF94150FBEA89EF5B4B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 4D3BA58D0583DF37575CACAB3DDADC85;
			remoteInfo = MJExtension;
		};
		CBFA23AF8313C43BCBB3A76E4307E725 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 98A98149697C80CEF8D5772791E92E66;
			remoteInfo = FBSnapshotTestCase;
		};
		FF6FA1F98B129F011D3B4BD0514978AA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 98A98149697C80CEF8D5772791E92E66;
			remoteInfo = FBSnapshotTestCase;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		01AEC93B1B10F81FCEFBC47535542C1D /* EXPUnsupportedObject.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = EXPUnsupportedObject.h; path = Expecta/EXPUnsupportedObject.h; sourceTree = "<group>"; };
		021FE5C7B055283BF4F548655823EAB5 /* Pods-RRXNNetwork_Example-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-RRXNNetwork_Example-frameworks.sh"; sourceTree = "<group>"; };
		02CF983B37B50F1FDA2D6F1A295626F2 /* Specta-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Specta-Info.plist"; sourceTree = "<group>"; };
		0467EAA73FA5BB16ED77C7C52778426C /* EXPMatchers+beInstanceOf.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+beInstanceOf.h"; path = "Expecta/Matchers/EXPMatchers+beInstanceOf.h"; sourceTree = "<group>"; };
		0478165A2541D70C4B57E197DD391FD2 /* Expecta+Snapshots.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Expecta+Snapshots.debug.xcconfig"; sourceTree = "<group>"; };
		07113274D27DE2F2912B7899EAF4DCB6 /* Expecta-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Expecta-dummy.m"; sourceTree = "<group>"; };
		072CC0B14503CE31B8875E17FFA70071 /* EXPMatchers+contain.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+contain.m"; path = "Expecta/Matchers/EXPMatchers+contain.m"; sourceTree = "<group>"; };
		07B52F27CB1E9D0F96D7BDC25FD0F2B5 /* Specta.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = Specta.modulemap; sourceTree = "<group>"; };
		08F7F0770B4878B9883B87DCD8569CB4 /* Expecta.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = Expecta.framework; path = Expecta.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		0975CAAFFF8F6C46E61131940FDA9DB1 /* UIImage+Snapshot.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+Snapshot.h"; path = "FBSnapshotTestCase/Categories/UIImage+Snapshot.h"; sourceTree = "<group>"; };
		0A244A4D3C362018305E401721ED2134 /* Expecta+Snapshots-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Expecta+Snapshots-prefix.pch"; sourceTree = "<group>"; };
		0B7771453228448A78CA58C11A19D31B /* EXPMatchers+beKindOf.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+beKindOf.m"; path = "Expecta/Matchers/EXPMatchers+beKindOf.m"; sourceTree = "<group>"; };
		0C7496B2DA4E69A0C6FFC62FD18A94C3 /* Expecta-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Expecta-Info.plist"; sourceTree = "<group>"; };
		0E94121B11103286987A23F57F9390BF /* MJExtension.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = MJExtension.release.xcconfig; sourceTree = "<group>"; };
		0F572CBC8DE244ED0672B8D060E8C32E /* SPTTestSuite.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SPTTestSuite.m; path = Specta/Specta/SPTTestSuite.m; sourceTree = "<group>"; };
		102C20B67141BCE0A013903F84EF6C47 /* SpectaUtility.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SpectaUtility.h; path = Specta/Specta/SpectaUtility.h; sourceTree = "<group>"; };
		10AA0332DC8304347E64E2C275E780B2 /* SPTSharedExampleGroups.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SPTSharedExampleGroups.m; path = Specta/Specta/SPTSharedExampleGroups.m; sourceTree = "<group>"; };
		12900C7322B3E85024F5F93519B50068 /* EXPMatchers+beLessThan.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+beLessThan.h"; path = "Expecta/Matchers/EXPMatchers+beLessThan.h"; sourceTree = "<group>"; };
		15B13B063AA97C48C9010C298AECBDDA /* Specta.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = Specta.framework; path = Specta.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		16AFD8BC14616D7DCB84913F08038101 /* EXPMatchers+beLessThan.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+beLessThan.m"; path = "Expecta/Matchers/EXPMatchers+beLessThan.m"; sourceTree = "<group>"; };
		1B5421659DA42C2538665DE74BEFA0B6 /* FBSnapshotTestCase-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "FBSnapshotTestCase-prefix.pch"; sourceTree = "<group>"; };
		1B8DCCC0586CCA186A9B441AC83F65B2 /* FBSnapshotTestCase.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FBSnapshotTestCase.h; path = FBSnapshotTestCase/FBSnapshotTestCase.h; sourceTree = "<group>"; };
		1C6F807A2A4DFB9995242A15DBFBB5F9 /* Expecta-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Expecta-prefix.pch"; sourceTree = "<group>"; };
		1D7AD6BB9802C647F6046ADA2DDA180A /* EXPMatchers+conformTo.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+conformTo.m"; path = "Expecta/Matchers/EXPMatchers+conformTo.m"; sourceTree = "<group>"; };
		1D958A54911535F8F383FDCABCDBE29D /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; path = LICENSE; sourceTree = "<group>"; };
		1DE2C277B1EF85D0F6A415CE4CE4B873 /* NSObject+Expecta.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSObject+Expecta.h"; path = "Expecta/NSObject+Expecta.h"; sourceTree = "<group>"; };
		1E5ECCB90FD628C17B917A3DA93A26DD /* SPTSpec.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SPTSpec.h; path = Specta/Specta/SPTSpec.h; sourceTree = "<group>"; };
		1E609CE69D35EF1788F2E2DB64CDB112 /* EXPMatchers+equal.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+equal.m"; path = "Expecta/Matchers/EXPMatchers+equal.m"; sourceTree = "<group>"; };
		208BAA193E3DBC090FB7DBAD81A67EB5 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.0.sdk/System/Library/Frameworks/QuartzCore.framework; sourceTree = DEVELOPER_DIR; };
		208F9A37FD362DA6240E8C1CA770D6AE /* SPTGlobalBeforeAfterEach.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SPTGlobalBeforeAfterEach.h; path = Specta/Specta/SPTGlobalBeforeAfterEach.h; sourceTree = "<group>"; };
		20B68C9269B45825E82F4F5ECE0EB27C /* Expecta_Snapshots.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = Expecta_Snapshots.framework; path = "Expecta+Snapshots.framework"; sourceTree = BUILT_PRODUCTS_DIR; };
		211D588A9D3D15479209B49542F7F4EB /* EXPMatchers+raiseWithReason.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+raiseWithReason.h"; path = "Expecta/Matchers/EXPMatchers+raiseWithReason.h"; sourceTree = "<group>"; };
		212794D7A73C01B2BE55BE10930CE17A /* XCTestCase+Specta.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "XCTestCase+Specta.h"; path = "Specta/Specta/XCTestCase+Specta.h"; sourceTree = "<group>"; };
		23219833AF1C111856F8975D22831ECD /* EXPMatcher.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = EXPMatcher.h; path = Expecta/EXPMatcher.h; sourceTree = "<group>"; };
		264BACBF8E31580B57DC70B9E73A944E /* XCTest.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = XCTest.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.0.sdk/System/Library/Frameworks/XCTest.framework; sourceTree = DEVELOPER_DIR; };
		274871DD1E844FF2523BB9FCE73472F1 /* SPTTestSuite.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SPTTestSuite.h; path = Specta/Specta/SPTTestSuite.h; sourceTree = "<group>"; };
		294A242FE22C146084141E637C157592 /* Specta-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Specta-umbrella.h"; sourceTree = "<group>"; };
		29967CA281983865F76C58BDA798F7DE /* libRRXNNetwork.a */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = archive.ar; name = libRRXNNetwork.a; path = RRXNNetwork/Classes/libRRXNNetwork.a; sourceTree = "<group>"; };
		29D444D4612EFAFF94A50A389D3F5173 /* EXPMatchers+beCloseTo.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+beCloseTo.h"; path = "Expecta/Matchers/EXPMatchers+beCloseTo.h"; sourceTree = "<group>"; };
		2A5DF5119E0065B4D021C2A91582E9F3 /* EXPMatchers+beInTheRangeOf.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+beInTheRangeOf.m"; path = "Expecta/Matchers/EXPMatchers+beInTheRangeOf.m"; sourceTree = "<group>"; };
		2A8247D1E638B4D88C9C23DBE09F2ABC /* Pods-RRXNNetwork_Example.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-RRXNNetwork_Example.release.xcconfig"; sourceTree = "<group>"; };
		2AD6710C63F6E766E5112AB8ACFAF0C7 /* EXPMatchers+match.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+match.m"; path = "Expecta/Matchers/EXPMatchers+match.m"; sourceTree = "<group>"; };
		2B276B0A79173A1D6E83C9B4FB9A4A57 /* MJExtension.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = MJExtension.framework; path = MJExtension.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		2B91EBE018915AC5893E7664DF7D73BE /* EXPMatchers+beLessThanOrEqualTo.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+beLessThanOrEqualTo.m"; path = "Expecta/Matchers/EXPMatchers+beLessThanOrEqualTo.m"; sourceTree = "<group>"; };
		2BDD457D5FEDFB4E52AA67296865F6B2 /* SpectaUtility.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SpectaUtility.m; path = Specta/Specta/SpectaUtility.m; sourceTree = "<group>"; };
		2C6CEE05F9FE06AF8F93CF8E6D2B252F /* EXPMatchers+endWith.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+endWith.h"; path = "Expecta/Matchers/EXPMatchers+endWith.h"; sourceTree = "<group>"; };
		2C8B863FB4605DE883491B7AD5B2F23A /* EXPMatchers+beginWith.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+beginWith.m"; path = "Expecta/Matchers/EXPMatchers+beginWith.m"; sourceTree = "<group>"; };
		2C9E2C816EA54CEED9D2BE834C78784F /* EXPMatchers+beSupersetOf.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+beSupersetOf.h"; path = "Expecta/Matchers/EXPMatchers+beSupersetOf.h"; sourceTree = "<group>"; };
		2D2155790E45F071C8893E3CAF573BD9 /* SPTCallSite.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SPTCallSite.h; path = Specta/Specta/SPTCallSite.h; sourceTree = "<group>"; };
		2FB71647BF31584DB75C2AD6212C96AC /* NSObject+MJCoding.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSObject+MJCoding.h"; path = "MJExtension/NSObject+MJCoding.h"; sourceTree = "<group>"; };
		30254E1B137E2ED6644C6C0CAC0013B0 /* EXPMatchers+beGreaterThanOrEqualTo.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+beGreaterThanOrEqualTo.h"; path = "Expecta/Matchers/EXPMatchers+beGreaterThanOrEqualTo.h"; sourceTree = "<group>"; };
		3136DFC41B471B914D1BAC52A9C902D0 /* MJExtension-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "MJExtension-dummy.m"; sourceTree = "<group>"; };
		31B427E04B6036ED2404CDE9B088D7BE /* NSObject+MJClass.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSObject+MJClass.h"; path = "MJExtension/NSObject+MJClass.h"; sourceTree = "<group>"; };
		32E8133C1375E4A872337CFAEC020A39 /* EXPMatchers+beFalsy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+beFalsy.m"; path = "Expecta/Matchers/EXPMatchers+beFalsy.m"; sourceTree = "<group>"; };
		334651540DDF81475209AB218B269345 /* MJProperty.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJProperty.h; path = MJExtension/MJProperty.h; sourceTree = "<group>"; };
		368784BEFB46BE221C570440A1BA20A2 /* ExpectaSupport.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ExpectaSupport.h; path = Expecta/ExpectaSupport.h; sourceTree = "<group>"; };
		37917FF47AC9E5A0F3E31DFFFEAF18DB /* EXPMatchers+conformTo.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+conformTo.h"; path = "Expecta/Matchers/EXPMatchers+conformTo.h"; sourceTree = "<group>"; };
		386610DE489D9E6B673E2D00D0936D99 /* MJExtension-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "MJExtension-umbrella.h"; sourceTree = "<group>"; };
		39C56C939D311409F5101A635FBCED9D /* XCTestCase+Specta.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "XCTestCase+Specta.m"; path = "Specta/Specta/XCTestCase+Specta.m"; sourceTree = "<group>"; };
		3B1067DB6C8A9D3DDD83223CA265454F /* FBSnapshotTestCase.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FBSnapshotTestCase.m; path = FBSnapshotTestCase/FBSnapshotTestCase.m; sourceTree = "<group>"; };
		3B264869160F6204DB704C92D4E45ADD /* EXPMatchers+beKindOf.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+beKindOf.h"; path = "Expecta/Matchers/EXPMatchers+beKindOf.h"; sourceTree = "<group>"; };
		3B8A95D30219FB9207A9CEB98E49283E /* Specta.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Specta.debug.xcconfig; sourceTree = "<group>"; };
		3D8092219D1A603A113D45B96BB428C4 /* EXPMatchers+beNil.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+beNil.h"; path = "Expecta/Matchers/EXPMatchers+beNil.h"; sourceTree = "<group>"; };
		3F054BA0A9243BC9BF84E14F820AE28E /* UIImage+Snapshot.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+Snapshot.m"; path = "FBSnapshotTestCase/Categories/UIImage+Snapshot.m"; sourceTree = "<group>"; };
		4006B2D9C60DA9AACCDAB092A0AB899C /* Pods_RRXNNetwork_Example.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = Pods_RRXNNetwork_Example.framework; path = "Pods-RRXNNetwork_Example.framework"; sourceTree = BUILT_PRODUCTS_DIR; };
		402A2673376D8C5B96DA384361EF5486 /* SwiftSupport.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SwiftSupport.swift; path = FBSnapshotTestCase/SwiftSupport.swift; sourceTree = "<group>"; };
		4137DDCE24A99B879F4B6136CDD8A92C /* NSObject+MJKeyValue.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSObject+MJKeyValue.m"; path = "MJExtension/NSObject+MJKeyValue.m"; sourceTree = "<group>"; };
		419662C188F678151E58A4D4CEE9F1E2 /* NSObject+MJKeyValue.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSObject+MJKeyValue.h"; path = "MJExtension/NSObject+MJKeyValue.h"; sourceTree = "<group>"; };
		41DA50AC2939028B8CEA80599DB1E1EF /* EXPMatchers+beginWith.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+beginWith.h"; path = "Expecta/Matchers/EXPMatchers+beginWith.h"; sourceTree = "<group>"; };
		4207BEF6E1A7A56893CA9D7E43AAC21B /* EXPMatchers+FBSnapshotTest.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "EXPMatchers+FBSnapshotTest.m"; sourceTree = "<group>"; };
		431A32406DCF7180C1321BCFFAC70B28 /* SPTExample.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SPTExample.m; path = Specta/Specta/SPTExample.m; sourceTree = "<group>"; };
		43C7DAEED4A26D0C3078E2BA1B83B1BA /* EXPMatchers+beIdenticalTo.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+beIdenticalTo.h"; path = "Expecta/Matchers/EXPMatchers+beIdenticalTo.h"; sourceTree = "<group>"; };
		455AC5279955ABC2065FBBF2E3111664 /* EXPMatchers+postNotification.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+postNotification.h"; path = "Expecta/Matchers/EXPMatchers+postNotification.h"; sourceTree = "<group>"; };
		47A46A19691546953C5903961C3E1388 /* Expecta.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Expecta.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		4954EF1FA93DB1C8247D9B4E5017290C /* EXPMatchers+endWith.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+endWith.m"; path = "Expecta/Matchers/EXPMatchers+endWith.m"; sourceTree = "<group>"; };
		49EA7E87FDF87EB5362D1E5121884E7B /* UIApplication+StrictKeyWindow.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIApplication+StrictKeyWindow.m"; path = "FBSnapshotTestCase/Categories/UIApplication+StrictKeyWindow.m"; sourceTree = "<group>"; };
		49FEB6D42274C6EDD572165950C20F56 /* EXPMatchers+respondTo.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+respondTo.h"; path = "Expecta/Matchers/EXPMatchers+respondTo.h"; sourceTree = "<group>"; };
		4A10012CDEF18B2D00F3DAED085A1CD6 /* EXPMatchers+beCloseTo.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+beCloseTo.m"; path = "Expecta/Matchers/EXPMatchers+beCloseTo.m"; sourceTree = "<group>"; };
		4A330379CAB29C00FEA5BBD48341C045 /* FBSnapshotTestCase.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = FBSnapshotTestCase.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		4AEB4843988BFC2C608B9C8919888641 /* EXPMatchers+beTruthy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+beTruthy.m"; path = "Expecta/Matchers/EXPMatchers+beTruthy.m"; sourceTree = "<group>"; };
		4CE0D22C1421BE79643D46D170D35689 /* EXPMatchers+beTruthy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+beTruthy.h"; path = "Expecta/Matchers/EXPMatchers+beTruthy.h"; sourceTree = "<group>"; };
		4D1B0D3E57843600BA627CA7E43E3AA9 /* Expecta.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = Expecta.h; path = Expecta/Expecta.h; sourceTree = "<group>"; };
		4D4DCBEB5FA53C63CD1C7924BFC72248 /* Specta-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Specta-dummy.m"; sourceTree = "<group>"; };
		4D6310B7A96B3658889C1A8A3B1C9AA3 /* UIApplication+StrictKeyWindow.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIApplication+StrictKeyWindow.h"; path = "FBSnapshotTestCase/Categories/UIApplication+StrictKeyWindow.h"; sourceTree = "<group>"; };
		4F002D82B12256B14124529BE432FA62 /* Pods-RRXNNetwork_Example-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-RRXNNetwork_Example-Info.plist"; sourceTree = "<group>"; };
		4F257917261454C6F65E113AB2DA83A4 /* Pods-RRXNNetwork_Example-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-RRXNNetwork_Example-acknowledgements.plist"; sourceTree = "<group>"; };
		4F47101339D7DAC052A6468FD3D93E33 /* SPTSharedExampleGroups.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SPTSharedExampleGroups.h; path = Specta/Specta/SPTSharedExampleGroups.h; sourceTree = "<group>"; };
		4FC668DC53B958DA7BC78FC3A8ADF375 /* EXPMatchers.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = EXPMatchers.h; path = Expecta/Matchers/EXPMatchers.h; sourceTree = "<group>"; };
		502989E6789122B273CD537861110F78 /* EXPFloatTuple.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = EXPFloatTuple.h; path = Expecta/EXPFloatTuple.h; sourceTree = "<group>"; };
		55148F07A3CCF3189A0FE587174DB24D /* SPTSpec.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SPTSpec.m; path = Specta/Specta/SPTSpec.m; sourceTree = "<group>"; };
		55A1A7755805EF535958531DDC9AF1BD /* SpectaDSL.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SpectaDSL.m; path = Specta/Specta/SpectaDSL.m; sourceTree = "<group>"; };
		55E2B922E82C59540A02DF9D77901AA1 /* SpectaDSL.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SpectaDSL.h; path = Specta/Specta/SpectaDSL.h; sourceTree = "<group>"; };
		59876E021357905508D9A38EE03701B9 /* NSString+MJExtension.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSString+MJExtension.m"; path = "MJExtension/NSString+MJExtension.m"; sourceTree = "<group>"; };
		5C4F31330DFA99D699E4BDC8C3573D73 /* FBSnapshotTestCase.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = FBSnapshotTestCase.framework; path = FBSnapshotTestCase.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		5C9611D112094F627DC674556E32314E /* EXPMatchers+beLessThanOrEqualTo.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+beLessThanOrEqualTo.h"; path = "Expecta/Matchers/EXPMatchers+beLessThanOrEqualTo.h"; sourceTree = "<group>"; };
		5D8999290497BEA07403FE4F0A2969B8 /* EXPMatchers+beNil.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+beNil.m"; path = "Expecta/Matchers/EXPMatchers+beNil.m"; sourceTree = "<group>"; };
		5D8F0C127E4BBCF78962592FB946C801 /* EXPExpect.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = EXPExpect.m; path = Expecta/EXPExpect.m; sourceTree = "<group>"; };
		5DF53864163311D68B642E1BA39355AE /* NSValue+Expecta.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSValue+Expecta.m"; path = "Expecta/NSValue+Expecta.m"; sourceTree = "<group>"; };
		601BEE6CD013420E87C8A1A13E06D406 /* ExpectaObject+FBSnapshotTest.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "ExpectaObject+FBSnapshotTest.h"; sourceTree = "<group>"; };
		611347B410CCB816184E3E0E4886BCE9 /* EXPMatchers+beSupersetOf.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+beSupersetOf.m"; path = "Expecta/Matchers/EXPMatchers+beSupersetOf.m"; sourceTree = "<group>"; };
		618B79E532D687B28CCBB20EDF93F806 /* Expecta+Snapshots-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Expecta+Snapshots-dummy.m"; sourceTree = "<group>"; };
		622D44842BCF1066557675DA74D84E77 /* EXPMatcherHelpers.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = EXPMatcherHelpers.h; path = Expecta/Matchers/EXPMatcherHelpers.h; sourceTree = "<group>"; };
		6259D0E1511B4BB6CCA4126CD2CF6DEC /* MJPropertyKey.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJPropertyKey.h; path = MJExtension/MJPropertyKey.h; sourceTree = "<group>"; };
		62ED963B1CD4FEC6A33B440EE6BEF5B9 /* NSValue+Expecta.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSValue+Expecta.h"; path = "Expecta/NSValue+Expecta.h"; sourceTree = "<group>"; };
		65678EBF223B84DBAF92801A83F6F8B3 /* UIImage+Diff.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+Diff.m"; path = "FBSnapshotTestCase/Categories/UIImage+Diff.m"; sourceTree = "<group>"; };
		671437566B7A8A198942147B2DF39543 /* RRXNNetwork.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = RRXNNetwork.release.xcconfig; sourceTree = "<group>"; };
		6867F62D06A185737C966370974491E5 /* Specta.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Specta.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		6A1F40A04EDE5FD6D04DF5A5BA054627 /* NSObject+MJProperty.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSObject+MJProperty.m"; path = "MJExtension/NSObject+MJProperty.m"; sourceTree = "<group>"; };
		6F59FBE37C815536ABB90249D7231F9B /* EXPMatchers+haveCountOf.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+haveCountOf.h"; path = "Expecta/Matchers/EXPMatchers+haveCountOf.h"; sourceTree = "<group>"; };
		7497DFCC302C671A7B61B118BB024923 /* Pods-RRXNNetwork_Tests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-RRXNNetwork_Tests.debug.xcconfig"; sourceTree = "<group>"; };
		74B9B16385F7011FD061861BE966435F /* SpectaTypes.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SpectaTypes.h; path = Specta/Specta/SpectaTypes.h; sourceTree = "<group>"; };
		760B1D5DA12EEDF969805FF0B3D4297E /* Expecta.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = Expecta.modulemap; sourceTree = "<group>"; };
		76624D5BA62353EB67AE8738A0C7E9F8 /* EXPFloatTuple.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = EXPFloatTuple.m; path = Expecta/EXPFloatTuple.m; sourceTree = "<group>"; };
		772A7558994548E0D163E8B1768A22D3 /* FBSnapshotTestCase-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "FBSnapshotTestCase-umbrella.h"; sourceTree = "<group>"; };
		7733E87F3638AEBFB768DC9D1BFBFA81 /* Specta.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Specta.release.xcconfig; sourceTree = "<group>"; };
		7897438240A4B99F78C7801FDE0A4675 /* FBSnapshotTestCasePlatform.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FBSnapshotTestCasePlatform.m; path = FBSnapshotTestCase/FBSnapshotTestCasePlatform.m; sourceTree = "<group>"; };
		790EE0A342A68982BB7057E7702329F9 /* NSObject+MJClass.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSObject+MJClass.m"; path = "MJExtension/NSObject+MJClass.m"; sourceTree = "<group>"; };
		7915A784D2E660A7BB7C7DC4D4C4B69D /* MJFoundation.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJFoundation.m; path = MJExtension/MJFoundation.m; sourceTree = "<group>"; };
		79D936ABC02A3D14848CBC3266356835 /* ExpectaObject.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = ExpectaObject.m; path = Expecta/ExpectaObject.m; sourceTree = "<group>"; };
		79F54D243C8437938281AD840EF379A4 /* EXPMatchers+postNotification.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+postNotification.m"; path = "Expecta/Matchers/EXPMatchers+postNotification.m"; sourceTree = "<group>"; };
		7BEAF51D4F0B8F5E6FED6630D89A9B50 /* MJExtension-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "MJExtension-Info.plist"; sourceTree = "<group>"; };
		7C48E54E864BC0C91C7C014FD2F2F7CF /* EXPDefines.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = EXPDefines.h; path = Expecta/EXPDefines.h; sourceTree = "<group>"; };
		7C5C955AD707E98A05704CEBDF2B001E /* UIImage+Compare.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+Compare.h"; path = "FBSnapshotTestCase/Categories/UIImage+Compare.h"; sourceTree = "<group>"; };
		7E2CD51A7DE4DBBFF42C8C7FC784B874 /* FBSnapshotTestCase-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "FBSnapshotTestCase-dummy.m"; sourceTree = "<group>"; };
		7EF3FAA9224F8A8229C44844741467DC /* EXPDoubleTuple.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = EXPDoubleTuple.m; path = Expecta/EXPDoubleTuple.m; sourceTree = "<group>"; };
		7F13AE7BE9A65D76B00177E02A0D438A /* EXPExpect.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = EXPExpect.h; path = Expecta/EXPExpect.h; sourceTree = "<group>"; };
		80BF039FD3648587C5C1183C7973D0FD /* EXPMatcherHelpers.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = EXPMatcherHelpers.m; path = Expecta/Matchers/EXPMatcherHelpers.m; sourceTree = "<group>"; };
		81F5733C6B17B7D42263FE8ABC147497 /* Pods-RRXNNetwork_Example-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-RRXNNetwork_Example-umbrella.h"; sourceTree = "<group>"; };
		825B4D9B935A5132DF0BC67A8BC1A599 /* Pods-RRXNNetwork_Example.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-RRXNNetwork_Example.modulemap"; sourceTree = "<group>"; };
		8547D62E2B32EFE850778FD8277DFAE2 /* Pods-RRXNNetwork_Tests-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-RRXNNetwork_Tests-umbrella.h"; sourceTree = "<group>"; };
		881F1109DD764A2172DAE18897CC9654 /* ExpectaSupport.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = ExpectaSupport.m; path = Expecta/ExpectaSupport.m; sourceTree = "<group>"; };
		891ED75679737218671A0645BFDAF90C /* EXPBlockDefinedMatcher.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = EXPBlockDefinedMatcher.m; path = Expecta/EXPBlockDefinedMatcher.m; sourceTree = "<group>"; };
		8986979A7DC636A325D78F94E99DDBE2 /* XCTest+Private.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "XCTest+Private.h"; path = "Specta/Specta/XCTest+Private.h"; sourceTree = "<group>"; };
		89DAABCCD5C00031E687F7D7289DDF72 /* UIImage+Compare.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImage+Compare.m"; path = "FBSnapshotTestCase/Categories/UIImage+Compare.m"; sourceTree = "<group>"; };
		8B73A651DC88E3BA3EEDCA4BBA4EC445 /* MJPropertyKey.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJPropertyKey.m; path = MJExtension/MJPropertyKey.m; sourceTree = "<group>"; };
		92AF96027F720C52EE67405BFC819F7A /* EXPMatchers+haveCountOf.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+haveCountOf.m"; path = "Expecta/Matchers/EXPMatchers+haveCountOf.m"; sourceTree = "<group>"; };
		936442D31F330A11B693D5907C86E4EA /* Expecta.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Expecta.debug.xcconfig; sourceTree = "<group>"; };
		95650DAAC4C08D04E84CCA5DD6D5078B /* Pods-RRXNNetwork_Tests-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-RRXNNetwork_Tests-acknowledgements.plist"; sourceTree = "<group>"; };
		96003F8C7A01676C3384A0A3A81C9D21 /* EXPMatchers+beFalsy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+beFalsy.h"; path = "Expecta/Matchers/EXPMatchers+beFalsy.h"; sourceTree = "<group>"; };
		969AEDC1AA3588046CDDBA95234B6BE2 /* EXPMatchers+beIdenticalTo.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+beIdenticalTo.m"; path = "Expecta/Matchers/EXPMatchers+beIdenticalTo.m"; sourceTree = "<group>"; };
		983975F382C85EA85B4F4169E49A72CE /* SPTExcludeGlobalBeforeAfterEach.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SPTExcludeGlobalBeforeAfterEach.h; path = Specta/Specta/SPTExcludeGlobalBeforeAfterEach.h; sourceTree = "<group>"; };
		98D37D597C20F78BCDE04426D7C648F5 /* SPTExample.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SPTExample.h; path = Specta/Specta/SPTExample.h; sourceTree = "<group>"; };
		9948276A8B9FABEC11ED2C7A978A2187 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.0.sdk/System/Library/Frameworks/UIKit.framework; sourceTree = DEVELOPER_DIR; };
		9A6898141C5F8EEC97ACFD67E1E054B7 /* Specta.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = Specta.h; path = Specta/Specta/Specta.h; sourceTree = "<group>"; };
		9BEAB5C3DF63DB81BA3958CDC196ED81 /* RRXNNetwork.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = RRXNNetwork.debug.xcconfig; sourceTree = "<group>"; };
		9C7FECDD0AC2DBF2B664B164A9B0C274 /* EXPUnsupportedObject.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = EXPUnsupportedObject.m; path = Expecta/EXPUnsupportedObject.m; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		9DE12AA56844865807C910E6A2DB67DC /* SPTCompiledExample.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SPTCompiledExample.m; path = Specta/Specta/SPTCompiledExample.m; sourceTree = "<group>"; };
		9E195CF6EC3CE2F10FE01161112613A9 /* EXPMatchers+match.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+match.h"; path = "Expecta/Matchers/EXPMatchers+match.h"; sourceTree = "<group>"; };
		9E5ED21AD446EF506FA61C5B1C442469 /* SPTCallSite.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SPTCallSite.m; path = Specta/Specta/SPTCallSite.m; sourceTree = "<group>"; };
		9F4AE9657AA253B4CC7CAE98DA1C8B05 /* ExpectaObject.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = ExpectaObject.h; path = Expecta/ExpectaObject.h; sourceTree = "<group>"; };
		A15DFDE56F9FD14F3F2F033433A91221 /* EXPMatchers+raiseWithReason.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+raiseWithReason.m"; path = "Expecta/Matchers/EXPMatchers+raiseWithReason.m"; sourceTree = "<group>"; };
		A164DD82225C0A23EA4B3BD6E3FE0AB2 /* README.md */ = {isa = PBXFileReference; includeInIndex = 1; path = README.md; sourceTree = "<group>"; };
		A33ADB8F10C23D6DC7566FE1D2FB05E9 /* ExpectaObject+FBSnapshotTest.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "ExpectaObject+FBSnapshotTest.m"; sourceTree = "<group>"; };
		A64CD2335CA6ED639AD2324D01F64F05 /* Pods-RRXNNetwork_Example.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-RRXNNetwork_Example.debug.xcconfig"; sourceTree = "<group>"; };
		A782737B791EBEB2970838B0FBCE5C64 /* Expecta+Snapshots-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Expecta+Snapshots-umbrella.h"; sourceTree = "<group>"; };
		A7E2BDBE39E9CFE315B13E75F19E07AE /* UIImage+Diff.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImage+Diff.h"; path = "FBSnapshotTestCase/Categories/UIImage+Diff.h"; sourceTree = "<group>"; };
		A8F18D79748C64DEE0B1C23EB39E28B9 /* Pods-RRXNNetwork_Example-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-RRXNNetwork_Example-dummy.m"; sourceTree = "<group>"; };
		B43C5FB7E24ADDE8FF6EB9E4656206B7 /* EXPMatchers+contain.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+contain.h"; path = "Expecta/Matchers/EXPMatchers+contain.h"; sourceTree = "<group>"; };
		B5E8936F710B1628D3CDBC8E627F04F2 /* MJExtensionConst.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJExtensionConst.h; path = MJExtension/MJExtensionConst.h; sourceTree = "<group>"; };
		B65B7D5E62F68F39011D7935CB00628B /* XNNetworkModel.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = XNNetworkModel.h; path = RRXNNetwork/Classes/XNNetworkModel.h; sourceTree = "<group>"; };
		B6CDEF756543437EADFE7C40DEC2E5B3 /* RRXNNetwork.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; path = RRXNNetwork.podspec; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		BA055E4BFD25A0EB27B34A60C217E649 /* Expecta.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Expecta.release.xcconfig; sourceTree = "<group>"; };
		BA6AE39182A5BF94E99130932C2054E6 /* EXPMatchers+raise.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+raise.h"; path = "Expecta/Matchers/EXPMatchers+raise.h"; sourceTree = "<group>"; };
		BC15168025F815F9FEEF2A78613859EE /* SPTExampleGroup.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = SPTExampleGroup.m; path = Specta/Specta/SPTExampleGroup.m; sourceTree = "<group>"; };
		BE85E66E07EB210EA1ACD44595CEF7DF /* MJPropertyType.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJPropertyType.m; path = MJExtension/MJPropertyType.m; sourceTree = "<group>"; };
		BFAC90CB0A0B5FCADAE3ADC551844E85 /* Specta-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Specta-prefix.pch"; sourceTree = "<group>"; };
		C1F2E0FD3E71C714E9A0278673E2B486 /* FBSnapshotTestController.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FBSnapshotTestController.m; path = FBSnapshotTestCase/FBSnapshotTestController.m; sourceTree = "<group>"; };
		C3EC45197A2D14923BD161E4F4990893 /* XNSDKNetworkModel.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = XNSDKNetworkModel.h; path = RRXNNetwork/Classes/XNSDKNetworkModel.h; sourceTree = "<group>"; };
		C56D1E3E73F0558B85D4A119ACB3A953 /* EXPMatchers+beGreaterThan.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+beGreaterThan.m"; path = "Expecta/Matchers/EXPMatchers+beGreaterThan.m"; sourceTree = "<group>"; };
		C839F3411D2296FA16451EAF9D223ED8 /* FBSnapshotTestCase.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = FBSnapshotTestCase.modulemap; sourceTree = "<group>"; };
		C8AE8CD4CB85FE34A5FD678F78D4DEB8 /* MJExtension-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "MJExtension-prefix.pch"; sourceTree = "<group>"; };
		C99C1D255FA392B89C170C89F77EF4CA /* EXPMatchers+equal.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+equal.h"; path = "Expecta/Matchers/EXPMatchers+equal.h"; sourceTree = "<group>"; };
		CA580C56ED4C80A1C254D02C0C982513 /* EXPMatchers+beGreaterThanOrEqualTo.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+beGreaterThanOrEqualTo.m"; path = "Expecta/Matchers/EXPMatchers+beGreaterThanOrEqualTo.m"; sourceTree = "<group>"; };
		CC9DCAE97C9D6094EABECFD3958E57E3 /* Expecta+Snapshots.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Expecta+Snapshots.modulemap"; sourceTree = "<group>"; };
		CF2A9C07FF2F529D95D33D74101BB22A /* NSObject+MJCoding.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "NSObject+MJCoding.m"; path = "MJExtension/NSObject+MJCoding.m"; sourceTree = "<group>"; };
		D025BD2B0986A8714625CDEEC11787C1 /* XNHttpRequest.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = XNHttpRequest.h; path = RRXNNetwork/Classes/XNHttpRequest.h; sourceTree = "<group>"; };
		D3B09CA29CA6FF74F27B112060C2F57B /* Pods-RRXNNetwork_Tests-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-RRXNNetwork_Tests-Info.plist"; sourceTree = "<group>"; };
		D4686DF0C3941D9F01AB9FEEB4F9649C /* MJExtensionConst.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJExtensionConst.m; path = MJExtension/MJExtensionConst.m; sourceTree = "<group>"; };
		D60C285E57263F5A3465E9DFFAE18CC6 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		D70D8599F0BF3E221E7ACAB6BF8550C9 /* EXPMatchers+beInTheRangeOf.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+beInTheRangeOf.h"; path = "Expecta/Matchers/EXPMatchers+beInTheRangeOf.h"; sourceTree = "<group>"; };
		D73ACE5D872599443EB28233EEBB224E /* SPTCompiledExample.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SPTCompiledExample.h; path = Specta/Specta/SPTCompiledExample.h; sourceTree = "<group>"; };
		D7F3C9A23A37CC31AEA639F1B67E8B17 /* EXPMatchers+FBSnapshotTest.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "EXPMatchers+FBSnapshotTest.h"; sourceTree = "<group>"; };
		D8412CD5B3B1711B0BD52FBF3B83451B /* EXPMatchers+respondTo.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+respondTo.m"; path = "Expecta/Matchers/EXPMatchers+respondTo.m"; sourceTree = "<group>"; };
		D848762F45FD6F8FDC89FD0C477176FF /* EXPMatchers+beGreaterThan.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+beGreaterThan.h"; path = "Expecta/Matchers/EXPMatchers+beGreaterThan.h"; sourceTree = "<group>"; };
		D9601B9938369E0B9FEB6055733C25EE /* Expecta+Snapshots.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Expecta+Snapshots.release.xcconfig"; sourceTree = "<group>"; };
		DC25EC7AF2A9861136C55449797A246E /* FBSnapshotTestController.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FBSnapshotTestController.h; path = FBSnapshotTestCase/FBSnapshotTestController.h; sourceTree = "<group>"; };
		DC669F3D73A900F252DA646BF63557CF /* MJExtension.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJExtension.h; path = MJExtension/MJExtension.h; sourceTree = "<group>"; };
		DFB54509BE077BC68194651FF6B93EF4 /* EXPDoubleTuple.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = EXPDoubleTuple.h; path = Expecta/EXPDoubleTuple.h; sourceTree = "<group>"; };
		E1DF883F7B22C901CCC6813193F842E8 /* Pods-RRXNNetwork_Tests-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-RRXNNetwork_Tests-dummy.m"; sourceTree = "<group>"; };
		E3327E8AD1C78F4E329C96B6CE7AC818 /* EXPBlockDefinedMatcher.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = EXPBlockDefinedMatcher.h; path = Expecta/EXPBlockDefinedMatcher.h; sourceTree = "<group>"; };
		E3B940133F9855985F1A95703684BC5F /* MJExtension.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = MJExtension.modulemap; sourceTree = "<group>"; };
		E5C7BFA922B49E10736ED1893E9759FB /* EXPMatchers+beInstanceOf.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+beInstanceOf.m"; path = "Expecta/Matchers/EXPMatchers+beInstanceOf.m"; sourceTree = "<group>"; };
		E65EC312B2A51B95CC3CCA8D3876E923 /* MJExtension.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = MJExtension.debug.xcconfig; sourceTree = "<group>"; };
		E6890AF535F843044E13B14D8CA6E81F /* Pods-RRXNNetwork_Tests-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-RRXNNetwork_Tests-frameworks.sh"; sourceTree = "<group>"; };
		E762F942774EA2871DECB3803278F4EE /* Pods-RRXNNetwork_Tests-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-RRXNNetwork_Tests-acknowledgements.markdown"; sourceTree = "<group>"; };
		E7F1041A2732068BD7222489683E42F5 /* MJFoundation.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJFoundation.h; path = MJExtension/MJFoundation.h; sourceTree = "<group>"; };
		E813254CCBCBE38CE3E20CC3A8A56A23 /* EXPMatchers+raise.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+raise.m"; path = "Expecta/Matchers/EXPMatchers+raise.m"; sourceTree = "<group>"; };
		E86E5AB1F1BD5EBAD7738359C4A97FA6 /* EXPMatchers+beSubclassOf.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "EXPMatchers+beSubclassOf.h"; path = "Expecta/Matchers/EXPMatchers+beSubclassOf.h"; sourceTree = "<group>"; };
		EAE3B877ABF534F2E865E08C864EAA46 /* FBSnapshotTestCase.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = FBSnapshotTestCase.release.xcconfig; sourceTree = "<group>"; };
		ED11E0546C1CDA4F85A0D614BFDB3217 /* FBSnapshotTestCase-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "FBSnapshotTestCase-Info.plist"; sourceTree = "<group>"; };
		EDD046E7522F87D76C9F979E490C2344 /* Pods-RRXNNetwork_Example-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-RRXNNetwork_Example-acknowledgements.markdown"; sourceTree = "<group>"; };
		EF453CCBAF78935DF7DD465E07642292 /* Pods-RRXNNetwork_Tests.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-RRXNNetwork_Tests.modulemap"; sourceTree = "<group>"; };
		F21E831B60DD880B6CC15B7EA09B9DB0 /* NSObject+MJProperty.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSObject+MJProperty.h"; path = "MJExtension/NSObject+MJProperty.h"; sourceTree = "<group>"; };
		F2988E82B1B862F659EF40EAC917BBB9 /* FBSnapshotTestCase.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = FBSnapshotTestCase.debug.xcconfig; sourceTree = "<group>"; };
		F3E2BB2DB6706CCB4367FBFFBD6D1176 /* Pods-RRXNNetwork_Tests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-RRXNNetwork_Tests.release.xcconfig"; sourceTree = "<group>"; };
		F62C4D95005825B7ED249D6378436945 /* Pods_RRXNNetwork_Tests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = Pods_RRXNNetwork_Tests.framework; path = "Pods-RRXNNetwork_Tests.framework"; sourceTree = BUILT_PRODUCTS_DIR; };
		F70501C0FB9F8131A5D7AD1534353377 /* Expecta-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Expecta-umbrella.h"; sourceTree = "<group>"; };
		F8DCA7A0EFF925F62ABFBEF2C385F3CA /* MJProperty.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MJProperty.m; path = MJExtension/MJProperty.m; sourceTree = "<group>"; };
		F9AC5F1248A1159E01F1C799EDA2A961 /* SPTExampleGroup.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = SPTExampleGroup.h; path = Specta/Specta/SPTExampleGroup.h; sourceTree = "<group>"; };
		FBB26F7A12B2E3089BBE83452DDAD08A /* FBSnapshotTestCasePlatform.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FBSnapshotTestCasePlatform.h; path = FBSnapshotTestCase/FBSnapshotTestCasePlatform.h; sourceTree = "<group>"; };
		FBBFB1E7F2D1C774CE9333F2AE2410E9 /* EXPMatchers+beSubclassOf.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "EXPMatchers+beSubclassOf.m"; path = "Expecta/Matchers/EXPMatchers+beSubclassOf.m"; sourceTree = "<group>"; };
		FD0E58C98AE5D9A1083E12EBF616A809 /* MJPropertyType.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MJPropertyType.h; path = MJExtension/MJPropertyType.h; sourceTree = "<group>"; };
		FE030903EC596BCD0B0F9117C1FDFDA5 /* Expecta+Snapshots-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Expecta+Snapshots-Info.plist"; sourceTree = "<group>"; };
		FE41DBBACACD729135DFC43B547E6E68 /* NSString+MJExtension.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "NSString+MJExtension.h"; path = "MJExtension/NSString+MJExtension.h"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		682792CCAB1324BA58826A2B6259E6BA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F4E585B2607D1B6F6FB3198A40888F5E /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6A2E4256BC18FACFD2180CA4C02A536F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C94EA6C4EA7695C4966E661E387C02FE /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7258024DA974B53FCC495CE6929F3D9D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AF493331C1B8B62E4FB770EE9C144E22 /* Expecta.framework in Frameworks */,
				EEC477D6B17E9AB60D60728946BA8D1D /* FBSnapshotTestCase.framework in Frameworks */,
				850E38ABD196F6844F7BCF89586C1A65 /* Foundation.framework in Frameworks */,
				C8D5363E37368C0CFE0DA66E1F5DD75D /* Specta.framework in Frameworks */,
				87602C676D877CE47FD5F9D1813EAA96 /* XCTest.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7312BAFD17174C2E85416CF941012159 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8E30B7DA0041E761CA840C538EC05152 /* Foundation.framework in Frameworks */,
				7D22935560DBA8F2EE62C3609A9EC3F1 /* XCTest.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BBD15B31B2C1B6A03B79F62467394B15 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D5030AF577970FF20BFA041AB9D7FA9F /* Foundation.framework in Frameworks */,
				8FA1B04566FB2C0BB648058E80B26F35 /* QuartzCore.framework in Frameworks */,
				460F529F2FDF0685EFA31047BAA5D628 /* UIKit.framework in Frameworks */,
				9CBBE07B6B6DD81BF799043AF0C2432B /* XCTest.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C2F33B6F846A9408CC9FBB1E0F9C9B9B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				313E7D422D5BD3F636611D3D66E2685E /* Foundation.framework in Frameworks */,
				C3576E9AC95E9412AF6CD051A4E8E55E /* XCTest.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E3DFB7F06EC80553131DB5B0993D681C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				464BE5F294EA7CC26AE101D0A24EFB3C /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		030DB05C81A99094FF0898DC6A5E0110 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				47A46A19691546953C5903961C3E1388 /* Expecta.framework */,
				4A330379CAB29C00FEA5BBD48341C045 /* FBSnapshotTestCase.framework */,
				6867F62D06A185737C966370974491E5 /* Specta.framework */,
				DF7370F0E9F831B310F9839BA03A7AF1 /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		0D5AE18A0B06CD14B01452FA1D88E76C /* Support Files */ = {
			isa = PBXGroup;
			children = (
				C839F3411D2296FA16451EAF9D223ED8 /* FBSnapshotTestCase.modulemap */,
				7E2CD51A7DE4DBBFF42C8C7FC784B874 /* FBSnapshotTestCase-dummy.m */,
				ED11E0546C1CDA4F85A0D614BFDB3217 /* FBSnapshotTestCase-Info.plist */,
				1B5421659DA42C2538665DE74BEFA0B6 /* FBSnapshotTestCase-prefix.pch */,
				772A7558994548E0D163E8B1768A22D3 /* FBSnapshotTestCase-umbrella.h */,
				F2988E82B1B862F659EF40EAC917BBB9 /* FBSnapshotTestCase.debug.xcconfig */,
				EAE3B877ABF534F2E865E08C864EAA46 /* FBSnapshotTestCase.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/FBSnapshotTestCase";
			sourceTree = "<group>";
		};
		13387351E100B33CBEBC662C5E4DC91A /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				6347B0661ED028904860E86DCD641C40 /* Pods-RRXNNetwork_Example */,
				2BE96D43588951A76A53FBAB8D531E2C /* Pods-RRXNNetwork_Tests */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		1BEAF35AB9F833061FE589BD8D63B9B5 /* Pod */ = {
			isa = PBXGroup;
			children = (
				1D958A54911535F8F383FDCABCDBE29D /* LICENSE */,
				A164DD82225C0A23EA4B3BD6E3FE0AB2 /* README.md */,
				B6CDEF756543437EADFE7C40DEC2E5B3 /* RRXNNetwork.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		2BE96D43588951A76A53FBAB8D531E2C /* Pods-RRXNNetwork_Tests */ = {
			isa = PBXGroup;
			children = (
				EF453CCBAF78935DF7DD465E07642292 /* Pods-RRXNNetwork_Tests.modulemap */,
				E762F942774EA2871DECB3803278F4EE /* Pods-RRXNNetwork_Tests-acknowledgements.markdown */,
				95650DAAC4C08D04E84CCA5DD6D5078B /* Pods-RRXNNetwork_Tests-acknowledgements.plist */,
				E1DF883F7B22C901CCC6813193F842E8 /* Pods-RRXNNetwork_Tests-dummy.m */,
				E6890AF535F843044E13B14D8CA6E81F /* Pods-RRXNNetwork_Tests-frameworks.sh */,
				D3B09CA29CA6FF74F27B112060C2F57B /* Pods-RRXNNetwork_Tests-Info.plist */,
				8547D62E2B32EFE850778FD8277DFAE2 /* Pods-RRXNNetwork_Tests-umbrella.h */,
				7497DFCC302C671A7B61B118BB024923 /* Pods-RRXNNetwork_Tests.debug.xcconfig */,
				F3E2BB2DB6706CCB4367FBFFBD6D1176 /* Pods-RRXNNetwork_Tests.release.xcconfig */,
			);
			name = "Pods-RRXNNetwork_Tests";
			path = "Target Support Files/Pods-RRXNNetwork_Tests";
			sourceTree = "<group>";
		};
		3961E9C686129C0012CA32BC01EB36B0 /* Expecta */ = {
			isa = PBXGroup;
			children = (
				E3327E8AD1C78F4E329C96B6CE7AC818 /* EXPBlockDefinedMatcher.h */,
				891ED75679737218671A0645BFDAF90C /* EXPBlockDefinedMatcher.m */,
				7C48E54E864BC0C91C7C014FD2F2F7CF /* EXPDefines.h */,
				DFB54509BE077BC68194651FF6B93EF4 /* EXPDoubleTuple.h */,
				7EF3FAA9224F8A8229C44844741467DC /* EXPDoubleTuple.m */,
				4D1B0D3E57843600BA627CA7E43E3AA9 /* Expecta.h */,
				9F4AE9657AA253B4CC7CAE98DA1C8B05 /* ExpectaObject.h */,
				79D936ABC02A3D14848CBC3266356835 /* ExpectaObject.m */,
				368784BEFB46BE221C570440A1BA20A2 /* ExpectaSupport.h */,
				881F1109DD764A2172DAE18897CC9654 /* ExpectaSupport.m */,
				7F13AE7BE9A65D76B00177E02A0D438A /* EXPExpect.h */,
				5D8F0C127E4BBCF78962592FB946C801 /* EXPExpect.m */,
				502989E6789122B273CD537861110F78 /* EXPFloatTuple.h */,
				76624D5BA62353EB67AE8738A0C7E9F8 /* EXPFloatTuple.m */,
				23219833AF1C111856F8975D22831ECD /* EXPMatcher.h */,
				622D44842BCF1066557675DA74D84E77 /* EXPMatcherHelpers.h */,
				80BF039FD3648587C5C1183C7973D0FD /* EXPMatcherHelpers.m */,
				4FC668DC53B958DA7BC78FC3A8ADF375 /* EXPMatchers.h */,
				29D444D4612EFAFF94A50A389D3F5173 /* EXPMatchers+beCloseTo.h */,
				4A10012CDEF18B2D00F3DAED085A1CD6 /* EXPMatchers+beCloseTo.m */,
				96003F8C7A01676C3384A0A3A81C9D21 /* EXPMatchers+beFalsy.h */,
				32E8133C1375E4A872337CFAEC020A39 /* EXPMatchers+beFalsy.m */,
				41DA50AC2939028B8CEA80599DB1E1EF /* EXPMatchers+beginWith.h */,
				2C8B863FB4605DE883491B7AD5B2F23A /* EXPMatchers+beginWith.m */,
				D848762F45FD6F8FDC89FD0C477176FF /* EXPMatchers+beGreaterThan.h */,
				C56D1E3E73F0558B85D4A119ACB3A953 /* EXPMatchers+beGreaterThan.m */,
				30254E1B137E2ED6644C6C0CAC0013B0 /* EXPMatchers+beGreaterThanOrEqualTo.h */,
				CA580C56ED4C80A1C254D02C0C982513 /* EXPMatchers+beGreaterThanOrEqualTo.m */,
				43C7DAEED4A26D0C3078E2BA1B83B1BA /* EXPMatchers+beIdenticalTo.h */,
				969AEDC1AA3588046CDDBA95234B6BE2 /* EXPMatchers+beIdenticalTo.m */,
				0467EAA73FA5BB16ED77C7C52778426C /* EXPMatchers+beInstanceOf.h */,
				E5C7BFA922B49E10736ED1893E9759FB /* EXPMatchers+beInstanceOf.m */,
				D70D8599F0BF3E221E7ACAB6BF8550C9 /* EXPMatchers+beInTheRangeOf.h */,
				2A5DF5119E0065B4D021C2A91582E9F3 /* EXPMatchers+beInTheRangeOf.m */,
				3B264869160F6204DB704C92D4E45ADD /* EXPMatchers+beKindOf.h */,
				0B7771453228448A78CA58C11A19D31B /* EXPMatchers+beKindOf.m */,
				12900C7322B3E85024F5F93519B50068 /* EXPMatchers+beLessThan.h */,
				16AFD8BC14616D7DCB84913F08038101 /* EXPMatchers+beLessThan.m */,
				5C9611D112094F627DC674556E32314E /* EXPMatchers+beLessThanOrEqualTo.h */,
				2B91EBE018915AC5893E7664DF7D73BE /* EXPMatchers+beLessThanOrEqualTo.m */,
				3D8092219D1A603A113D45B96BB428C4 /* EXPMatchers+beNil.h */,
				5D8999290497BEA07403FE4F0A2969B8 /* EXPMatchers+beNil.m */,
				E86E5AB1F1BD5EBAD7738359C4A97FA6 /* EXPMatchers+beSubclassOf.h */,
				FBBFB1E7F2D1C774CE9333F2AE2410E9 /* EXPMatchers+beSubclassOf.m */,
				2C9E2C816EA54CEED9D2BE834C78784F /* EXPMatchers+beSupersetOf.h */,
				611347B410CCB816184E3E0E4886BCE9 /* EXPMatchers+beSupersetOf.m */,
				4CE0D22C1421BE79643D46D170D35689 /* EXPMatchers+beTruthy.h */,
				4AEB4843988BFC2C608B9C8919888641 /* EXPMatchers+beTruthy.m */,
				37917FF47AC9E5A0F3E31DFFFEAF18DB /* EXPMatchers+conformTo.h */,
				1D7AD6BB9802C647F6046ADA2DDA180A /* EXPMatchers+conformTo.m */,
				B43C5FB7E24ADDE8FF6EB9E4656206B7 /* EXPMatchers+contain.h */,
				072CC0B14503CE31B8875E17FFA70071 /* EXPMatchers+contain.m */,
				2C6CEE05F9FE06AF8F93CF8E6D2B252F /* EXPMatchers+endWith.h */,
				4954EF1FA93DB1C8247D9B4E5017290C /* EXPMatchers+endWith.m */,
				C99C1D255FA392B89C170C89F77EF4CA /* EXPMatchers+equal.h */,
				1E609CE69D35EF1788F2E2DB64CDB112 /* EXPMatchers+equal.m */,
				6F59FBE37C815536ABB90249D7231F9B /* EXPMatchers+haveCountOf.h */,
				92AF96027F720C52EE67405BFC819F7A /* EXPMatchers+haveCountOf.m */,
				9E195CF6EC3CE2F10FE01161112613A9 /* EXPMatchers+match.h */,
				2AD6710C63F6E766E5112AB8ACFAF0C7 /* EXPMatchers+match.m */,
				455AC5279955ABC2065FBBF2E3111664 /* EXPMatchers+postNotification.h */,
				79F54D243C8437938281AD840EF379A4 /* EXPMatchers+postNotification.m */,
				BA6AE39182A5BF94E99130932C2054E6 /* EXPMatchers+raise.h */,
				E813254CCBCBE38CE3E20CC3A8A56A23 /* EXPMatchers+raise.m */,
				211D588A9D3D15479209B49542F7F4EB /* EXPMatchers+raiseWithReason.h */,
				A15DFDE56F9FD14F3F2F033433A91221 /* EXPMatchers+raiseWithReason.m */,
				49FEB6D42274C6EDD572165950C20F56 /* EXPMatchers+respondTo.h */,
				D8412CD5B3B1711B0BD52FBF3B83451B /* EXPMatchers+respondTo.m */,
				01AEC93B1B10F81FCEFBC47535542C1D /* EXPUnsupportedObject.h */,
				9C7FECDD0AC2DBF2B664B164A9B0C274 /* EXPUnsupportedObject.m */,
				1DE2C277B1EF85D0F6A415CE4CE4B873 /* NSObject+Expecta.h */,
				62ED963B1CD4FEC6A33B440EE6BEF5B9 /* NSValue+Expecta.h */,
				5DF53864163311D68B642E1BA39355AE /* NSValue+Expecta.m */,
				5F183FBD511B41F7B7810657F9E2D236 /* Support Files */,
			);
			name = Expecta;
			path = Expecta;
			sourceTree = "<group>";
		};
		3D7E9027AE39C0742D4222FB3CC5A2E0 /* Products */ = {
			isa = PBXGroup;
			children = (
				08F7F0770B4878B9883B87DCD8569CB4 /* Expecta.framework */,
				20B68C9269B45825E82F4F5ECE0EB27C /* Expecta_Snapshots.framework */,
				5C4F31330DFA99D699E4BDC8C3573D73 /* FBSnapshotTestCase.framework */,
				2B276B0A79173A1D6E83C9B4FB9A4A57 /* MJExtension.framework */,
				4006B2D9C60DA9AACCDAB092A0AB899C /* Pods_RRXNNetwork_Example.framework */,
				F62C4D95005825B7ED249D6378436945 /* Pods_RRXNNetwork_Tests.framework */,
				15B13B063AA97C48C9010C298AECBDDA /* Specta.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		3F734280FD5F0246B14D7AD82BAC2609 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				E3B940133F9855985F1A95703684BC5F /* MJExtension.modulemap */,
				3136DFC41B471B914D1BAC52A9C902D0 /* MJExtension-dummy.m */,
				7BEAF51D4F0B8F5E6FED6630D89A9B50 /* MJExtension-Info.plist */,
				C8AE8CD4CB85FE34A5FD678F78D4DEB8 /* MJExtension-prefix.pch */,
				386610DE489D9E6B673E2D00D0936D99 /* MJExtension-umbrella.h */,
				E65EC312B2A51B95CC3CCA8D3876E923 /* MJExtension.debug.xcconfig */,
				0E94121B11103286987A23F57F9390BF /* MJExtension.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/MJExtension";
			sourceTree = "<group>";
		};
		407541CE4DEDBBE15DA333E9742ED180 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				9BEAB5C3DF63DB81BA3958CDC196ED81 /* RRXNNetwork.debug.xcconfig */,
				671437566B7A8A198942147B2DF39543 /* RRXNNetwork.release.xcconfig */,
			);
			name = "Support Files";
			path = "Example/Pods/Target Support Files/RRXNNetwork";
			sourceTree = "<group>";
		};
		4C84C2C5BC1605A4201F1A6A7DF82F44 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				07B52F27CB1E9D0F96D7BDC25FD0F2B5 /* Specta.modulemap */,
				4D4DCBEB5FA53C63CD1C7924BFC72248 /* Specta-dummy.m */,
				02CF983B37B50F1FDA2D6F1A295626F2 /* Specta-Info.plist */,
				BFAC90CB0A0B5FCADAE3ADC551844E85 /* Specta-prefix.pch */,
				294A242FE22C146084141E637C157592 /* Specta-umbrella.h */,
				3B8A95D30219FB9207A9CEB98E49283E /* Specta.debug.xcconfig */,
				7733E87F3638AEBFB768DC9D1BFBFA81 /* Specta.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/Specta";
			sourceTree = "<group>";
		};
		525CEEDA2B91A1F7AB14E09309826A94 /* Expecta+Snapshots */ = {
			isa = PBXGroup;
			children = (
				601BEE6CD013420E87C8A1A13E06D406 /* ExpectaObject+FBSnapshotTest.h */,
				A33ADB8F10C23D6DC7566FE1D2FB05E9 /* ExpectaObject+FBSnapshotTest.m */,
				D7F3C9A23A37CC31AEA639F1B67E8B17 /* EXPMatchers+FBSnapshotTest.h */,
				4207BEF6E1A7A56893CA9D7E43AAC21B /* EXPMatchers+FBSnapshotTest.m */,
				90F06A9AB3C5E0F9379A0476551ABD50 /* Support Files */,
			);
			name = "Expecta+Snapshots";
			path = "Expecta+Snapshots";
			sourceTree = "<group>";
		};
		5F183FBD511B41F7B7810657F9E2D236 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				760B1D5DA12EEDF969805FF0B3D4297E /* Expecta.modulemap */,
				07113274D27DE2F2912B7899EAF4DCB6 /* Expecta-dummy.m */,
				0C7496B2DA4E69A0C6FFC62FD18A94C3 /* Expecta-Info.plist */,
				1C6F807A2A4DFB9995242A15DBFBB5F9 /* Expecta-prefix.pch */,
				F70501C0FB9F8131A5D7AD1534353377 /* Expecta-umbrella.h */,
				936442D31F330A11B693D5907C86E4EA /* Expecta.debug.xcconfig */,
				BA055E4BFD25A0EB27B34A60C217E649 /* Expecta.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/Expecta";
			sourceTree = "<group>";
		};
		6347B0661ED028904860E86DCD641C40 /* Pods-RRXNNetwork_Example */ = {
			isa = PBXGroup;
			children = (
				825B4D9B935A5132DF0BC67A8BC1A599 /* Pods-RRXNNetwork_Example.modulemap */,
				EDD046E7522F87D76C9F979E490C2344 /* Pods-RRXNNetwork_Example-acknowledgements.markdown */,
				4F257917261454C6F65E113AB2DA83A4 /* Pods-RRXNNetwork_Example-acknowledgements.plist */,
				A8F18D79748C64DEE0B1C23EB39E28B9 /* Pods-RRXNNetwork_Example-dummy.m */,
				021FE5C7B055283BF4F548655823EAB5 /* Pods-RRXNNetwork_Example-frameworks.sh */,
				4F002D82B12256B14124529BE432FA62 /* Pods-RRXNNetwork_Example-Info.plist */,
				81F5733C6B17B7D42263FE8ABC147497 /* Pods-RRXNNetwork_Example-umbrella.h */,
				A64CD2335CA6ED639AD2324D01F64F05 /* Pods-RRXNNetwork_Example.debug.xcconfig */,
				2A8247D1E638B4D88C9C23DBE09F2ABC /* Pods-RRXNNetwork_Example.release.xcconfig */,
			);
			name = "Pods-RRXNNetwork_Example";
			path = "Target Support Files/Pods-RRXNNetwork_Example";
			sourceTree = "<group>";
		};
		7C8497EF32C70E2FEFAD28EB7A832B28 /* SwiftSupport */ = {
			isa = PBXGroup;
			children = (
				402A2673376D8C5B96DA384361EF5486 /* SwiftSupport.swift */,
			);
			name = SwiftSupport;
			sourceTree = "<group>";
		};
		7D4C859CFF959E4D20615B7A8F5420DF /* Specta */ = {
			isa = PBXGroup;
			children = (
				9A6898141C5F8EEC97ACFD67E1E054B7 /* Specta.h */,
				55E2B922E82C59540A02DF9D77901AA1 /* SpectaDSL.h */,
				55A1A7755805EF535958531DDC9AF1BD /* SpectaDSL.m */,
				74B9B16385F7011FD061861BE966435F /* SpectaTypes.h */,
				102C20B67141BCE0A013903F84EF6C47 /* SpectaUtility.h */,
				2BDD457D5FEDFB4E52AA67296865F6B2 /* SpectaUtility.m */,
				2D2155790E45F071C8893E3CAF573BD9 /* SPTCallSite.h */,
				9E5ED21AD446EF506FA61C5B1C442469 /* SPTCallSite.m */,
				D73ACE5D872599443EB28233EEBB224E /* SPTCompiledExample.h */,
				9DE12AA56844865807C910E6A2DB67DC /* SPTCompiledExample.m */,
				98D37D597C20F78BCDE04426D7C648F5 /* SPTExample.h */,
				431A32406DCF7180C1321BCFFAC70B28 /* SPTExample.m */,
				F9AC5F1248A1159E01F1C799EDA2A961 /* SPTExampleGroup.h */,
				BC15168025F815F9FEEF2A78613859EE /* SPTExampleGroup.m */,
				983975F382C85EA85B4F4169E49A72CE /* SPTExcludeGlobalBeforeAfterEach.h */,
				208F9A37FD362DA6240E8C1CA770D6AE /* SPTGlobalBeforeAfterEach.h */,
				4F47101339D7DAC052A6468FD3D93E33 /* SPTSharedExampleGroups.h */,
				10AA0332DC8304347E64E2C275E780B2 /* SPTSharedExampleGroups.m */,
				1E5ECCB90FD628C17B917A3DA93A26DD /* SPTSpec.h */,
				55148F07A3CCF3189A0FE587174DB24D /* SPTSpec.m */,
				274871DD1E844FF2523BB9FCE73472F1 /* SPTTestSuite.h */,
				0F572CBC8DE244ED0672B8D060E8C32E /* SPTTestSuite.m */,
				8986979A7DC636A325D78F94E99DDBE2 /* XCTest+Private.h */,
				212794D7A73C01B2BE55BE10930CE17A /* XCTestCase+Specta.h */,
				39C56C939D311409F5101A635FBCED9D /* XCTestCase+Specta.m */,
				4C84C2C5BC1605A4201F1A6A7DF82F44 /* Support Files */,
			);
			name = Specta;
			path = Specta;
			sourceTree = "<group>";
		};
		90F06A9AB3C5E0F9379A0476551ABD50 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				CC9DCAE97C9D6094EABECFD3958E57E3 /* Expecta+Snapshots.modulemap */,
				618B79E532D687B28CCBB20EDF93F806 /* Expecta+Snapshots-dummy.m */,
				FE030903EC596BCD0B0F9117C1FDFDA5 /* Expecta+Snapshots-Info.plist */,
				0A244A4D3C362018305E401721ED2134 /* Expecta+Snapshots-prefix.pch */,
				A782737B791EBEB2970838B0FBCE5C64 /* Expecta+Snapshots-umbrella.h */,
				0478165A2541D70C4B57E197DD391FD2 /* Expecta+Snapshots.debug.xcconfig */,
				D9601B9938369E0B9FEB6055733C25EE /* Expecta+Snapshots.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/Expecta+Snapshots";
			sourceTree = "<group>";
		};
		A15D0DC7A454D026E4DD3E4574FE4465 /* Development Pods */ = {
			isa = PBXGroup;
			children = (
				C065C5553708F9980A653BC7BA96C640 /* RRXNNetwork */,
			);
			name = "Development Pods";
			sourceTree = "<group>";
		};
		A2B8223B539841676489A50226567E6A /* Core */ = {
			isa = PBXGroup;
			children = (
				1B8DCCC0586CCA186A9B441AC83F65B2 /* FBSnapshotTestCase.h */,
				3B1067DB6C8A9D3DDD83223CA265454F /* FBSnapshotTestCase.m */,
				FBB26F7A12B2E3089BBE83452DDAD08A /* FBSnapshotTestCasePlatform.h */,
				7897438240A4B99F78C7801FDE0A4675 /* FBSnapshotTestCasePlatform.m */,
				DC25EC7AF2A9861136C55449797A246E /* FBSnapshotTestController.h */,
				C1F2E0FD3E71C714E9A0278673E2B486 /* FBSnapshotTestController.m */,
				4D6310B7A96B3658889C1A8A3B1C9AA3 /* UIApplication+StrictKeyWindow.h */,
				49EA7E87FDF87EB5362D1E5121884E7B /* UIApplication+StrictKeyWindow.m */,
				7C5C955AD707E98A05704CEBDF2B001E /* UIImage+Compare.h */,
				89DAABCCD5C00031E687F7D7289DDF72 /* UIImage+Compare.m */,
				A7E2BDBE39E9CFE315B13E75F19E07AE /* UIImage+Diff.h */,
				65678EBF223B84DBAF92801A83F6F8B3 /* UIImage+Diff.m */,
				0975CAAFFF8F6C46E61131940FDA9DB1 /* UIImage+Snapshot.h */,
				3F054BA0A9243BC9BF84E14F820AE28E /* UIImage+Snapshot.m */,
			);
			name = Core;
			sourceTree = "<group>";
		};
		A83D98F9A526BBC44CA436FB2C870C8B /* MJExtension */ = {
			isa = PBXGroup;
			children = (
				DC669F3D73A900F252DA646BF63557CF /* MJExtension.h */,
				B5E8936F710B1628D3CDBC8E627F04F2 /* MJExtensionConst.h */,
				D4686DF0C3941D9F01AB9FEEB4F9649C /* MJExtensionConst.m */,
				E7F1041A2732068BD7222489683E42F5 /* MJFoundation.h */,
				7915A784D2E660A7BB7C7DC4D4C4B69D /* MJFoundation.m */,
				334651540DDF81475209AB218B269345 /* MJProperty.h */,
				F8DCA7A0EFF925F62ABFBEF2C385F3CA /* MJProperty.m */,
				6259D0E1511B4BB6CCA4126CD2CF6DEC /* MJPropertyKey.h */,
				8B73A651DC88E3BA3EEDCA4BBA4EC445 /* MJPropertyKey.m */,
				FD0E58C98AE5D9A1083E12EBF616A809 /* MJPropertyType.h */,
				BE85E66E07EB210EA1ACD44595CEF7DF /* MJPropertyType.m */,
				31B427E04B6036ED2404CDE9B088D7BE /* NSObject+MJClass.h */,
				790EE0A342A68982BB7057E7702329F9 /* NSObject+MJClass.m */,
				2FB71647BF31584DB75C2AD6212C96AC /* NSObject+MJCoding.h */,
				CF2A9C07FF2F529D95D33D74101BB22A /* NSObject+MJCoding.m */,
				419662C188F678151E58A4D4CEE9F1E2 /* NSObject+MJKeyValue.h */,
				4137DDCE24A99B879F4B6136CDD8A92C /* NSObject+MJKeyValue.m */,
				F21E831B60DD880B6CC15B7EA09B9DB0 /* NSObject+MJProperty.h */,
				6A1F40A04EDE5FD6D04DF5A5BA054627 /* NSObject+MJProperty.m */,
				FE41DBBACACD729135DFC43B547E6E68 /* NSString+MJExtension.h */,
				59876E021357905508D9A38EE03701B9 /* NSString+MJExtension.m */,
				3F734280FD5F0246B14D7AD82BAC2609 /* Support Files */,
			);
			name = MJExtension;
			path = MJExtension;
			sourceTree = "<group>";
		};
		BE08B7784D4316C5209981261871FCCA /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				29967CA281983865F76C58BDA798F7DE /* libRRXNNetwork.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		C065C5553708F9980A653BC7BA96C640 /* RRXNNetwork */ = {
			isa = PBXGroup;
			children = (
				D025BD2B0986A8714625CDEEC11787C1 /* XNHttpRequest.h */,
				B65B7D5E62F68F39011D7935CB00628B /* XNNetworkModel.h */,
				C3EC45197A2D14923BD161E4F4990893 /* XNSDKNetworkModel.h */,
				BE08B7784D4316C5209981261871FCCA /* Frameworks */,
				1BEAF35AB9F833061FE589BD8D63B9B5 /* Pod */,
				407541CE4DEDBBE15DA333E9742ED180 /* Support Files */,
			);
			name = RRXNNetwork;
			path = ../..;
			sourceTree = "<group>";
		};
		C8791AB1CA4D9B60D69D79822F67221A /* FBSnapshotTestCase */ = {
			isa = PBXGroup;
			children = (
				A2B8223B539841676489A50226567E6A /* Core */,
				0D5AE18A0B06CD14B01452FA1D88E76C /* Support Files */,
				7C8497EF32C70E2FEFAD28EB7A832B28 /* SwiftSupport */,
			);
			name = FBSnapshotTestCase;
			path = FBSnapshotTestCase;
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				A15D0DC7A454D026E4DD3E4574FE4465 /* Development Pods */,
				030DB05C81A99094FF0898DC6A5E0110 /* Frameworks */,
				F32F85EFF521F08E673971550A95BB4C /* Pods */,
				3D7E9027AE39C0742D4222FB3CC5A2E0 /* Products */,
				13387351E100B33CBEBC662C5E4DC91A /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		DF7370F0E9F831B310F9839BA03A7AF1 /* iOS */ = {
			isa = PBXGroup;
			children = (
				D60C285E57263F5A3465E9DFFAE18CC6 /* Foundation.framework */,
				208BAA193E3DBC090FB7DBAD81A67EB5 /* QuartzCore.framework */,
				9948276A8B9FABEC11ED2C7A978A2187 /* UIKit.framework */,
				264BACBF8E31580B57DC70B9E73A944E /* XCTest.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		F32F85EFF521F08E673971550A95BB4C /* Pods */ = {
			isa = PBXGroup;
			children = (
				3961E9C686129C0012CA32BC01EB36B0 /* Expecta */,
				525CEEDA2B91A1F7AB14E09309826A94 /* Expecta+Snapshots */,
				C8791AB1CA4D9B60D69D79822F67221A /* FBSnapshotTestCase */,
				A83D98F9A526BBC44CA436FB2C870C8B /* MJExtension */,
				7D4C859CFF959E4D20615B7A8F5420DF /* Specta */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		02DC89F7FB592C1D4A6197B34AE92D35 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				62E551E2D06D6CC46FEA244D299BE2A4 /* Expecta+Snapshots-umbrella.h in Headers */,
				421E2E02871F45F6FC44045DF1144A54 /* ExpectaObject+FBSnapshotTest.h in Headers */,
				AAD41575D71560C5BE9D3BEC532028F7 /* EXPMatchers+FBSnapshotTest.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4900EEFFF3A049EDA40F917F4EED85CD /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5242CFE182637D4BCCAC9E0BCC4483C4 /* EXPBlockDefinedMatcher.h in Headers */,
				521CCD99D4003505D4B65A4131BAFB22 /* EXPDefines.h in Headers */,
				4CD00C2DDD1130268D57492C92768C2D /* EXPDoubleTuple.h in Headers */,
				821F025A006CB157497E83C0EC102052 /* Expecta-umbrella.h in Headers */,
				97D9A825385A35B046B1DFF01F24136F /* Expecta.h in Headers */,
				4D48EA2D9F127FF844FE61016D25DDE0 /* ExpectaObject.h in Headers */,
				416D3BE841B08CED03D16649957ECB10 /* ExpectaSupport.h in Headers */,
				DBFF33BEB222CF58005C06FF0CD556DF /* EXPExpect.h in Headers */,
				3B2A59E466E5E952E51862D462BD0A6C /* EXPFloatTuple.h in Headers */,
				38A289C0550F25C9EDBF6A54BABC5A18 /* EXPMatcher.h in Headers */,
				8B8CB2F1361DD1EA4711D54F596569F4 /* EXPMatcherHelpers.h in Headers */,
				5C6754FDD1DFAD63B3689EE5A7897F01 /* EXPMatchers+beCloseTo.h in Headers */,
				C18FBD6CD221CAFD1AF15CC99A48CB84 /* EXPMatchers+beFalsy.h in Headers */,
				4EA3C56AE53AD37BC0273D80017164F0 /* EXPMatchers+beginWith.h in Headers */,
				6B575D5C03624D5A253D6C128F3768DC /* EXPMatchers+beGreaterThan.h in Headers */,
				25C2905BD04D69AB3CCA7BCC93D07AC7 /* EXPMatchers+beGreaterThanOrEqualTo.h in Headers */,
				66ABBD694E306E452A0D3DB3972FA429 /* EXPMatchers+beIdenticalTo.h in Headers */,
				32762E6201C8909C32899F1EA489913B /* EXPMatchers+beInstanceOf.h in Headers */,
				4FD8D4A9E3DFCABB9A26AA22FABA018B /* EXPMatchers+beInTheRangeOf.h in Headers */,
				3681AC7A2C3E52B5F6514D1560E13191 /* EXPMatchers+beKindOf.h in Headers */,
				28F23E68E55462EBC24ABF2F2F131255 /* EXPMatchers+beLessThan.h in Headers */,
				12C14FE4834134BC916F8DD42B875D3B /* EXPMatchers+beLessThanOrEqualTo.h in Headers */,
				873165886F20870B205EBE37F8185546 /* EXPMatchers+beNil.h in Headers */,
				0DE0AA588BC61692033B67CFCE4EC742 /* EXPMatchers+beSubclassOf.h in Headers */,
				2A46DD69C90BDFA0AD55A31D0F16A78A /* EXPMatchers+beSupersetOf.h in Headers */,
				4A03F7E68886AE6A13DFC35E9A9B942A /* EXPMatchers+beTruthy.h in Headers */,
				59D34E8065607E956AC2DC046E76B743 /* EXPMatchers+conformTo.h in Headers */,
				E969F8D679A40C630D2E855651A49D90 /* EXPMatchers+contain.h in Headers */,
				00B3EF135DE4634D6FA1E5EF15239824 /* EXPMatchers+endWith.h in Headers */,
				FBC9734FF84807AE5347B2196D7EBC6D /* EXPMatchers+equal.h in Headers */,
				ACBDE7479CEDFD9F3261E26D6B1015FC /* EXPMatchers+haveCountOf.h in Headers */,
				660F79D9086A7962E1FFFA101802E8E0 /* EXPMatchers+match.h in Headers */,
				D5C3E91A2121AE27F99028292B9436A8 /* EXPMatchers+postNotification.h in Headers */,
				3C82710DF39F627A44AE7067B65F92C7 /* EXPMatchers+raise.h in Headers */,
				1CF5E91F1AE39E4CA72E7CB9DC066BC5 /* EXPMatchers+raiseWithReason.h in Headers */,
				E791EEB9E7DA8C5DBDFF80A2BA7A6A4F /* EXPMatchers+respondTo.h in Headers */,
				41B490A5CB6565458F1F507A8AD0FB45 /* EXPMatchers.h in Headers */,
				777DB2CAF42F78F06C61F9A4AA3AD09C /* EXPUnsupportedObject.h in Headers */,
				114C2163DAE74196CAF6FA2E5FB633D7 /* NSObject+Expecta.h in Headers */,
				008A37B2A63BC1F3E33557FEF5BEE189 /* NSValue+Expecta.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		51920A20FE5CC59B2094BDDE3818B4B3 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C6A9508A7FD62A641E84BA24EAE1221D /* Pods-RRXNNetwork_Example-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		82FC218B985DF3D4673ACDD61B2E9DD1 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				86BEB7586600AA84390E9281D76EF3C9 /* Specta-umbrella.h in Headers */,
				336CECBF15D84BEED991129EB1D216FD /* Specta.h in Headers */,
				73387C9F80146B32C2C6A6912385157E /* SpectaDSL.h in Headers */,
				8431178BE956AD1EAD03D5A155661A49 /* SpectaTypes.h in Headers */,
				7E241629D4A2B42D8BD26BC1007CCF82 /* SpectaUtility.h in Headers */,
				EB37F8D1C24FE29BC22691ED1C0FDAD3 /* SPTCallSite.h in Headers */,
				66096613585721460B95DA2C43D07E18 /* SPTCompiledExample.h in Headers */,
				4F10A7AA65CECC8F35127673D0695B7B /* SPTExample.h in Headers */,
				C692F5BF6AA4421A622E9080EA79F7FA /* SPTExampleGroup.h in Headers */,
				467DD58C550CA69A89271EE938C59199 /* SPTExcludeGlobalBeforeAfterEach.h in Headers */,
				D07B657936CAAF7E35236DF1A85E4C36 /* SPTGlobalBeforeAfterEach.h in Headers */,
				62C82C06E44453A6F10D76B4F1C9A8EB /* SPTSharedExampleGroups.h in Headers */,
				2CCA1E306E93380C2AE4F1043872FDA5 /* SPTSpec.h in Headers */,
				CBC2D78B7950ADE46E54AB83721F1ADF /* SPTTestSuite.h in Headers */,
				32A3818AA4621A9A9BD3ECD5CCFCEDA8 /* XCTest+Private.h in Headers */,
				5E3B7A0571C1949090A34F9C7871AD42 /* XCTestCase+Specta.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B92BCDC038613D74A0EE7D94436CCE0E /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				19F41D9168054C85C99DAE77A9A0B511 /* FBSnapshotTestCase-umbrella.h in Headers */,
				F77F788A1F6AC791644AB70E5AA31B60 /* FBSnapshotTestCase.h in Headers */,
				D7E0395840A93502D928B2B392479C5E /* FBSnapshotTestCasePlatform.h in Headers */,
				506200204CAFAE6387207AEF27513195 /* FBSnapshotTestController.h in Headers */,
				F07956640E1263458BD4CC074C68FA9B /* UIApplication+StrictKeyWindow.h in Headers */,
				EA52D7D80F55EA982F56D85B1878F70C /* UIImage+Compare.h in Headers */,
				5C1CA24C739C943A7A9A525438E3E2A8 /* UIImage+Diff.h in Headers */,
				F4BD31D7FFFC8EC2102581C790B8AD57 /* UIImage+Snapshot.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB1DE317F3D4815D87890DEB09B99C2E /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				41BBD20F99D3D0EFD9D38337503D4C1F /* MJExtension-umbrella.h in Headers */,
				9796F9FAC1941A992E8B17F31D62B2E4 /* MJExtension.h in Headers */,
				912758556B9AB1DEEABE189955C5B8F2 /* MJExtensionConst.h in Headers */,
				CAD2539BE3C4AB2F1FD1DF30D7629238 /* MJFoundation.h in Headers */,
				FAB7CF107E7E3EE6FA7949CF18B8BA12 /* MJProperty.h in Headers */,
				A47472F50A3A7869A644789159571A5C /* MJPropertyKey.h in Headers */,
				B94AD428EA4EFD7BA79BCF57680D390A /* MJPropertyType.h in Headers */,
				F0EF96DAB145D1B2247C01B3517B54FE /* NSObject+MJClass.h in Headers */,
				6EC015330CAC3D60745A20B01928B8DB /* NSObject+MJCoding.h in Headers */,
				25E03213AA01D83EB415A04635B34F1A /* NSObject+MJKeyValue.h in Headers */,
				C0C8961B2EAF90DA7244B427C569FCFD /* NSObject+MJProperty.h in Headers */,
				51E7A17D617310CE81444F5D315EAEF0 /* NSString+MJExtension.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F1A2B2F32A2730FDF51B2BF09CDCB55A /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CABEED8EF629A236023F097BB06F5162 /* Pods-RRXNNetwork_Tests-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		4D3BA58D0583DF37575CACAB3DDADC85 /* MJExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 5B8E68449A18121BC5A5B5C491F1283E /* Build configuration list for PBXNativeTarget "MJExtension" */;
			buildPhases = (
				DB1DE317F3D4815D87890DEB09B99C2E /* Headers */,
				7062CDA8FD11E2E9D19A9A40021C44AD /* Sources */,
				E3DFB7F06EC80553131DB5B0993D681C /* Frameworks */,
				5526C4C8822D8F13C2C736A5F9677481 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = MJExtension;
			productName = MJExtension;
			productReference = 2B276B0A79173A1D6E83C9B4FB9A4A57 /* MJExtension.framework */;
			productType = "com.apple.product-type.framework";
		};
		4FF0F902969DCD6815514C0C1B4E15FE /* Pods-RRXNNetwork_Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4BEE929749C66A9AF1D2943C16890BDD /* Build configuration list for PBXNativeTarget "Pods-RRXNNetwork_Tests" */;
			buildPhases = (
				F1A2B2F32A2730FDF51B2BF09CDCB55A /* Headers */,
				565E03A934CA0597DA7089ADE472F337 /* Sources */,
				682792CCAB1324BA58826A2B6259E6BA /* Frameworks */,
				9820A81FDB5FA5B9FCD9F84116C914C1 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				897FF1D898790A9EAA9438478482620C /* PBXTargetDependency */,
				34DDC72829C6D53F570BF27D0D5AA184 /* PBXTargetDependency */,
				0EFAF29B5A339D1A3DCC21CAD3E8E8B7 /* PBXTargetDependency */,
				992349E2D38574059EAA85E0FA2EE205 /* PBXTargetDependency */,
				596DE86469B595DFC00BC5DA2C253D02 /* PBXTargetDependency */,
			);
			name = "Pods-RRXNNetwork_Tests";
			productName = "Pods-RRXNNetwork_Tests";
			productReference = F62C4D95005825B7ED249D6378436945 /* Pods_RRXNNetwork_Tests.framework */;
			productType = "com.apple.product-type.framework";
		};
		8B98F09738742E4D780D1B20B468CD95 /* Expecta+Snapshots */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A544F23B7B54645476CE7B4439129651 /* Build configuration list for PBXNativeTarget "Expecta+Snapshots" */;
			buildPhases = (
				02DC89F7FB592C1D4A6197B34AE92D35 /* Headers */,
				53058DC29B66B76A037E1F9F36DF6B5C /* Sources */,
				7258024DA974B53FCC495CE6929F3D9D /* Frameworks */,
				7441ACE0B91F2EC611DDA60F668C4A2C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				BD9CDEDC121A1DBCFE0846DC7841D1A7 /* PBXTargetDependency */,
				4EE07287320D739304449952E960851B /* PBXTargetDependency */,
				557024EE30D538ECE901C2649C0C9933 /* PBXTargetDependency */,
			);
			name = "Expecta+Snapshots";
			productName = "Expecta+Snapshots";
			productReference = 20B68C9269B45825E82F4F5ECE0EB27C /* Expecta_Snapshots.framework */;
			productType = "com.apple.product-type.framework";
		};
		98A98149697C80CEF8D5772791E92E66 /* FBSnapshotTestCase */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B9EF8E4D56B41516A5382798F2EE4E34 /* Build configuration list for PBXNativeTarget "FBSnapshotTestCase" */;
			buildPhases = (
				B92BCDC038613D74A0EE7D94436CCE0E /* Headers */,
				BDB3F34F723D48B548EFB38471EA6889 /* Sources */,
				BBD15B31B2C1B6A03B79F62467394B15 /* Frameworks */,
				49C4A16F8CE1F29C8E8C33FEFA8623D5 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = FBSnapshotTestCase;
			productName = FBSnapshotTestCase;
			productReference = 5C4F31330DFA99D699E4BDC8C3573D73 /* FBSnapshotTestCase.framework */;
			productType = "com.apple.product-type.framework";
		};
		BEB305119BDB853F0B6A905DA436143F /* Pods-RRXNNetwork_Example */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0AEF3F59E81E9687B2CB8FA06729978F /* Build configuration list for PBXNativeTarget "Pods-RRXNNetwork_Example" */;
			buildPhases = (
				51920A20FE5CC59B2094BDDE3818B4B3 /* Headers */,
				77C87FF9E9DB36E3D4A0702316C295F5 /* Sources */,
				6A2E4256BC18FACFD2180CA4C02A536F /* Frameworks */,
				D7C05223E0B58636F66DE1CBF0C682FC /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				A8FCE1D505486F7C446DD36555417AB0 /* PBXTargetDependency */,
				8B866729DF8E87EF2CD9E084C197D175 /* PBXTargetDependency */,
			);
			name = "Pods-RRXNNetwork_Example";
			productName = "Pods-RRXNNetwork_Example";
			productReference = 4006B2D9C60DA9AACCDAB092A0AB899C /* Pods_RRXNNetwork_Example.framework */;
			productType = "com.apple.product-type.framework";
		};
		DC371B7477C88184274EC6710690F97C /* Expecta */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 902BCF405B3D02679340695CD9C09879 /* Build configuration list for PBXNativeTarget "Expecta" */;
			buildPhases = (
				4900EEFFF3A049EDA40F917F4EED85CD /* Headers */,
				9BF49B144A81B4CF50843EB439765834 /* Sources */,
				7312BAFD17174C2E85416CF941012159 /* Frameworks */,
				34011600D72DD04AC12C96367136E94B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Expecta;
			productName = Expecta;
			productReference = 08F7F0770B4878B9883B87DCD8569CB4 /* Expecta.framework */;
			productType = "com.apple.product-type.framework";
		};
		F8676010755CF1530FC02DA9A0D8822B /* Specta */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6F41D3429610B3D645B5897D13031194 /* Build configuration list for PBXNativeTarget "Specta" */;
			buildPhases = (
				82FC218B985DF3D4673ACDD61B2E9DD1 /* Headers */,
				5A4BB1BFDB2326E302CF59DE1A696906 /* Sources */,
				C2F33B6F846A9408CC9FBB1E0F9C9B9B /* Frameworks */,
				4677A3C1261672591884FD482A7381DA /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Specta;
			productName = Specta;
			productReference = 15B13B063AA97C48C9010C298AECBDDA /* Specta.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1100;
				LastUpgradeCheck = 1100;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			productRefGroup = 3D7E9027AE39C0742D4222FB3CC5A2E0 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				DC371B7477C88184274EC6710690F97C /* Expecta */,
				8B98F09738742E4D780D1B20B468CD95 /* Expecta+Snapshots */,
				98A98149697C80CEF8D5772791E92E66 /* FBSnapshotTestCase */,
				4D3BA58D0583DF37575CACAB3DDADC85 /* MJExtension */,
				BEB305119BDB853F0B6A905DA436143F /* Pods-RRXNNetwork_Example */,
				4FF0F902969DCD6815514C0C1B4E15FE /* Pods-RRXNNetwork_Tests */,
				08A0C7AD2171E1B59DB7361465394C73 /* RRXNNetwork */,
				F8676010755CF1530FC02DA9A0D8822B /* Specta */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		34011600D72DD04AC12C96367136E94B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4677A3C1261672591884FD482A7381DA /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		49C4A16F8CE1F29C8E8C33FEFA8623D5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5526C4C8822D8F13C2C736A5F9677481 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7441ACE0B91F2EC611DDA60F668C4A2C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9820A81FDB5FA5B9FCD9F84116C914C1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D7C05223E0B58636F66DE1CBF0C682FC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		53058DC29B66B76A037E1F9F36DF6B5C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D4472453A8147DF973BAAB33AE7320E0 /* Expecta+Snapshots-dummy.m in Sources */,
				B1E009C09736B0D8150B3FF489A1B627 /* ExpectaObject+FBSnapshotTest.m in Sources */,
				2A93B4FF56A270D64F8CCBF8D2DD0A6D /* EXPMatchers+FBSnapshotTest.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		565E03A934CA0597DA7089ADE472F337 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				76930156A1D3E8B1CA29285999690F6C /* Pods-RRXNNetwork_Tests-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5A4BB1BFDB2326E302CF59DE1A696906 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F5D444B578A69550DFF8BE1C6A5D2CFC /* Specta-dummy.m in Sources */,
				C42994AEEDDC8243740ED31DB9007807 /* SpectaDSL.m in Sources */,
				F3542D58505D8D67A9DA62BF0816F759 /* SpectaUtility.m in Sources */,
				A1E07AC96433D5912BC3075D3FF0C207 /* SPTCallSite.m in Sources */,
				08D035ADB73D33A2CCF38D93822D6BE0 /* SPTCompiledExample.m in Sources */,
				C01879F2C9DEAB70FB7C5CFB3B7C4647 /* SPTExample.m in Sources */,
				F123A8E2BCE34F84A7C42887F33116E2 /* SPTExampleGroup.m in Sources */,
				AC5F9E839FB9F882601510390E523747 /* SPTSharedExampleGroups.m in Sources */,
				104534994BF3B515BB9EE2C0073FCA88 /* SPTSpec.m in Sources */,
				4A1B41B33D507DA09EF085F49492AAD1 /* SPTTestSuite.m in Sources */,
				24495C552C108DB6DC2205A79C64F72F /* XCTestCase+Specta.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7062CDA8FD11E2E9D19A9A40021C44AD /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				05452B06007B8F1969A23C3C9D494CBD /* MJExtension-dummy.m in Sources */,
				01E3E1FB1F9A0FA56C79608F8139A32F /* MJExtensionConst.m in Sources */,
				033484819373AB7E020C0026CAE0F7BF /* MJFoundation.m in Sources */,
				3BAA6DDBE44238B40D6A72A16EF41814 /* MJProperty.m in Sources */,
				7155607EADA71F37CFE188D866B7E40A /* MJPropertyKey.m in Sources */,
				8A2E1E487FBFC3DF55C12C056BFA36A1 /* MJPropertyType.m in Sources */,
				0077E2C1E1042512F36A3204CFD42BEF /* NSObject+MJClass.m in Sources */,
				7BE71B62F38458AC771581F60006F9CE /* NSObject+MJCoding.m in Sources */,
				99BF63084CB0EDEA0A9CBB77E8315E6E /* NSObject+MJKeyValue.m in Sources */,
				07288680C9E7EDC1E240E0E3C50EE65C /* NSObject+MJProperty.m in Sources */,
				E6775033B3F0080B56E29AB395A08867 /* NSString+MJExtension.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		77C87FF9E9DB36E3D4A0702316C295F5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				32F37AC2805D9AFF29F79D97F6FB7D1D /* Pods-RRXNNetwork_Example-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9BF49B144A81B4CF50843EB439765834 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3DB6607AA00EDF662D4328EA4F9C44EC /* EXPBlockDefinedMatcher.m in Sources */,
				61CA94EF2639807466427720DA2091C6 /* EXPDoubleTuple.m in Sources */,
				96BC54118F99CACBCEDE0B64784E0737 /* Expecta-dummy.m in Sources */,
				B628F2BB909FBE9D533C0C6D382BAA9E /* ExpectaObject.m in Sources */,
				7967AB2E31F5D064244EB23735D0B3E8 /* ExpectaSupport.m in Sources */,
				B999DEBC999F335CC8E6C471F942F93D /* EXPExpect.m in Sources */,
				F3EDE6D241EE2A6D0992A24ACDDBAB35 /* EXPFloatTuple.m in Sources */,
				C4632B64DD0F3ADB3BB829A6EBC322E6 /* EXPMatcherHelpers.m in Sources */,
				F31CCBB90D75D9E5A83545FD95954FFA /* EXPMatchers+beCloseTo.m in Sources */,
				6E8A45EF6CD46D90A634E1E9163E562E /* EXPMatchers+beFalsy.m in Sources */,
				05CFF6618B87EDE2069D4C5D3DDFF9EB /* EXPMatchers+beginWith.m in Sources */,
				46ACAF12638940470981FA6689B0469F /* EXPMatchers+beGreaterThan.m in Sources */,
				4E7928EF9169E1DE84CBF429162A4128 /* EXPMatchers+beGreaterThanOrEqualTo.m in Sources */,
				F4591B9C7F70268A3F94B40ADDBA5202 /* EXPMatchers+beIdenticalTo.m in Sources */,
				B5596C58EE23B7C7BCE850A4C6679F1C /* EXPMatchers+beInstanceOf.m in Sources */,
				5DD71838737687E6FA4583D98B08D7D4 /* EXPMatchers+beInTheRangeOf.m in Sources */,
				244D8552B11F2D1B417EBB82BBE9E938 /* EXPMatchers+beKindOf.m in Sources */,
				B31E1ABB0DF9F6E7C3B8BEC90C04FCBE /* EXPMatchers+beLessThan.m in Sources */,
				9AC1A3902FC9162C9B432B411928B215 /* EXPMatchers+beLessThanOrEqualTo.m in Sources */,
				EC57F974317D76B799CFE2A018B5F9E2 /* EXPMatchers+beNil.m in Sources */,
				188A55E8570219DC971027460C2BCF1B /* EXPMatchers+beSubclassOf.m in Sources */,
				20471DE376ACB94F0E144A26EFFCFD21 /* EXPMatchers+beSupersetOf.m in Sources */,
				140394F97EEF0A95AB171649249F211C /* EXPMatchers+beTruthy.m in Sources */,
				DE7FC5D8F81CE893DB6EDF99865350C5 /* EXPMatchers+conformTo.m in Sources */,
				7709CF64039955B3BAF42CDA38E0F96F /* EXPMatchers+contain.m in Sources */,
				D327EEB8522F6389513EB84ACF12F242 /* EXPMatchers+endWith.m in Sources */,
				05BCAED08DADF67CC4711A102262D9AB /* EXPMatchers+equal.m in Sources */,
				15C0054801F96BA3329545B1FAA6A85E /* EXPMatchers+haveCountOf.m in Sources */,
				B24C0951E730C8D7B6539598555A0126 /* EXPMatchers+match.m in Sources */,
				CAEE0E44BC37459BD49C0B1D626B0477 /* EXPMatchers+postNotification.m in Sources */,
				91F08081D0286CC0733C2D5A9B4A8F32 /* EXPMatchers+raise.m in Sources */,
				E2058F4A0E7638E420DE96F8DC17AAA9 /* EXPMatchers+raiseWithReason.m in Sources */,
				1EDA4657EEF6B795B643063E05E8DC57 /* EXPMatchers+respondTo.m in Sources */,
				3372164C22B301D4D5F1C9C7C6DDB25D /* EXPUnsupportedObject.m in Sources */,
				19621E59AF2C3772FF6290740DB6EAB4 /* NSValue+Expecta.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BDB3F34F723D48B548EFB38471EA6889 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EBDF90B06D57C3007BE2C2E41EA38715 /* FBSnapshotTestCase-dummy.m in Sources */,
				6760CBEEEF3F3D63CFEC0F6CAA5E874E /* FBSnapshotTestCase.m in Sources */,
				D6DC25726EA495CE08EFA81AF65B81B0 /* FBSnapshotTestCasePlatform.m in Sources */,
				F346958A5DC33C5043C2BF36C6D49CE9 /* FBSnapshotTestController.m in Sources */,
				CDB51044D4EE9EB628D098CF53F75985 /* SwiftSupport.swift in Sources */,
				15E1E0CCB36191445EE86E9DD876417C /* UIApplication+StrictKeyWindow.m in Sources */,
				3E7DE43360ABFE1981835683FE47148F /* UIImage+Compare.m in Sources */,
				B0502CA7E2E35CB490EFAF55702B626C /* UIImage+Diff.m in Sources */,
				69A41F3EBD91571F24C3047B901FB519 /* UIImage+Snapshot.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		0EFAF29B5A339D1A3DCC21CAD3E8E8B7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = FBSnapshotTestCase;
			target = 98A98149697C80CEF8D5772791E92E66 /* FBSnapshotTestCase */;
			targetProxy = FF6FA1F98B129F011D3B4BD0514978AA /* PBXContainerItemProxy */;
		};
		34DDC72829C6D53F570BF27D0D5AA184 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "Expecta+Snapshots";
			target = 8B98F09738742E4D780D1B20B468CD95 /* Expecta+Snapshots */;
			targetProxy = 61EDDB15091045A9C90F0DE1423B4B38 /* PBXContainerItemProxy */;
		};
		4EE07287320D739304449952E960851B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = FBSnapshotTestCase;
			target = 98A98149697C80CEF8D5772791E92E66 /* FBSnapshotTestCase */;
			targetProxy = CBFA23AF8313C43BCBB3A76E4307E725 /* PBXContainerItemProxy */;
		};
		557024EE30D538ECE901C2649C0C9933 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Specta;
			target = F8676010755CF1530FC02DA9A0D8822B /* Specta */;
			targetProxy = ADFBE44D73599FEEC8C991F9CFC627C2 /* PBXContainerItemProxy */;
		};
		596DE86469B595DFC00BC5DA2C253D02 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Specta;
			target = F8676010755CF1530FC02DA9A0D8822B /* Specta */;
			targetProxy = 349380DC267AD294183C697ACA4321DC /* PBXContainerItemProxy */;
		};
		87A0AD161B20CB5A1427EF8D798E8C9C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = MJExtension;
			target = 4D3BA58D0583DF37575CACAB3DDADC85 /* MJExtension */;
			targetProxy = 0E9A3D3EFBE113FE99878CA2AA08FA7B /* PBXContainerItemProxy */;
		};
		897FF1D898790A9EAA9438478482620C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Expecta;
			target = DC371B7477C88184274EC6710690F97C /* Expecta */;
			targetProxy = 99B96CE6B0894A063CA71E74CE287DD6 /* PBXContainerItemProxy */;
		};
		8B866729DF8E87EF2CD9E084C197D175 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = RRXNNetwork;
			target = 08A0C7AD2171E1B59DB7361465394C73 /* RRXNNetwork */;
			targetProxy = A0DA94BE05F7A760579BA78E0968BDB7 /* PBXContainerItemProxy */;
		};
		992349E2D38574059EAA85E0FA2EE205 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "Pods-RRXNNetwork_Example";
			target = BEB305119BDB853F0B6A905DA436143F /* Pods-RRXNNetwork_Example */;
			targetProxy = 10F417E8A0F1AE38B3C8BB75F0A066A7 /* PBXContainerItemProxy */;
		};
		A8FCE1D505486F7C446DD36555417AB0 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = MJExtension;
			target = 4D3BA58D0583DF37575CACAB3DDADC85 /* MJExtension */;
			targetProxy = CB43A4E21BE74BF94150FBEA89EF5B4B /* PBXContainerItemProxy */;
		};
		BD9CDEDC121A1DBCFE0846DC7841D1A7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Expecta;
			target = DC371B7477C88184274EC6710690F97C /* Expecta */;
			targetProxy = 4515D7A38A49166269D9AB4E98CDC7D4 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		0AD146C765FE96A6E7152CF0B46D7C76 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9BEAB5C3DF63DB81BA3958CDC196ED81 /* RRXNNetwork.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1BDCB158D164BC9BB11425D9F08B59C7 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F2988E82B1B862F659EF40EAC917BBB9 /* FBSnapshotTestCase.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GCC_PREFIX_HEADER = "Target Support Files/FBSnapshotTestCase/FBSnapshotTestCase-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/FBSnapshotTestCase/FBSnapshotTestCase-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MODULEMAP_FILE = "Target Support Files/FBSnapshotTestCase/FBSnapshotTestCase.modulemap";
				PRODUCT_MODULE_NAME = FBSnapshotTestCase;
				PRODUCT_NAME = FBSnapshotTestCase;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		25AD9454612BF454A1E3DC4CD4FA8C6D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		2B086BBC99E9BA4C23007D19F68A1FFC /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D9601B9938369E0B9FEB6055733C25EE /* Expecta+Snapshots.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GCC_PREFIX_HEADER = "Target Support Files/Expecta+Snapshots/Expecta+Snapshots-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/Expecta+Snapshots/Expecta+Snapshots-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MODULEMAP_FILE = "Target Support Files/Expecta+Snapshots/Expecta+Snapshots.modulemap";
				PRODUCT_MODULE_NAME = Expecta_Snapshots;
				PRODUCT_NAME = Expecta_Snapshots;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		2CB49082F399147F4342A19C616AA608 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 936442D31F330A11B693D5907C86E4EA /* Expecta.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GCC_PREFIX_HEADER = "Target Support Files/Expecta/Expecta-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/Expecta/Expecta-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MODULEMAP_FILE = "Target Support Files/Expecta/Expecta.modulemap";
				PRODUCT_MODULE_NAME = Expecta;
				PRODUCT_NAME = Expecta;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		345ACEAC606A779AA44E9431E06F2F27 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7497DFCC302C671A7B61B118BB024923 /* Pods-RRXNNetwork_Tests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = "Target Support Files/Pods-RRXNNetwork_Tests/Pods-RRXNNetwork_Tests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-RRXNNetwork_Tests/Pods-RRXNNetwork_Tests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		3FABB26DA012ECDDE776FFD6AEEFF216 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7733E87F3638AEBFB768DC9D1BFBFA81 /* Specta.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GCC_PREFIX_HEADER = "Target Support Files/Specta/Specta-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/Specta/Specta-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MODULEMAP_FILE = "Target Support Files/Specta/Specta.modulemap";
				PRODUCT_MODULE_NAME = Specta;
				PRODUCT_NAME = Specta;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		40E9FE60E38621F89BB988A25BDE78BD /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E65EC312B2A51B95CC3CCA8D3876E923 /* MJExtension.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GCC_PREFIX_HEADER = "Target Support Files/MJExtension/MJExtension-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/MJExtension/MJExtension-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MODULEMAP_FILE = "Target Support Files/MJExtension/MJExtension.modulemap";
				PRODUCT_MODULE_NAME = MJExtension;
				PRODUCT_NAME = MJExtension;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		46735F4245D338158831A19A6AAACDCD /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = EAE3B877ABF534F2E865E08C864EAA46 /* FBSnapshotTestCase.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GCC_PREFIX_HEADER = "Target Support Files/FBSnapshotTestCase/FBSnapshotTestCase-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/FBSnapshotTestCase/FBSnapshotTestCase-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MODULEMAP_FILE = "Target Support Files/FBSnapshotTestCase/FBSnapshotTestCase.modulemap";
				PRODUCT_MODULE_NAME = FBSnapshotTestCase;
				PRODUCT_NAME = FBSnapshotTestCase;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		46FBC0A4B6C32C455E2978CC826AAFE9 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0E94121B11103286987A23F57F9390BF /* MJExtension.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GCC_PREFIX_HEADER = "Target Support Files/MJExtension/MJExtension-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/MJExtension/MJExtension-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MODULEMAP_FILE = "Target Support Files/MJExtension/MJExtension.modulemap";
				PRODUCT_MODULE_NAME = MJExtension;
				PRODUCT_NAME = MJExtension;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		62715F4BCB22C4D04FBBC298D749D028 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A64CD2335CA6ED639AD2324D01F64F05 /* Pods-RRXNNetwork_Example.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = "Target Support Files/Pods-RRXNNetwork_Example/Pods-RRXNNetwork_Example-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-RRXNNetwork_Example/Pods-RRXNNetwork_Example.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		62947E67332EB5865237FAD57FAFA3F9 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F3E2BB2DB6706CCB4367FBFFBD6D1176 /* Pods-RRXNNetwork_Tests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = "Target Support Files/Pods-RRXNNetwork_Tests/Pods-RRXNNetwork_Tests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-RRXNNetwork_Tests/Pods-RRXNNetwork_Tests.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		62EB3BC205E02EB2A53F8415D987114C /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2A8247D1E638B4D88C9C23DBE09F2ABC /* Pods-RRXNNetwork_Example.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = "Target Support Files/Pods-RRXNNetwork_Example/Pods-RRXNNetwork_Example-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-RRXNNetwork_Example/Pods-RRXNNetwork_Example.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		7E9E5644D93371EBF5A31325B43A85C4 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 671437566B7A8A198942147B2DF39543 /* RRXNNetwork.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		917BA6399CD127871549EA177A64D30E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3B8A95D30219FB9207A9CEB98E49283E /* Specta.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GCC_PREFIX_HEADER = "Target Support Files/Specta/Specta-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/Specta/Specta-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MODULEMAP_FILE = "Target Support Files/Specta/Specta.modulemap";
				PRODUCT_MODULE_NAME = Specta;
				PRODUCT_NAME = Specta;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		A498D33CB3B72CC19E15D227D80DE848 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0478165A2541D70C4B57E197DD391FD2 /* Expecta+Snapshots.debug.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GCC_PREFIX_HEADER = "Target Support Files/Expecta+Snapshots/Expecta+Snapshots-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/Expecta+Snapshots/Expecta+Snapshots-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MODULEMAP_FILE = "Target Support Files/Expecta+Snapshots/Expecta+Snapshots.modulemap";
				PRODUCT_MODULE_NAME = Expecta_Snapshots;
				PRODUCT_NAME = Expecta_Snapshots;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		CA547D2C7E9A8A153DC2B27FBE00B112 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		D63A6CE1A84E918B6641AE3388AC457F /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BA055E4BFD25A0EB27B34A60C217E649 /* Expecta.release.xcconfig */;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GCC_PREFIX_HEADER = "Target Support Files/Expecta/Expecta-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/Expecta/Expecta-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MODULEMAP_FILE = "Target Support Files/Expecta/Expecta.modulemap";
				PRODUCT_MODULE_NAME = Expecta;
				PRODUCT_NAME = Expecta;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0AEF3F59E81E9687B2CB8FA06729978F /* Build configuration list for PBXNativeTarget "Pods-RRXNNetwork_Example" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				62715F4BCB22C4D04FBBC298D749D028 /* Debug */,
				62EB3BC205E02EB2A53F8415D987114C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				25AD9454612BF454A1E3DC4CD4FA8C6D /* Debug */,
				CA547D2C7E9A8A153DC2B27FBE00B112 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4BEE929749C66A9AF1D2943C16890BDD /* Build configuration list for PBXNativeTarget "Pods-RRXNNetwork_Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				345ACEAC606A779AA44E9431E06F2F27 /* Debug */,
				62947E67332EB5865237FAD57FAFA3F9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		5B8E68449A18121BC5A5B5C491F1283E /* Build configuration list for PBXNativeTarget "MJExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				40E9FE60E38621F89BB988A25BDE78BD /* Debug */,
				46FBC0A4B6C32C455E2978CC826AAFE9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6F41D3429610B3D645B5897D13031194 /* Build configuration list for PBXNativeTarget "Specta" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				917BA6399CD127871549EA177A64D30E /* Debug */,
				3FABB26DA012ECDDE776FFD6AEEFF216 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		902BCF405B3D02679340695CD9C09879 /* Build configuration list for PBXNativeTarget "Expecta" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2CB49082F399147F4342A19C616AA608 /* Debug */,
				D63A6CE1A84E918B6641AE3388AC457F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9CDB0F6920843124AE017576184187EC /* Build configuration list for PBXAggregateTarget "RRXNNetwork" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0AD146C765FE96A6E7152CF0B46D7C76 /* Debug */,
				7E9E5644D93371EBF5A31325B43A85C4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A544F23B7B54645476CE7B4439129651 /* Build configuration list for PBXNativeTarget "Expecta+Snapshots" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A498D33CB3B72CC19E15D227D80DE848 /* Debug */,
				2B086BBC99E9BA4C23007D19F68A1FFC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B9EF8E4D56B41516A5382798F2EE4E34 /* Build configuration list for PBXNativeTarget "FBSnapshotTestCase" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1BDCB158D164BC9BB11425D9F08B59C7 /* Debug */,
				46735F4245D338158831A19A6AAACDCD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
