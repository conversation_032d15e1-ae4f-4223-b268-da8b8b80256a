import Flutter
import UIKit
// import GMObjC  // Temporarily commented out due to static framework issue

public class SwiftRrworkmanagerPlugin: NSObject, FlutterPlugin {
  public static func register(with registrar: FlutterPluginRegistrar) {
    let channel = FlutterMethodChannel(name: "rrworkmanager", binaryMessenger: registrar.messenger())
    let instance = SwiftRrworkmanagerPlugin()
    registrar.addMethodCallDelegate(instance, channel: channel)
  }

  public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
    let arguments = call.arguments as! NSDictionary
      if ("SM2Encrypt" == call.method) {
        // Temporarily disabled - GMObjC dependency removed
        result("GMObjC functionality temporarily disabled")
      } else if ("SM3Signature" == call.method) {
        // Temporarily disabled - GMObjC dependency removed  
        result("GMObjC functionality temporarily disabled")
      } else {
        result(FlutterMethodNotImplemented);
      }
  }
}
