// ignore: file_names
class FeedbackSubTypeListApiBean {
  int? code;
  List<FeedbackSubTypeBean>? data;
  bool? success;
  String? message;
  String? status;

  FeedbackSubTypeListApiBean(
      {this.code, this.data, this.success, this.message, this.status});

  FeedbackSubTypeListApiBean.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    if (json['data'] != null) {
      data = <FeedbackSubTypeBean>[];
      json['data'].forEach((v) {
        data!.add(FeedbackSubTypeBean.fromJson(v));
      });
    }
    success = json['success'];
    message = json['message'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['success'] = success;
    data['message'] = message;
    data['status'] = status;
    return data;
  }
}

class FeedbackSubTypeBean {
  String? addTime;
  String? typeName;
  String? id;
  List<String>? visibleConfig;
  String? typeAttribution;

  FeedbackSubTypeBean(
      {this.addTime,
      this.typeName,
      this.id,
      this.visibleConfig,
      this.typeAttribution});

  FeedbackSubTypeBean.fromJson(Map<String, dynamic> json) {
    addTime = json['addTime'];
    typeName = json['typeName'];
    id = json['id'];
    if (json['visibleConfig'] != null) {
      visibleConfig = <String>[];
      json['visibleConfig'].forEach((v) {
        visibleConfig!.add(v);
      });
    }
    typeAttribution = json['typeAttribution'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['addTime'] = addTime;
    data['typeName'] = typeName;
    data['id'] = id;
    if (visibleConfig != null) {
      data['visibleConfig'] = visibleConfig!;
    }
    data['typeAttribution'] = typeAttribution;
    return data;
  }
}
