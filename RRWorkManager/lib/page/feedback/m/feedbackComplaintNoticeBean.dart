class FeedbackComplaintNoticeBean {
  int? code;
  String? data;
  bool? success;
  String? message;
  String? status;

  FeedbackComplaintNoticeBean(
      {this.code,
      this.data,
      this.success,
      this.message,
      this.status});

  FeedbackComplaintNoticeBean.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    data = json['data'];
    success = json['success'];
    message = json['message'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['data'] = this.data;
    data['success'] = success;
    data['message'] = message;
    data['status'] = status;
    return data;
  }
}