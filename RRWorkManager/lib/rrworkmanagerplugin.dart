import 'package:flutter/widgets.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class RRWorkManagerPlugin {
  static Map<String, dynamic> userInfo = {
    "userId": "4783248763274",
    "userName": "agent1",
    "token": "6d6e46407d530cb406b8b67a0d8003d2e7a5dda0fc62cf9c651aae4c8905971e",
    "tradeInfo":{
      "tradeType":"20",
      "tradeTypeDesc":"Withdrawal",
      "tradeAmt":"-9.00",
      "currency":"USD",
      "orderNo":"1624138515728646151",
      "tradeTime":"2023-09-21 11:23:23",
      "status":"Success"
    }
  };

  static late String baseUrl;

  //页面来源
  static late String sourcesRouteName;

  //平台类型
  static late String platform;

  ///安全存储的实例
  static FlutterSecureStorage? storage;

  ///全局上下文
  static late BuildContext workContext;

  ///设备标识
  static late String uuid;

  ///app版本信息
  static late String version;

  ///app当前语言
  static late Locale currentLocale;

  ///手机品牌
  static late String brand;

  ///手机型号
  static late String series;

  ///手机系统版本
  static late String systemVersion;
}
