import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:rrworkmanager/common_widget/AppBarWidget.dart';
import 'package:rrworkmanager/page/feedback/m/feedbackSubTypeListApiBean.dart';
import 'package:rrworkmanager/page/feedback/v/SelectCountryPage.dart';
import 'package:rrworkmanager/page/feedback/v/feedbackComplaintNoticePage.dart';
import 'package:rrworkmanager/page/feedback/v/feedbackResultPage.dart';
import 'package:rrworkmanager/page/feedback/vm/feedbackInputViewModel.dart';
import 'package:rrworkmanager/rrworkmanager.dart';
import 'package:rrworkmanager/rrworkmanagerplugin.dart';
import 'package:rrworkmanager/utils/Global.dart';
import 'package:rrworkmanager/utils/StringUtils.dart';

class FeedbackInfoInputPage extends StatefulWidget {
  //工单类型
  final String type;

  //反馈子类型
  final FeedbackSubTypeBean? subType;

  const FeedbackInfoInputPage({Key? key, required this.type, this.subType})
      : super(key: key);

  @override
  _FeedbackInfoInputPageState createState() => _FeedbackInfoInputPageState();
}

class _FeedbackInfoInputPageState extends State<FeedbackInfoInputPage> {
  final FeedbackInputViewModel _model = FeedbackInputViewModel();
  final ImagePicker _picker = ImagePicker();
  TextEditingController descController = TextEditingController();
  TextEditingController phoneController = TextEditingController();

  FocusNode descNode = FocusNode();
  FocusNode phoneNode = FocusNode();

  //凭证图片大小
  final double pictureWidth = (1.sw - 5 * 30.w) * 0.25;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (widget.type == "transaction") {
        _model.requestTransactionComplaintNotice(context, (p0) {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    String title = trans("feedback_label_dysfunction");
    if (widget.type == "product") {
      title = trans("feedback_label_product_recommendations");
    } else if (widget.type == "transaction") {
      title = trans("transaction_title_transaction_complaints");
    }
    return ChangeNotifierProvider<FeedbackInputViewModel>(
      create: (_) => _model,
      child: Scaffold(
        appBar: AppBarUtils(
          title: title,
        ).build(context),
        body: GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
          },
          child: _body(context),
        ),
        backgroundColor: Global.lightBackGroundColor,
      ),
    );
  }

  Widget _body(BuildContext context) {
    return Consumer<FeedbackInputViewModel>(builder: (context, model, child) {
      return SingleChildScrollView(
        child: Column(
          children: [
            _kindReminderWidget(context),
            _feedbackTypeWidget(context),
            _transactionInfoWidget(context),
            _complaintDescWidget(context),
            _credentialsWidget(context),
            _telephoneWidget(context),
            _conditionWidget(context),
            _submitWidget(context),
          ],
        ),
      );
    });
  }

  //温馨提示
  Widget _kindReminderWidget(BuildContext context) {
    if (widget.type == "transaction") {
      return Container(
        width: 1.sw,
        color: Colors.white,
        padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 24.w),
        child: Column(
          children: [
            Container(
              alignment: Alignment.centerLeft,
              child: Text(
                trans("transaction_label_kind_reminder"),
                style: TextStyle(color: Global.mainTextColor, fontSize: 32.sp),
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: 20.w),
              alignment: Alignment.centerLeft,
              child: Text(
                trans("transaction_tips_kind_reminder"),
                style: TextStyle(color: Global.tipsTextColor, fontSize: 26.sp),
              ),
            ),
          ],
        ),
      );
    } else {
      return Container();
    }
  }

  //反馈类型
  Widget _feedbackTypeWidget(BuildContext context) {
    if (widget.subType != null) {
      return Container(
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
        child: Text(
          widget.subType!.typeName!,
          style: TextStyle(color: Global.tipsTextColor, fontSize: 28.sp),
        ),
      );
    }
    return Container();
  }

  //交易信息
  Widget _transactionInfoWidget(BuildContext context) {
    if (widget.type == "transaction") {
      return Consumer<FeedbackInputViewModel>(
          builder: ((context, model, child) {
        Map tradeInfo = RRWorkManagerPlugin.userInfo["tradeInfo"];
        return Container(
          width: 1.sw,
          padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 22.w),
          decoration: const BoxDecoration(color: Colors.white),
          child: Column(
            children: [
              Container(
                margin: EdgeInsets.only(bottom: 16.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      tradeInfo["tradeTypeDesc"],
                      style: TextStyle(
                          color: Global.mainTextColor, fontSize: 32.sp),
                    ),
                    Text(
                      tradeInfo["tradeAmt"] + tradeInfo["currency"],
                      style: TextStyle(
                          color: Global.successColor, fontSize: 32.sp),
                    )
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.only(bottom: 16.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      tradeInfo["tradeTime"],
                      style: TextStyle(
                          color: Global.tipsTextColor, fontSize: 24.sp),
                    ),
                    Text(
                      tradeInfo["status"],
                      style: TextStyle(
                          color: Global.tipsTextColor, fontSize: 24.sp),
                    )
                  ],
                ),
              ),
            ],
          ),
        );
      }));
    }
    return Container();
  }

  //投诉描述
  Widget _complaintDescWidget(BuildContext context) {
    return Consumer<FeedbackInputViewModel>(builder: ((context, model, child) {
      String title = trans("feedback_label_problem_description");
      if (widget.type == "transaction") {
        title = trans("transaction_label_complaints_description");
      }
      return Container(
        width: 1.sw,
        color: Colors.white,
        padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 24.w),
        child: Column(
          children: [
            Container(
              alignment: Alignment.centerLeft,
              child: Text(
                title,
                style: TextStyle(color: Global.mainTextColor, fontSize: 32.sp),
              ),
            ),
            Container(
                margin: EdgeInsets.only(top: 20.w),
                alignment: Alignment.centerLeft,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.r),
                    border:
                        Border.all(width: 1, color: Global.inputBorderColor)),
                child: Column(
                  children: [
                    TextField(
                        focusNode: descNode,
                        style: TextStyle(
                            fontSize: 26.sp, color: Global.mainTextColor),
                        cursorColor: RRWorkManagerConfig.mainColor,
                        controller: descController,
                        maxLength: 200,
                        maxLines: 7,
                        onChanged: (value) {
                          model.setComplaintDesc(value);
                        },
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(200)
                        ],
                        decoration: InputDecoration(
                          hintText: trans("feedback_tips_problem_description"),
                          hintStyle: TextStyle(
                            color: Global.tipsTextColor,
                            fontSize: 26.sp,
                          ),
                          counterStyle: TextStyle(
                              color: Global.tipsTextColor, fontSize: 24.sp),
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 20.w, vertical: 15.w),
                          border: InputBorder.none,
                        )),
                    SizedBox(
                      height: 24.w,
                    )
                  ],
                )),
          ],
        ),
      );
    }));
  }

  //凭证
  Widget _credentialsWidget(BuildContext context) {
    return Consumer<FeedbackInputViewModel>(builder: (context, model, child) {
      String title = trans("feedback_label_upload_credentials");
      //图片限制
      int count = 4;
      if (widget.type == "transaction") {
        title = trans("transaction_label_picture_evidence");
        count = 9;
      }
      return Container(
        width: 1.sw,
        color: Colors.white,
        padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 24.w),
        child: Column(
          children: [
            Container(
              alignment: Alignment.centerLeft,
              child: Text(
                title,
                style: TextStyle(color: Global.mainTextColor, fontSize: 32.sp),
              ),
            ),
            Container(
                margin: EdgeInsets.only(top: 20.w),
                alignment: Alignment.centerLeft,
                child: Wrap(
                  spacing: 30.w,
                  runSpacing: 30.w,
                  children:
                      List.generate(model.evidenceList.length + 1, (index) {
                    if (index == model.evidenceList.length) {
                      return _addPhotoWidget(context, index != count);
                    } else {
                      return _pictureItemWidget(context,
                          image: model.evidenceList[index], ontap: () {
                        showBigPictureDialog(
                            context, model.evidenceList[index]);
                      }, delete: () {
                        model.deletePicture(index);
                      });
                    }
                  }),
                )),
          ],
        ),
      );
    });
  }

  Widget _pictureItemWidget(BuildContext context,
      {required String image, Function? ontap, Function? delete}) {
    return Container(
      width: pictureWidth,
      height: pictureWidth,
      clipBehavior: Clip.hardEdge,
      alignment: Alignment.center,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(8.r)),
      child: Stack(
        children: [
          GestureDetector(
              onTap: () {
                if (ontap != null) {
                  ontap();
                }
              },
              child: image.contains("https")
                  ? Image.network(image,
                      width: pictureWidth, fit: BoxFit.contain)
                  : Image.file(File(image),
                      width: pictureWidth,
                      height: pictureWidth,
                      fit: BoxFit.fill)),
          Positioned(
              right: 0,
              child: GestureDetector(
                onTap: () {
                  if (delete != null) {
                    delete();
                  }
                },
                child: Image.asset(
                  "resources/image/icon_close.png",
                  width: 40.w,
                  package: Global.package,
                ),
              ))
        ],
      ),
    );
  }

  Widget _addPhotoWidget(BuildContext context, bool isAdd) {
    if (isAdd) {
      return Consumer<FeedbackInputViewModel>(builder: (context, model, child) {
        String length = "4";
        if (widget.type == "transaction") {
          length = "10";
        }
        return GestureDetector(
          onTap: () {
            _showPickPhotoMethod(context);
          },
          child: Container(
            width: pictureWidth,
            height: pictureWidth,
            alignment: Alignment.center,
            decoration: BoxDecoration(
                color: Global.inputBorderColor,
                borderRadius: BorderRadius.circular(8.r)),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  "resources/image/icon_camera.png",
                  width: 48.w,
                  package: Global.package,
                ),
                Container(
                  margin: EdgeInsets.only(top: 8.w),
                  child: Text(
                    "${model.evidenceList.length}/$length",
                    style:
                        TextStyle(color: Global.tipsTextColor, fontSize: 24.sp),
                  ),
                )
              ],
            ),
          ),
        );
      });
    }
    return Container();
  }

  //联系电话
  Widget _telephoneWidget(BuildContext context) {
    return Consumer<FeedbackInputViewModel>(builder: (context, model, child) {
      return Container(
        width: 1.sw,
        color: Colors.white,
        margin: EdgeInsets.only(top: 24.w, bottom: 40.w),
        padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 24.w),
        child: Column(
          children: [
            Container(
              alignment: Alignment.centerLeft,
              child: Text(
                trans("feedback_label_telephone"),
                style: TextStyle(color: Global.mainTextColor, fontSize: 32.sp),
              ),
            ),
            Container(
              height: 88.w,
              margin: EdgeInsets.only(top: 20.w, bottom: 14.w),
              child: Row(
                children: [
                  GestureDetector(
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute<Map>(
                              builder: (BuildContext context) =>
                                  SelectCountryPage(
                                areaCode: model.countryCode,
                                mobileNationId: model.mobileNationId,
                                country: model.country,
                              ),
                            )).then((value) {
                          if (value != null) {
                            _model.setCountryCode(value["areaCode"]);
                          }
                        });
                      },
                      child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 24.w),
                          margin: EdgeInsets.only(right: 10.w),
                          alignment: Alignment.centerLeft,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.r),
                              border: Border.all(
                                  width: 1, color: Global.inputBorderColor)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                margin: EdgeInsets.only(right: 13.w),
                                child: Text(
                                  "+${model.countryCode}",
                                  style: TextStyle(
                                    color: RRWorkManagerConfig.mainColor,
                                    fontSize: 32.sp,
                                  ),
                                ),
                              ),
                              Image.asset(
                                "resources/image/icon_selector_area.png",
                                width: 12.w,
                                color: RRWorkManagerConfig.mainColor,
                                package: Global.package,
                              )
                            ],
                          ))),
                  Expanded(
                      child: Container(
                    height: 88.w,
                    alignment: Alignment.centerLeft,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(
                            width: 1, color: Global.inputBorderColor)),
                    child: TextField(
                        focusNode: phoneNode,
                        style: TextStyle(
                            fontSize: 32.sp, color: Global.mainTextColor),
                        cursorColor: Global.mainTextColor,
                        controller: phoneController,
                        keyboardType: TextInputType.number,
                        onChanged: (value) {
                          model.setTelephone(value);
                        },
                        inputFormatters: [LengthLimitingTextInputFormatter(15)],
                        decoration: InputDecoration(
                          focusColor: Colors.amber,
                          hintText: trans("feedback_tips_telephone"),
                          hintStyle: TextStyle(
                            color: Global.tipsTextColor,
                            fontSize: 26.sp,
                          ),
                          contentPadding: EdgeInsets.only(
                              left: 20.w, right: 20.w, bottom: 10.w),
                          border: InputBorder.none,
                        )),
                  ))
                ],
              ),
            ),
            Container(
              alignment: Alignment.centerLeft,
              child: Text(
                trans("feedback_warn_fill_telephone"),
                style: TextStyle(color: Global.tipsTextColor, fontSize: 22.sp),
              ),
            ),
          ],
        ),
      );
    });
  }

  //协议须知
  Widget _conditionWidget(BuildContext context) {
    if (widget.type == "transaction") {
      return Consumer<FeedbackInputViewModel>(builder: (context, model, child) {
        TapGestureRecognizer tap = TapGestureRecognizer();
        tap.onTap = () {
          Navigator.push(
              context,
              MaterialPageRoute<Map>(
                builder: (BuildContext context) => FeedbackComplaintNoticePage(
                  conditionTitle: trans("transaction_title_notice_middle"),
                  conditionContent: model.notice,
                ),
              ));
        };
        return Container(
          margin: EdgeInsets.only(bottom: 24.w),
          padding: EdgeInsets.symmetric(horizontal: 30.w),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              menuCheckBox(
                  select: model.isAgree,
                  onTap: () {
                    model.setIsAgree(!model.isAgree);
                  }),
              Expanded(
                  child: Container(
                margin: EdgeInsets.only(top: 4.w),
                child: Text.rich(TextSpan(children: [
                  TextSpan(
                      text: trans("transaction_label_notice_first"),
                      style: TextStyle(
                          color: Global.tipsTextColor, fontSize: 26.sp)),
                  TextSpan(
                      text: trans("transaction_label_notice_middle"),
                      style: TextStyle(
                          color: Global.successColor, fontSize: 26.sp),
                      recognizer: tap),
                  TextSpan(
                      text: trans("transaction_label_notice_last"),
                      style: TextStyle(
                          color: Global.tipsTextColor, fontSize: 26.sp)),
                ])),
              ))
            ],
          ),
        );
      });
    }
    return Container();
  }

  Widget menuCheckBox({bool select = false, required Function onTap}) {
    return GestureDetector(
      onTap: () {
        onTap();
      },
      child: Container(
        margin: EdgeInsets.only(right: 10.w),
        width: 34.w,
        height: 34.w,
        child: Image.asset(
          "resources/image/${select ? 'icon_pitchon.png' : 'icon_unselected.png'}",
          package: Global.package,
        ),
      ),
    );
  }

  //提交
  Widget _submitWidget(BuildContext context) {
    return Consumer<FeedbackInputViewModel>(builder: (context, model, child) {
      return GestureDetector(
        onTap: () async {
          if (model.isCanSubmit) {
            bool isUploadSuccess = await model.uploadAllImage(context);
            if (isUploadSuccess) {
              model.requestWorkOrderInfoCreate(context,
                  workOrderType: widget.type,
                  workOrderSubType: widget.subType?.id, success: (result) {
                Navigator.push(
                    context,
                    MaterialPageRoute<void>(
                      builder: (BuildContext context) => FeedbackResultPage(
                        type: widget.type,
                      ),
                    ));
              });
            }
          }
        },
        child: Container(
          width: 1.sw - 60.w,
          height: 88.w,
          margin: EdgeInsets.only(bottom: 30.w),
          alignment: Alignment.center,
          decoration: BoxDecoration(
              color: model.isCanSubmit
                  ? RRWorkManagerConfig.submitBtnColor
                  : RRWorkManagerConfig.submitBtnColor.withOpacity(0.06),
              borderRadius: BorderRadius.circular(8.r)),
          child: Text(
            trans("common_btn_submit"),
            style: TextStyle(color: Colors.white, fontSize: 36.sp),
          ),
        ),
      );
    });
  }

  ///查看图片弹窗
  void showBigPictureDialog(BuildContext context, String url) {
    bool isLocalFile = true;
    if (url.contains("http")) {
      isLocalFile = false;
    }
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return Column(mainAxisAlignment: MainAxisAlignment.center, children: [
            Container(
              alignment: Alignment.bottomCenter,
              margin: EdgeInsets.only(bottom: 200.w),
              child: SizedBox(
                width: 600.w,
                height: 380.w,
                child: isLocalFile
                    ? Image.file(File(url))
                    : Image(
                        image: NetworkImage(url, headers: {
                          "token": RRWorkManagerPlugin.userInfo["token"]
                        }),
                        fit: BoxFit.contain),
              ),
            ),
            GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Image.asset(
                "resources/image/popup_close.png",
                package: Global.package,
              ),
            ),
          ]);
        });
  }

  //照片选择方式
  void _showPickPhotoMethod(BuildContext inputContext) {
    List addTypes = [];
    addTypes = [
      {"type": "1", "typeDesc": trans("common_btn_capture")},
      {"type": "2", "typeDesc": trans("common_btn_select_from_camera")}
    ];
    showModalBottomSheet(
        context: inputContext,
        isDismissible: true,
        backgroundColor: Colors.black.withOpacity(0.03),
        builder: (context) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                width: 1.sw - 60.w,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4.r)),
                child: Column(
                  children: List.generate(addTypes.length, (index) {
                    return GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                        pickPhoto(addTypes[index]["type"]);
                      },
                      child: Container(
                        height: 88.w,
                        width: 1.sw - 70.w,
                        alignment: Alignment.center,
                        child: Text(
                          addTypes[index]["typeDesc"],
                          style: TextStyle(
                              color: Global.mainTextColor, fontSize: 32.sp),
                        ),
                      ),
                    );
                  }),
                ),
              ),
              GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  width: 1.sw - 60.w,
                  height: 88.w,
                  margin: EdgeInsets.only(top: 40.w, bottom: 40.w),
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.r)),
                  child: Text(
                    trans("common_btn_cancel"),
                    style:
                        TextStyle(color: Global.mainTextColor, fontSize: 32.sp),
                  ),
                ),
              )
            ],
          );
        });
  }

  void pickPhoto(String type) async {
    XFile? res;
    if (type == "2") {
      //相册
      res = await _picker.pickImage(
          source: ImageSource.gallery, imageQuality: 50);
    } else {
      //拍照
      res =
          await _picker.pickImage(source: ImageSource.camera, imageQuality: 50);
    }
    if (res != null) {
      res.length().then((value) {
        print("图片文件的大小：========$value");
      });
      _model.addPicture(res.path);
    }
  }
}
