import 'package:dio/dio.dart';

///日志拦截器
class LogInterceptors extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    print("\n====================== 请求数据 ======================");
    print("method = ${options.method.toString()}");
    print("url = ${options.uri.toString()}");
    print("headers = ${options.headers}");
    print("params = ${options.queryParameters}");
    print("data = ${options.data}");
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    print("\n====================== 响应数据开始 ======================");
    print("code = ${response.statusCode}");
    print("data = ${response.data}");
    print("headers = ${response.headers}");
    print("====================== 响应数据结束 ======================\n");
    super.onResponse(response, handler);
  }

  @override
  void onError(DioError err, ErrorInterceptorHandler handler) {
    print("\n====================== 错误响应数据 ======================");
    print("type = ${err.type}");
    print("message = ${err.message}");
    // print("stackTrace = ${err.stackTrace}");
    print("\n");
    super.onError(err, handler);
  }
}
