PODS:
  - device_info (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_secure_storage (3.3.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - GMObjC (3.3.2):
    - GMOpenSSL
  - GMOpenSSL (3.0.0)
  - image_picker_ios (0.0.1):
    - Flutter
  - package_info (0.0.1):
    - Flutter
  - rrworkmanager (0.0.1):
    - Flutter
    - GMObjC
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - Toast (4.0.0)
  - video_player_avfoundation (0.0.1):
    - Flutter
  - wakelock (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - device_info (from `.symlinks/plugins/device_info/ios`)
  - Flutter (from `Flutter`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - package_info (from `.symlinks/plugins/package_info/ios`)
  - rrworkmanager (from `.symlinks/plugins/rrworkmanager/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/ios`)
  - wakelock (from `.symlinks/plugins/wakelock/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - GMObjC
    - GMOpenSSL
    - Toast

EXTERNAL SOURCES:
  device_info:
    :path: ".symlinks/plugins/device_info/ios"
  Flutter:
    :path: Flutter
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  package_info:
    :path: ".symlinks/plugins/package_info/ios"
  rrworkmanager:
    :path: ".symlinks/plugins/rrworkmanager/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/ios"
  wakelock:
    :path: ".symlinks/plugins/wakelock/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  device_info: d7d233b645a32c40dfdc212de5cf646ca482f175
  Flutter: 50d75fe2f02b26cc09d224853bb45737f8b3214a
  flutter_secure_storage: 7953c38a04c3fdbb00571bcd87d8e3b5ceb9daec
  fluttertoast: 6122fa75143e992b1d3470f61000f591a798cc58
  GMObjC: 5e251e7ab2bf8127dda70f90e827a5323137ca7f
  GMOpenSSL: 753ecadaa28028dcce9e0ca250f126a33696ad58
  image_picker_ios: b786a5dcf033a8336a657191401bfdf12017dabb
  package_info: 873975fc26034f0b863a300ad47e7f1ac6c7ec62
  rrworkmanager: 2c75aa79154f0f4171af4c7eed4d6cbfed5a4831
  shared_preferences_foundation: 297b3ebca31b34ec92be11acd7fb0ba932c822ca
  Toast: 91b396c56ee72a5790816f40d3a94dd357abc196
  video_player_avfoundation: e489aac24ef5cf7af82702979ed16f2a5ef84cff
  wakelock: d0fc7c864128eac40eba1617cb5264d9c940b46f
  webview_flutter_wkwebview: b7e70ef1ddded7e69c796c7390ee74180182971f

PODFILE CHECKSUM: 1d62be80675f50e63eb2f851d55ee75f09951c1b

COCOAPODS: 1.11.3
