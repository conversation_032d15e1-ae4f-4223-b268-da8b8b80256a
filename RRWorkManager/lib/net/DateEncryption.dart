import 'dart:convert';

import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:flutter/services.dart';
import 'package:pointycastle/asymmetric/api.dart';
import 'package:rrworkmanager/utils/Global.dart';

///加密工具类
class DateEncryption {
  ///加密
  static Future<String> encode(Map<String, dynamic> map) async {
    String dataJson = json.encode(map);

    if (Global.smSwitch) {
      MethodChannel gmPlugin = const MethodChannel('rrworkmanager');
      Map<String, dynamic> encryptMap = {
        "dataJson": dataJson,
      };
      String signature =
          await gmPlugin.invokeMethod('SM3Signature', encryptMap);

      print("====signature=======$signature==========");

      return dataJson + signature;
    } else {
      //转字节
      List<int> byte = utf8.encode(dataJson);
      // print("转成字节:$byte");

      //sha256
      List<int> sha256Byte = sha256.convert(byte).bytes;
      // print("转成256:$sha256Byte");

      //转16进制
      String sha25616 = to16(sha256Byte);

      //MD5加密
      List<int> md5Byte = md5.convert(utf8.encode(sha25616)).bytes;
      // print("转成MD5:$md5Byte");

      //转16进制
      String md5To16 = to16(md5Byte);
      // print("16进制 $md5To16");

      //转大写
      String upperStr = md5To16.toUpperCase();
      // print("大写 $upperStr");
      return dataJson + upperStr;
    }
  }

  ///转16进制
  static String to16(List<int> byte) {
    var concatenate = StringBuffer();
    for (int i in byte) {
      concatenate.write('${i < 16 ? '0' : ''}${i.toRadixString(16)}');
    }
  
    List<int> to16Type = utf8.encode(concatenate.toString());

    //转ascii
    AsciiCodec ac = const AsciiCodec();
    String asciiStr = ac.decode(to16Type);

    return asciiStr;
  }

  ///公钥加密长字符
  static Future<String> publicKeyEncodeStr(
      String publicKeyStr, String contentStr) async {
    if (contentStr.isEmpty) {
      return "";
    }

    if (Global.smSwitch) {
      MethodChannel gmPlugin = const MethodChannel('rrworkmanager');
      Map<String, dynamic> encryptMap = {
        "contentStr": contentStr,
        "gPubkey": publicKeyStr,
      };
      String encryptedStr =
          await gmPlugin.invokeMethod('SM2Encrypt', encryptMap);
      print("=======encryptedStr=============$encryptedStr==============");
      //拼接字符
      contentStr = "02|04$encryptedStr";

      //转byte
      List<int> encode = utf8.encode(contentStr);

      //base64加密
      contentStr = base64Encode(encode);

      return contentStr;
    } else {
      publicKeyStr =
          "-----BEGIN PUBLIC KEY-----\n$publicKeyStr\n-----END PUBLIC KEY-----";

      //公钥加密
      RSAPublicKey publicKey = RSAKeyParser().parse(publicKeyStr) as RSAPublicKey;
      Encrypter encrypter = Encrypter(RSA(publicKey: publicKey));

      // 原始json转成字节数组
      List<int> sourceByts = utf8.encode(contentStr);
      // 数据长度
      int inputLen = sourceByts.length;
      // 加密最大长度
      int maxLen = 117;
      // 存放加密后的字节数组
      List<int> totalByts = [];
      // 分段加密 步长为117
      for (var i = 0; i < inputLen; i += maxLen) {
        // 还剩多少字节长度
        int endLen = inputLen - i;
        List<int> item;
        if (endLen > maxLen) {
          item = sourceByts.sublist(i, i + maxLen);
        } else {
          item = sourceByts.sublist(i, i + endLen);
        }
        // 加密后的对象转换成字节数组再存放到容器
        totalByts.addAll(encrypter.encryptBytes(item).bytes);
      }
      //加密后的字节数组转换成base64编码并返回
      //字节转Base64
      String publicBase64 = base64Encode(totalByts);

      //拼接字符
      contentStr = "01|$publicBase64";

      //转byte
      List<int> encode = utf8.encode(contentStr);

      //base64加密
      contentStr = base64Encode(encode);

      return contentStr;
    }
  }

  ///公钥加密字符-原方法，这种方法在单个字符超过82个汉字，字符超过255个字符的时候会加密失败
  // static Future<String> publicKeyEncodeLongStr(
  //     String publicKeyStr, String contentStr) async {
  //   if (contentStr.isEmpty) {
  //     return "";
  //   }

  //   if (Global.smSwitch) {
  //     MethodChannel gmPlugin = const MethodChannel('gmPlugin');
  //     Map<String, dynamic> encryptMap = {
  //       "contentStr": contentStr,
  //       "gPubkey": publicKeyStr,
  //     };
  //     String encryptedStr =
  //         await gmPlugin.invokeMethod('SM2Encrypt', encryptMap);
  //     print("=======encryptedStr=============$encryptedStr==============");
  //     //拼接字符
  //     contentStr = "02|04$encryptedStr";

  //     //转byte
  //     List<int> encode = utf8.encode(contentStr);

  //     //base64加密
  //     contentStr = base64Encode(encode);

  //     return contentStr;
  //   } else {
  //     publicKeyStr =
  //         "-----BEGIN PUBLIC KEY-----\n$publicKeyStr\n-----END PUBLIC KEY-----";

  //     //公钥加密
  //     RSAPublicKey publicKey = RSAKeyParser().parse(publicKeyStr);
  //     Encrypter publicEn = Encrypter(RSA(publicKey: publicKey));
  //     List<int> publicByte = publicEn.encrypt(contentStr).bytes;

  //     //字节转Base64
  //     String publicBase64 = base64Encode(publicByte);

  //     //拼接字符
  //     contentStr = "01|$publicBase64";

  //     //转byte
  //     List<int> encode = utf8.encode(contentStr);

  //     //base64加密
  //     contentStr = base64Encode(encode);

  //     return contentStr;
  //   }
  // }
}
