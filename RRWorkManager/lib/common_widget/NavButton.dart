import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rrworkmanager/utils/Global.dart';

class NavButton extends StatelessWidget {
  ///点击方法
  final Function? onPressed;

  ///图标地址
  final String? iconRes;

  ///button宽高
  final double? btnWidth;
  final double? btnHeight;

  ///图标宽高
  final double? iconWidth;
  final double? iconHeight;

  final bool hasFlag;

  const NavButton({
    Key? key,
    this.onPressed,
    this.iconRes,
    this.btnWidth,
    this.btnHeight,
    this.iconWidth,
    this.iconHeight,
    this.hasFlag = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: btnHeight ?? 48.w,
      width: btnWidth ?? 48.w,
      child: GestureDetector(
        child: hasFlag
            ? Stack(
                children: [
                  Container(
                    margin: EdgeInsets.only(
                        top: 4.w),
                    height: iconHeight ?? 48.w,
                    width: iconWidth ?? 48.w,
                    child: Image.asset(
                      iconRes!,
                      width: iconWidth ?? 48.w,
                      height: iconHeight ?? 48.w,
                      fit: BoxFit.cover,
                    ),
                  ),
                  Positioned(
                    top: 0,
                    right: 0,
                    child: Icon(Icons.brightness_1_rounded,
                        size: 18.w, color: Global.errorColor),
                  ),
                ],
              )
            : Container(
                child: Image.asset(
                  iconRes!,
                  package: "rrworkmanager",
                  width: iconWidth ?? 48.w,
                  height: iconHeight ?? 48.w,
                ),
              ),
        onTap: () {
          if(onPressed != null) {
            onPressed!();
          }
        },
      ),
    );
  }
}
