PODS:
  - CRBoxInputView (1.1.7):
    - Masonry
  - CWLateralSlide (1.6.5)
  - device_info (0.0.1):
    - Flutter
  - FirebaseCore (8.9.0):
    - FirebaseCoreDiagnostics (~> 8.0)
    - GoogleUtilities/Environment (~> 7.6)
    - GoogleUtilities/Logger (~> 7.6)
  - FirebaseCoreDiagnostics (8.15.0):
    - GoogleDataTransport (~> 9.1)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
    - nanopb (~> 2.30908.0)
  - FirebaseInstallations (8.15.0):
    - FirebaseCore (~> 8.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/UserDefaults (~> 7.7)
    - PromisesObjC (< 3.0, >= 1.2)
  - FirebaseMessaging (8.9.0):
    - FirebaseCore (~> 8.0)
    - FirebaseInstallations (~> 8.0)
    - GoogleDataTransport (~> 9.1)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.6)
    - GoogleUtilities/Environment (~> 7.6)
    - GoogleUtilities/Reachability (~> 7.6)
    - GoogleUtilities/UserDefaults (~> 7.6)
    - nanopb (~> 2.30908.0)
  - Flutter (1.0.0)
  - flutter_secure_storage (3.3.1):
    - Flutter
  - FlutterPluginRegistrant (0.0.1):
    - device_info
    - Flutter
    - flutter_secure_storage
    - fluttertoast
    - image_picker_ios
    - package_info
    - rrworkmanager
    - shared_preferences_foundation
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - GMObjC (4.0.3):
    - GMOpenSSL (~> 3.1.2)
  - GMOpenSSL (3.1.2)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (6.2.1):
    - GoogleMaps/Maps (= 6.2.1)
  - GoogleMaps/Base (6.2.1)
  - GoogleMaps/Maps (6.2.1):
    - GoogleMaps/Base
  - GooglePlaces (6.2.1)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - image_picker_ios (0.0.1):
    - Flutter
  - IQKeyboardManager (6.5.19)
  - LBXScan/LBXNative (2.5.1):
    - LBXScan/Types (~> 2.2)
  - LBXScan/Types (2.5.1)
  - LBXScan/UI (2.5.1):
    - LBXScan/Types (~> 2.2)
  - lottie-ios (2.5.3)
  - Masonry (1.1.0)
  - MBProgressHUD (1.2.0)
  - MJExtension (3.4.2)
  - MJRefresh (3.7.9)
  - nanopb (2.30908.0):
    - nanopb/decode (= 2.30908.0)
    - nanopb/encode (= 2.30908.0)
  - nanopb/decode (2.30908.0)
  - nanopb/encode (2.30908.0)
  - package_info (0.0.1):
    - Flutter
  - PromisesObjC (2.4.0)
  - rrworkmanager (0.0.1):
    - Flutter
  - RRXNNetwork (1.1.0):
    - MJExtension (~> 3.4.0)
  - SDCycleScrollView (1.82):
    - SDWebImage (>= 5.0.0)
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - Toast (4.1.1)

DEPENDENCIES:
  - CRBoxInputView (~> 1.1.7)
  - CWLateralSlide (~> 1.6.5)
  - device_info (from `xwalletpro_flutter/.ios/.symlinks/plugins/device_info/ios`)
  - FirebaseCore (= 8.9.0)
  - FirebaseMessaging (= 8.9.0)
  - Flutter (from `xwalletpro_flutter/.ios/Flutter`)
  - flutter_secure_storage (from `xwalletpro_flutter/.ios/.symlinks/plugins/flutter_secure_storage/ios`)
  - FlutterPluginRegistrant (from `xwalletpro_flutter/.ios/Flutter/FlutterPluginRegistrant`)
  - fluttertoast (from `xwalletpro_flutter/.ios/.symlinks/plugins/fluttertoast/ios`)
  - GMObjC (= 4.0.3)
  - GMOpenSSL (~> 3.1.2)
  - GoogleMaps
  - GooglePlaces
  - image_picker_ios (from `xwalletpro_flutter/.ios/.symlinks/plugins/image_picker_ios/ios`)
  - IQKeyboardManager (~> 6.5.4)
  - LBXScan/LBXNative
  - LBXScan/UI
  - lottie-ios (= 2.5.3)
  - Masonry
  - MBProgressHUD
  - MJExtension (~> 3.4.0)
  - MJRefresh
  - package_info (from `xwalletpro_flutter/.ios/.symlinks/plugins/package_info/ios`)
  - rrworkmanager (from `xwalletpro_flutter/.ios/.symlinks/plugins/rrworkmanager/ios`)
  - RRXNNetwork (from `./RRXNNetwork`)
  - SDCycleScrollView (>= 1.80)
  - SDWebImage
  - shared_preferences_foundation (from `xwalletpro_flutter/.ios/.symlinks/plugins/shared_preferences_foundation/darwin`)

SPEC REPOS:
  https://github.com/CocoaPods/Specs.git:
    - CRBoxInputView
    - CWLateralSlide
    - FirebaseCore
    - FirebaseCoreDiagnostics
    - FirebaseInstallations
    - FirebaseMessaging
    - GMObjC
    - GMOpenSSL
    - GoogleDataTransport
    - GoogleMaps
    - GooglePlaces
    - GoogleUtilities
    - IQKeyboardManager
    - LBXScan
    - lottie-ios
    - Masonry
    - MBProgressHUD
    - MJExtension
    - MJRefresh
    - nanopb
    - PromisesObjC
    - SDCycleScrollView
    - SDWebImage
    - Toast

EXTERNAL SOURCES:
  device_info:
    :path: xwalletpro_flutter/.ios/.symlinks/plugins/device_info/ios
  Flutter:
    :path: xwalletpro_flutter/.ios/Flutter
  flutter_secure_storage:
    :path: xwalletpro_flutter/.ios/.symlinks/plugins/flutter_secure_storage/ios
  FlutterPluginRegistrant:
    :path: xwalletpro_flutter/.ios/Flutter/FlutterPluginRegistrant
  fluttertoast:
    :path: xwalletpro_flutter/.ios/.symlinks/plugins/fluttertoast/ios
  image_picker_ios:
    :path: xwalletpro_flutter/.ios/.symlinks/plugins/image_picker_ios/ios
  package_info:
    :path: xwalletpro_flutter/.ios/.symlinks/plugins/package_info/ios
  rrworkmanager:
    :path: xwalletpro_flutter/.ios/.symlinks/plugins/rrworkmanager/ios
  RRXNNetwork:
    :path: "./RRXNNetwork"
  shared_preferences_foundation:
    :path: xwalletpro_flutter/.ios/.symlinks/plugins/shared_preferences_foundation/darwin

SPEC CHECKSUMS:
  CRBoxInputView: 72b35e2f3df36739a444eadefc9ef0dbf1fe576e
  CWLateralSlide: 8939c8dea91a24e40d0793553118cb57cd67f64b
  device_info: 52e8c0c9c61def8d0a92bf175f5f500abbea04bc
  FirebaseCore: 599ee609343eaf4941bd188f85e3aa077ffe325b
  FirebaseCoreDiagnostics: 92e07a649aeb66352b319d43bdd2ee3942af84cb
  FirebaseInstallations: 40bd9054049b2eae9a2c38ef1c3dd213df3605cd
  FirebaseMessaging: 82c4a48638f53f7b184f3cc9f6cd2cbe533ab316
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_secure_storage: 50035aef357c5a8bdd67fd6bc81370d46efc4d16
  FlutterPluginRegistrant: f7ab17b48aa7d1e88d66d2347a6881736cae9bc9
  fluttertoast: 3a276f62eb0a34f7736f6d8f105e436faf19468e
  GMObjC: 7c4671eed727b18de85a15ef9e90b9be82fbe5c1
  GMOpenSSL: 1307e0778146502299bc84d9a0a1b2f7789a1e0c
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMaps: 20d7b12be49a14287f797e88e0e31bc4156aaeb4
  GooglePlaces: 94974aa119573d5acc2a35a699948dac838abd73
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  IQKeyboardManager: c8665b3396bd0b79402b4c573eac345a31c7d485
  LBXScan: 90ca10d0c38fb4a5a6980d7782354f3f69f50093
  lottie-ios: a50d5c0160425cd4b01b852bb9578963e6d92d31
  Masonry: 678fab65091a9290e40e2832a55e7ab731aad201
  MBProgressHUD: 3ee5efcc380f6a79a7cc9b363dd669c5e1ae7406
  MJExtension: e97d164cb411aa9795cf576093a1fa208b4a8dd8
  MJRefresh: ff9e531227924c84ce459338414550a05d2aea78
  nanopb: a0ba3315591a9ae0a16a309ee504766e90db0c96
  package_info: cce50adca9873c79f931618469d2114b91d71189
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  rrworkmanager: fbbf5f7452386837102aebe2c2b0ed330813beb7
  RRXNNetwork: 46093e24f1db5eec116e1d6e08bd787f893086c5
  SDCycleScrollView: a0d74c3384caa72bdfc81470bdbc8c14b3e1fbcf
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e

PODFILE CHECKSUM: d8e11f4cad227c98547c63c0f667eefea787e340

COCOAPODS: 1.16.2
