import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:rrworkmanager/common_widget/AppBarWidget.dart';
import 'package:rrworkmanager/page/feedback/m/feedbackSubTypeListApiBean.dart';
import 'package:rrworkmanager/page/feedback/v/feedbackInfoInputPage.dart';
import 'package:rrworkmanager/page/feedback/vm/feedbackDysfunctionViewMode.dart';
import 'package:rrworkmanager/utils/Global.dart';
import 'package:rrworkmanager/utils/StringUtils.dart';

class FeedbackDysfunctionPage extends StatefulWidget {
  
  //子工单列表
  final List<FeedbackSubTypeBean> subTypes;

  //主工单类型
  final String workOrderType;

  const FeedbackDysfunctionPage(
      {Key? key, required this.subTypes, required this.workOrderType})
      : super(key: key);

  @override
  _FeedbackDysfunctionPageState createState() =>
      _FeedbackDysfunctionPageState();
}

class _FeedbackDysfunctionPageState extends State<FeedbackDysfunctionPage> {
  final FeedbackDysfunctionViewModel _model = FeedbackDysfunctionViewModel();

  @override
  void initState() {
    super.initState();
    _model.subTypeList.addAll(widget.subTypes);
  }

  @override
  Widget build(BuildContext context) {
    String title = trans("feedback_label_dysfunction");
    if (widget.workOrderType == "transaction") {
      title = trans("transaction_title_transaction_complaints");
    }
    return ChangeNotifierProvider<FeedbackDysfunctionViewModel>(
      create: (_) => _model,
      child: Scaffold(
        appBar: AppBarUtils(title: title).build(context),
        body: _body(context),
        backgroundColor: Global.lightBackGroundColor,
      ),
    );
  }

  Widget _body(BuildContext context) {
    return Consumer<FeedbackDysfunctionViewModel>(
        builder: (context, model, child) {
      String tips = trans("feedback_tips_select_type");
      if (widget.workOrderType == "transaction") {
        tips = trans("transaction_tips_select_type");
      }
      return Container(
        padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
        child: Column(
          children: [
            Container(
              alignment: Alignment.centerLeft,
              margin: EdgeInsets.only(bottom: 24.w),
              child: Text(
                tips,
                style: TextStyle(color: Global.tipsTextColor, fontSize: 24.sp),
              ),
            ),
            SingleChildScrollView(
              child: Column(
                children: List.generate(model.subTypeList.length, (index) {
                  FeedbackSubTypeBean bean = model.subTypeList[index];
                  return GestureDetector(
                    onTap: () {
                      Navigator.push(
                          context,
                          MaterialPageRoute<void>(
                            builder: (BuildContext context) =>
                                FeedbackInfoInputPage(
                              type: widget.workOrderType,
                              subType: bean,
                            ),
                          ));
                    },
                    child: _itemWidget(bean),
                  );
                }),
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _itemWidget(FeedbackSubTypeBean bean) {
    return Container(
      height: 88.w,
      margin: EdgeInsets.only(bottom: 24.w),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(8.r)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            margin: EdgeInsets.only(left: 24.w),
            child: Text(
              bean.typeName!,
              style: TextStyle(color: Global.mainTextColor, fontSize: 32.sp),
            ),
          ),
          const Spacer(),
          Container(
            width: 30.w,
            margin: EdgeInsets.only(right: 24.w),
            child: Image.asset(
              "resources/image/icon_right.png",
              package: Global.package,
            ),
          )
        ],
      ),
    );
  }
}
