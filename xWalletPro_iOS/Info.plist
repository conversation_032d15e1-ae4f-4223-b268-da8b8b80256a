<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleDisplayName</key>
	<string>OneMoney Mobile</string>
	<key>CFBundleName</key>
	<string>OneMoney Mobile</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>mfswallet</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>mfswallet</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>1000</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>payment.bankrakyat.com.my</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSExceptionRequiresForwardSecrecy</key>
				<false/>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
			<key>www.hlbepay.com.my</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSExceptionRequiresForwardSecrecy</key>
				<false/>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>we need to access your camera for scanning QR code,Allow?</string>
	<key>NSFaceIDUsageDescription</key>
	<string>we need to access your Face ID to unlock account,Allow?</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>OneMoney Mobile need to access your Location,and provide information about nearby merchants?</string>
	<key>NSLocationUsageDescription</key>
	<string>OneMoney Mobile need to access your Location,and provide information about nearby merchants?</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>OneMoney Mobile need to access your Location,and provide information about nearby merchants?</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>We need to access your photo library to save the photos.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We need to access your photo library to view and select photos.</string>
	<key>NSContactsUsageDescription</key>
	<string>We need to access your contact information to provide better service.</string>
	<key>UIBackgroundModes</key>
	<array/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleDefault</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string></string>
	</array>
</dict>
</plist>
