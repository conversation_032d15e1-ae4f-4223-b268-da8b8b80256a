import 'package:flutter/material.dart';
import 'package:rrworkmanager/base/BaseViewModel.dart';
import 'package:rrworkmanager/config/StaticFieldConfig.dart';
import 'package:rrworkmanager/net/HttpUtils.dart';
import 'package:rrworkmanager/page/records/m/feedbackRecordDetailsApiBean.dart';
import 'package:rrworkmanager/page/records/m/feedbackRecordsApiBean.dart';
import 'package:rrworkmanager/utils/StringUtils.dart';
import 'package:rrworkmanager/utils/ToastUtils.dart';

class FeedbackRecordDetailsViewModel extends BaseViewModel {
  FeedbackRecordDetailsDataBean? detailsBean;

  late FeedbackRecordBean recordBean;

  //星级评分
  double rat = 0;

  void setStarRating(double value) {
    rat = value;
    notifyListeners();
  }

  //工单详情
  Map<String, String> get problemInfo {
    String telephone =
        "+" + detailsBean!.phoneAreaCode! + detailsBean!.customerPhone!;
    String workOrderType = "";
    if (recordBean.workOrderType! == "AF") {
      workOrderType = trans("feedback_label_dysfunction");
    } else if (recordBean.workOrderType! == "PS") {
      workOrderType = trans("feedback_label_product_recommendations");
    } else {
      workOrderType = trans("transaction_title_transaction_complaints");
    }
    if (recordBean.workOrderSubType != null &&
        recordBean.workOrderSubType!.isNotEmpty) {
      workOrderType += "-" + recordBean.workOrderSubType!;
    }
    return {
      trans("records_label_work_order_number"): recordBean.workOrderId!,
      trans("records_label_problem_type"): workOrderType,
      trans("records_label_submission_time"): recordBean.createTime!,
      trans("feedback_label_problem_description"): recordBean.description!,
      trans("feedback_label_telephone"): telephone,
    };
  }

  //凭证
  List<String> get credentials => detailsBean!.voucherList!;

  //处理进度
  Map<String, String> get processInfo {
    Map<String, String>? info;
    String status = trans("records_label_status_received");
    if (recordBean.status == "2") {
      status = trans("records_label_status_completed");
    } else if (recordBean.status == "1") {
      status = trans("records_label_status_following_up");
    }

    info = {
      trans("records_label_current_status"): status,
    };

    // if (recordBean.status == "2") {
    //   status = trans("records_label_status_completed");
    //   info[trans("records_label_processing_time")] =
    //       detailsBean!.processRecord!.overTime ?? "";
    //   info[trans("records_label_processing_result")] =
    //       detailsBean!.processRecord!.description ?? "";
    // }
    return info;
  }

  ///查询记录详情
  void requestWorkOrderRecordDetailsInfo(
      BuildContext context, String workOrderId) {
    HttpUtils.getInstance().posHttp<FeedbackRecordDetailsApiBean>(
        context, UrlConfig.workOrderRecordDetailsQuery, {
      RequestParamConfig.workOrderId: workOrderId,
    }, (data) {
      FeedbackRecordDetailsApiBean apiBean = data;
      detailsBean = apiBean.data!;
      if(detailsBean!.satisfactionRating != null) {
        rat = detailsBean!.satisfactionRating!.score! / 20.0;
      }
      notifyListeners();
    }, (error, code) {
      ToastUtils.showToast(error);
    }, isShowLoading: true);
  }

  ///评分反馈
  void requestWorkOrdersatisfactionRating(
      BuildContext context, Success success) {
    int level = rat.toInt();
    HttpUtils.getInstance()
        .posHttp(context, UrlConfig.workOrderSatisLevelFeedback, {
      RequestParamConfig.workOrderId: recordBean.workOrderId,
      RequestParamConfig.satisLevel: level.toString()
    }, (data) {
      success(data);
      notifyListeners();
    }, (error, code) {
      ToastUtils.showToast(error);
    }, isShowLoading: true);
  }
}
