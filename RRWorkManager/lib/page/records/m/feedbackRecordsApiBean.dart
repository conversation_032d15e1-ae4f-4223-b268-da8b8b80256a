class FeedbackRecordsApiBean {
  int? code;
  FeedbackRecordsDataBean? data;
  bool? success;
  String? message;
  String? status;
  int? timestamp;

  FeedbackRecordsApiBean(
      {this.code,
      this.data,
      this.success,
      this.message,
      this.status,
      this.timestamp});

  FeedbackRecordsApiBean.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    data = json['data'] != null ? FeedbackRecordsDataBean.fromJson(json['data']) : null;
    success = json['success'];
    message = json['message'];
    status = json['status'];
    timestamp = json['timestamp'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['success'] = success;
    data['message'] = message;
    data['status'] = status;
    data['timestamp'] = timestamp;
    return data;
  }
}

class FeedbackRecordsDataBean {
  int? total;
  List<FeedbackRecordBean>? rows;

  FeedbackRecordsDataBean({this.total, this.rows});

  FeedbackRecordsDataBean.fromJson(Map<String, dynamic> json) {
    total = json['total'];
    if (json['rows'] != null) {
      rows = <FeedbackRecordBean>[];
      json['rows'].forEach((v) {
        rows!.add(FeedbackRecordBean.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['total'] = total;
    if (rows != null) {
      data['rows'] = rows!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class FeedbackRecordBean {
  String? submitterType;
  String? workOrderType;
  String? createTime;
  String? submitterId;
  String? description;
  String? workOrderId;
  String? submitterName;
  String? workOrderSubType;
  String? status;

  FeedbackRecordBean(
      {this.submitterType,
      this.workOrderType,
      this.createTime,
      this.submitterId,
      this.description,
      this.workOrderId,
      this.submitterName,
      this.workOrderSubType,
      this.status});

  FeedbackRecordBean.fromJson(Map<String, dynamic> json) {
    submitterType = json['submitterType'];
    workOrderType = json['workOrderType'];
    createTime = json['createTime'];
    submitterId = json['submitterId'];
    description = json['description'];
    workOrderId = json['workOrderId'];
    submitterName = json['submitterName'];
    workOrderSubType = json['workOrderSubType'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['submitterType'] = submitterType;
    data['workOrderType'] = workOrderType;
    data['createTime'] = createTime;
    data['submitterId'] = submitterId;
    data['description'] = description;
    data['workOrderId'] = workOrderId;
    data['submitterName'] = submitterName;
    data['workOrderSubType'] = workOrderSubType;
    data['status'] = status;
    return data;
  }
}