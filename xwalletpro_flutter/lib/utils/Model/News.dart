class NewsInfo {
  String total;
  List<News> newsList;

  NewsInfo({required this.total, required this.newsList});

  factory NewsInfo.fromJson(Map<String, dynamic> json) {
    final originList = json['noticeList'] as List;
    List<News> newsList =
        originList.map((value) => News.fromJson(value)).toList();
    return NewsInfo(total: json['total'], newsList: newsList);
  }
}

class News {
  String sendTime;
  String title;
  String text;

  News({required this.sendTime, required this.title, required this.text});

  factory News.fromJson(json) {
    return News(
        sendTime: json['sendTime'], title: json['title'], text: json['text']);
  }

  @override
  // String toString() {
  //   // {"status":"0","message":"success","code":200,"data":{"hasNew":"1"},"success":true}
  //   return '{\"sendTime\":\"${this.sendTime}\",\"text\":\"${this.text}\",\"title\":\"${this.title}\"}';
  // }
  Map<String, dynamic> toJson() => <String, dynamic>{
        'sendTime': sendTime,
        'title': title,
        'text': text,
      };
}
