import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferencesUtils {
  ///保存相应的数据
  static void putData(String key, dynamic value) async {
    SharedPreferences sp = await SharedPreferences.getInstance();
    if (value is String) {
      sp.setString(key, value);
    } else if (value is bool) {
      sp.setBool(key, value);
    } else if (value is double) {
      sp.setDouble(key, value);
    } else if (value is int) {
      sp.setInt(key, value);
    } else if (value is List<String>) {
      sp.setStringList(key, value);
    }
  }

  ///获取保存的数据
  static Future<dynamic> getData(String key) async {
    SharedPreferences sp = await SharedPreferences.getInstance();
    return sp.get(key);
  }

    ///清除保存的信息
  static Future<dynamic> removeData(String key) async {
    SharedPreferences sp = await SharedPreferences.getInstance();
    return sp.remove(key);
  }
}
