import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xwalletpro_flutter/newsCenter/newsListDetails.dart';
import 'package:xwalletpro_flutter/utils/DemoLocalizations.dart';
import 'package:xwalletpro_flutter/utils/Global.dart';
import 'package:xwalletpro_flutter/utils/Model/News.dart';
import 'package:xwalletpro_flutter/utils/RRHttpPost.dart';

// //翻译
// import 'package:flutter_translate/flutter_translate.dart';

class newsList extends StatefulWidget {
  newsList({Key? key}) : super(key: key);

  _newsListState createState() => _newsListState();
}

class _newsListState extends State<newsList> {
  static const platform = const MethodChannel('com.cpkj.xwalletPro');
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      new GlobalKey<RefreshIndicatorState>();

  //配置下拉加载更多：
  ScrollController _scrollController = ScrollController();

  //加载中...
  bool isLoading = false;

  //分页：
  int _page = 1;

  //每一页有多少条数据：
  int _pageSize = 10;

  //信息列表数据
  List<News> _newList = [];

  //信息总条数
  int _total = 0;

  //解决重复请求的问题：
  bool flag = true;

  //是否有更多数据：
  bool _hasMore = true;

  //初始数据是否为空
  bool _noInitData = false;

  //初始化的时候获取的生命周期函数：
  @override
  void initState() {
    _refreshListData();
    _getProductListData();
    super.initState();
    //监听滚动条滚动事件：
    // _scrollController.position.pixels //获取滚动条滚动高度
    // _scrollController.position.maxScrollExtent //获取页面滚动高度
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >
          _scrollController.position.maxScrollExtent) {
        if (this.flag && this._hasMore) {
          _getProductListMoreData();
        }
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  //刷新商品列表
  _refreshListData() {
    platform.setMethodCallHandler((handler) => Future<dynamic>(() {
          print("handler.method -- ${handler.method}");
          switch (handler.method) {
            case "refresh":
              isLoading = false;
              _getProductListData();
              break;
          }
        }));
  }

  //获取商品列表的数据：
  _getProductListData() async {
    if (!isLoading) {
      setState(() {
        isLoading = true;
        this._page = 1;
        this._pageSize = 10;
        this._newList = [];
        this._total = 0;
      });
      //获取数据
      var result = await RRHttpPost.getNewsList(this._page, this._pageSize);
      Map<String, dynamic> json = jsonDecode(result);
      Map<String, dynamic> jsonMap =
          new Map<String, dynamic>.from(json["data"]);
      NewsInfo newsInfo = NewsInfo.fromJson(jsonMap);
      // newsInfo.newsList
      //     .forEach((News) => print('student name is ${News.title}'));
      this._total = int.parse(newsInfo.total);
      print("this._total----${this._total}");
      print("newsInfo.newsList.length----${newsInfo.newsList.length}");
      if (this._total == 0) {
        setState(() {
          this._noInitData = true;
        });
      }
      if (newsInfo.newsList.length < this._total) {
        setState(() {
          isLoading = false;
          this._newList.addAll(newsInfo.newsList);
          this._page++;
          this._hasMore = true;
          this.flag = true;
        });
      } else {
        setState(() {
          isLoading = false;
          this._newList.addAll(newsInfo.newsList);
          this._hasMore = false;
          this.flag = true;
        });
      }
    }
  }

  //获取更多商品列表的数据：
  _getProductListMoreData() async {
    if (!isLoading) {
      setState(() {
        isLoading = true;
      });
      //获取数据
      var result = await RRHttpPost.getNewsList(this._page, this._pageSize);
      Map<String, dynamic> json = jsonDecode(result);
      Map<String, dynamic> jsonMap =
          new Map<String, dynamic>.from(json["data"]);
      NewsInfo newsInfo = NewsInfo.fromJson(jsonMap);
      print(newsInfo.newsList);
      // newsInfo.newsList
      //     .forEach((Map<dynamic,dynamic>) => print('student name is ${Map["sendTime"]}'));
      this._total = int.parse(newsInfo.total);
      print("this._total----${this._total}");
      print("newsInfo.newsList.length----${newsInfo.newsList.length}");
      if (newsInfo.newsList.length < this._total) {
        setState(() {
          isLoading = false;
          this._newList.addAll(newsInfo.newsList);
          this._hasMore = false;
          this.flag = true;
        });
      } else {
        setState(() {
          isLoading = false;
          this._newList.addAll(newsInfo.newsList);
          this._page++;
          this.flag = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(

        ///屏幕适配
        designSize: const Size(750, 1334),
        builder: () {
          return Scaffold(
            appBar: AppBar(
              elevation: 0.0,
              centerTitle: true,
              title: Text(
                DemoLocalizations.of(context).msgcenter_title_msgCenter,
                style: TextStyle(
                  fontSize: 36.w,
                  color: Color(0xFF0F0F0F),
                  fontWeight: FontWeight.w500,
                ),
              ),
              leading: IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: Colors.black,
                ),
                onPressed: () {
                  //返回原生页面
                  // _backToNative();
                  // backToNative('', {});
                  popPage(context);
                },
              ),
              backgroundColor: Colors.white,
            ),
            body: RefreshIndicator(
              child: _newsListWidget(),
              onRefresh: () => _getProductListData(),
            ),
            // resizeToAvoidBottomPadding: false,
            resizeToAvoidBottomInset: false,
          );
        });
  }

  Widget _buildProgressIndicator() {
    return new Padding(
      padding: const EdgeInsets.all(8.0),
      child: new Center(
        child: new Opacity(
          opacity: isLoading ? 1.0 : 0.0,
          child: new CircularProgressIndicator(),
        ),
      ),
    );
  }

  //消息列表组件
  Widget _newsListWidget() {
    if (this._newList.length > 0) {
      //有数据
      return Container(
        decoration: BoxDecoration(
          color: Color(0xFFF5F7F8),
        ),
        child: ListView.builder(
          key: _refreshIndicatorKey,
          physics: const AlwaysScrollableScrollPhysics(),
          itemCount: this._newList.length + 1,
          controller: _scrollController,
          itemBuilder: (context, index) {
            if (index == this._newList.length) {
              return _buildProgressIndicator();
            } else {
              News newOne = this._newList[index];
              return GestureDetector(
                onTap: () {
                  //点击列表项
                  print("$index");

                  Navigator.push(context, MaterialPageRoute(builder: (context) {
                    return newsListDetails(newOne);
                  }));
                  // _goNewsDetails('newsListDetails', newOne.toJson());
                },
                child: Container(
                  // child: Column(
                  //   crossAxisAlignment: CrossAxisAlignment.start,
                  //   children: <Widget>[
                  // Container(
                  //   padding: EdgeInsets.only(
                  //     top: ScreenUtil.getInstance().setWidth(31),
                  //     // bottom: ScreenUtil.getInstance().setHeight(10),
                  //   ),
                  //   child: Text(
                  //     newOne.sendTime,
                  //     maxLines: 1,
                  //     overflow: TextOverflow.ellipsis,
                  //     style: TextStyle(
                  //       color: Color(0xFF252525),
                  //       fontSize: ScreenUtil.getInstance().setWidth(24),
                  //     ),
                  //   ),
                  // ),
                  // Container(
                  margin: EdgeInsets.only(
                    top: ScreenUtil().setWidth(30),
                    left: ScreenUtil().setWidth(30),
                    right: ScreenUtil().setWidth(30),
                  ),
                  decoration: BoxDecoration(
                    color: Color(0xFFFFFFFF),
                    borderRadius: BorderRadius.all(Radius.circular(8.0)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Container(
                        padding: EdgeInsets.only(
                          left: ScreenUtil().setWidth(20),
                          right: ScreenUtil().setWidth(20),
                          top: ScreenUtil().setHeight(40),
                          bottom: ScreenUtil().setHeight(10),
                        ),
                        child: Text(
                          newOne.title,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            color: Color(0xFF252525),
                            fontSize: ScreenUtil().setWidth(28),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.only(
                          left: ScreenUtil().setWidth(20),
                          right: ScreenUtil().setWidth(20),
                          bottom: ScreenUtil().setHeight(40),
                        ),
                        child: Text(
                          newOne.sendTime,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            color: Color(0xFF999999),
                            fontSize: ScreenUtil().setWidth(24),
                          ),
                        ),
                      ),
                      // Container(
                      //   padding: EdgeInsets.only(
                      //     left: ScreenUtil.getInstance().setWidth(20),
                      //     right: ScreenUtil.getInstance().setWidth(20),
                      //     top: ScreenUtil.getInstance().setHeight(18),
                      //   ),
                      //   height: ScreenUtil.getInstance().setHeight(1.0),
                      //   color: Color(0xFFC9CACB),
                      // ),
                      // Container(
                      //   padding: EdgeInsets.only(
                      //     left: ScreenUtil.getInstance().setWidth(20),
                      //     right: ScreenUtil.getInstance().setWidth(33),
                      //     top: ScreenUtil.getInstance().setWidth(20),
                      //     bottom: ScreenUtil.getInstance().setWidth(20),
                      //   ),
                      //   child: Text(
                      //     newOne.text,
                      //     maxLines: 4,
                      //     overflow: TextOverflow.ellipsis,
                      //     style: TextStyle(
                      //       color: Color(0xFF999999),
                      //       fontSize: ScreenUtil.getInstance().setWidth(28),
                      //     ),
                      //   ),
                      // RichText(
                      //   text: TextSpan(
                      //     text: newOne.text,
                      //     style: TextStyle(
                      //       color: Color(0xFF999999),
                      //       fontSize:
                      //           ScreenUtil.getInstance().setWidth(28),
                      //     ),
                      //   ),
                      // ),
                      // ),
                    ],
                  ),
                  // ),
                  //   ],
                  // ),
                ),
              );
            }
          },
        ),
      );
    } else {
      if (_noInitData) {
        //没有数据,空白页面
        return Container(
          width: ScreenUtil().setWidth(750),
          decoration: BoxDecoration(
            color: Color(0xFFF5F7F8),
          ),
          child: Column(
            children: <Widget>[
              Container(
                margin: EdgeInsets.only(top: ScreenUtil().setHeight(241)),
                child: Image(
                  height: ScreenUtil().setHeight(464),
                  image: AssetImage('assets/images/img_Null_information.png'),
                ),
              ),
              Container(
                height: ScreenUtil().setHeight(45),
                child: Text(
                  DemoLocalizations.of(context).common_label_nomessage,
                  style: TextStyle(
                    color: Color(0xFF212121),
                    fontSize: ScreenUtil().setWidth(32),
                  ),
                ),
              ),
              Container(
                margin: EdgeInsets.only(top: ScreenUtil().setHeight(10)),
                child: Text(
                  DemoLocalizations.of(context).common_label_norecord_tip,
                  style: TextStyle(
                    color: Color(0xFF9C9C9C),
                    fontSize: ScreenUtil().setWidth(28),
                  ),
                ),
              ),
            ],
          ),
        );
      } else {
        return Container();
      }
    }
  }
}
