//
//  RRHistoryDetailsModel.h
//  xWalletPro_iOS
//
//  Created by <PERSON> on 6/1/20.
//  Copyright © 2020 Shirley. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface RRHistoryDetailsModel : NSObject

@property (copy, nonatomic) NSString *orderId;
@property (copy, nonatomic) NSString *tradeType;
//除提现以外的交易使用 0-处理中 1-成功 2-失败 3-关闭 4-全额退款  5-部分退款
//提现订单由于有初始化状态 直接使用原始订单状态 10:初始化 20 :处理中 30 :成功 40:失败
@property (copy, nonatomic) NSString *orderStatus;
@property (copy, nonatomic) NSString *tradeTime;
@property (copy, nonatomic) NSString *orderIncomeFlag;
@property (copy, nonatomic) NSString *orderCurrType;
@property (copy, nonatomic) NSString *tradeAmt;
@property (copy, nonatomic) NSString *name;//转账人姓名，aa发起者姓名
@property (copy, nonatomic) NSString *merName;
@property (copy, nonatomic) NSString *recCstMame;
@property (copy, nonatomic) NSString *tradingSource;
@property (copy, nonatomic) NSString *recMobile;
@property (copy, nonatomic) NSString *payMobile;
@property (copy, nonatomic) NSString *payeeMobileNo;
@property (copy, nonatomic) NSString *payeeName;
@property (copy, nonatomic) NSString *payMethod;//支付方式 充值时返回
@property (copy, nonatomic) NSString *paymentMethod;//支付方式 充值时返回
@property (copy, nonatomic) NSString *remark;
@property (copy, nonatomic) NSString *commodityRemark;
@property (copy, nonatomic) NSString *originalOrderNo;//退款原始订单
@property (copy, nonatomic) NSString *originalOrderId;//退款原始订单
@property (copy, nonatomic) NSString *createTime;//提现初使时间
@property (copy, nonatomic) NSString *sendChannelTime;//提现发送渠道处理中时间
@property (copy, nonatomic) NSString *completeTime;//提现交易完成时间
@property (copy, nonatomic) NSString *bankName;//提现银行名称
@property (copy, nonatomic) NSString *feeAmt;//提现|转账转账手续费
@property (copy, nonatomic) NSString *taxAmt;//提现|转账转账手续费
@property (copy, nonatomic) NSString *bankCardNo;//提现银行卡
@property (copy, nonatomic) NSString *merFeeAmt;//支付时必须 商户手续费 扣费方式为内扣时取此值
@property (copy, nonatomic) NSString *payFeeAmt;//支付/转账时必须 付款用户手续费  支付且扣费方式为外扣 转账且扣费方式为内扣时展示此字段
@property (copy, nonatomic) NSString *payTaxAmt;//支付/转账时必须 付款用户税费  支付且扣费方式为外扣 转账且扣费方式为内扣时展示此字段
@property (copy, nonatomic) NSString *recFeeAmt;//转账时必须 收款用户手续费 转账且扣费方式为内扣时展示此字段
@property (copy, nonatomic) NSString *recTaxAmt;//转账时必须 收款用户税费 转账且扣费方式为内扣时展示此字段
@property (copy, nonatomic) NSString *refundFeeAmt;//退款时必须，退款手续费
@property (copy, nonatomic) NSString *refundMerFeeAmt;//退款时必须，退款给商户的手续费
@property (copy, nonatomic) NSString *refundPayFeeAmt;//退款时必须，退款给用户的手续费
@property (copy, nonatomic) NSString *refundPayTaxAmt;//退款时必须，退款给用户的手续费
@property (copy, nonatomic) NSString *participantType;//返现参与人类型 0：发起人   1：推荐人
@property (copy, nonatomic) NSString *businessType;//业务类型 05-注册 00-充值
@property (copy, nonatomic) NSString *initiator;//返现：发起人 参与人是被推荐人时有此值
@property (copy, nonatomic) NSString *outOrderNo;//商户订单号
@property (copy, nonatomic) NSString *cardNo;//银行卡
@property (copy, nonatomic) NSString *terminalNo;//终端编号
@property (copy, nonatomic) NSString *cashbackDeductAmount;//支付时使用的返现金额
@property (copy, nonatomic) NSString *cashbackRefundAmount;//退款时退回的返现金额
@property (copy, nonatomic) NSString *agentName;//代客业务代理商名称
@property (copy, nonatomic) NSString *agentOperName;//代客业务代理商名称
@property (copy, nonatomic) NSString *serviceProviderName;//代客业务代理商名称
@property (copy, nonatomic) NSString *paymentType;//交易方式 02-钱包 03-银行
@property (copy, nonatomic) NSString *recMobileNo;//代客话费充值手机号
@property (copy, nonatomic) NSString *address;//地址
@property (copy, nonatomic) NSString *billAccount;//充值手机号，学生姓名，党员号，市政账号，电表号，房产信息，其他缴费账号
@property (copy, nonatomic) NSString *billerName;//账单名称
@property (copy, nonatomic) NSString *city;//地区
@property (copy, nonatomic) NSString *billType;//缴费类型
@property (copy, nonatomic) NSString *billTypeDesc;//缴费类型
@property (copy, nonatomic) NSString *semester;//学期
@property (copy, nonatomic) NSString *classes;//班级
@property (copy, nonatomic) NSString *billerCode;//缴费单号
@property (copy, nonatomic) NSString *userName;//房产姓名
@property (copy, nonatomic) NSString *enquiryAccount;//查询账户
@property (copy, nonatomic) NSString *failureReason;//失败原因
@property (copy, nonatomic) NSString *resultRemark;//失败原因
@property (copy, nonatomic) NSString *paymentProduct;  //05-USSD支付  06-POS支付
@property (copy, nonatomic) NSString *transactionMode;
@property (copy, nonatomic) NSString *payCstType;  //0-商户
@property (copy, nonatomic) NSString *bundleName;  //流量包
@property (copy, nonatomic) NSString *bundlePlan;  //流量套餐
@property (copy, nonatomic) NSString *rechargeTradingType;  //话费类型
@property (copy, nonatomic) NSString *actualAmt;  //
@property (copy, nonatomic) NSString *cashbackAmt;  //
@property (copy, nonatomic) NSString *discountAmt;  //营销优惠金额
@property (copy, nonatomic) NSString *couponAmt;  //优惠券
@property (copy, nonatomic) NSString *membershipBonusAmt;  //活动优惠金额
@property (copy, nonatomic) NSString *membershipPointAmt;  //活动优惠金额
@property (copy, nonatomic) NSString *refundTaxAmt;  //退款税费
@property (copy, nonatomic) NSString *walletRefundAmount;  //钱包退款金额
@property (copy, nonatomic) NSString *counterpartyMobileNo;  //退款税费
@property (copy, nonatomic) NSString *counterpartyName;  //钱包退款金额
@property (copy, nonatomic) NSString *meterNo;  //电表号
@property (copy, nonatomic) NSString *meterName;  //电表用户名
@property (copy, nonatomic) NSString *billPaymentType; //社区服务缴费专用
@property (copy, nonatomic) NSString *channelTransNo; //渠道流水号
@property (copy, nonatomic) NSString *originalTradeType; //退款详情专用 30是支付退款
@property (copy, nonatomic) NSString *agentShortCode; //汇款码
@property (copy, nonatomic) NSString *refundAccount; //退款账号
@end

NS_ASSUME_NONNULL_END
