import 'package:flutter/material.dart';
import 'package:rrworkmanager/rrworkmanager.dart';

// ignore: constant_identifier_names
const String BaseUrl = "http://wc.proxy.vimbug.com";

void main() {
  runApp(const MyAppPrepare());
}

class MyAppPrepare extends StatelessWidget {
  const MyAppPrepare({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      debugShowCheckedModeBanner: false,
      home: MyApp(),
    );
  }
}

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

///全局上下文
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    RRWorkManager.initRRWorkManager(context,
        platform: "Agent", baseUrl: BaseUrl);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text('Plugin example app'),
        ),
        body: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            GestureDetector(
              onTap: () {
                RRWorkManager.openFeedBackSuggestions(
                    context, const Locale("en", "US"),
                    arguments: {});
              },
              child: Center(
                child: Container(
                  width: 200,
                  height: 50,
                  decoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(10)),
                  alignment: Alignment.center,
                  child: const Text(
                    "打开工单",
                    style: TextStyle(color: Colors.white, fontSize: 24),
                  ),
                ),
              ),
            ),
            GestureDetector(
              onTap: () {
                RRWorkManager.openTransactionComplaints(
                    context, const Locale("en", "US"),
                    arguments: {});
              },
              child: Center(
                child: Container(
                  width: 200,
                  height: 50,
                  margin: const EdgeInsets.only(top: 50),
                  decoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(10)),
                  alignment: Alignment.center,
                  child: const Text(
                    "打开交易投诉",
                    style: TextStyle(color: Colors.white, fontSize: 24),
                  ),
                ),
              ),
            ),
          ],
        ));
  }
}
