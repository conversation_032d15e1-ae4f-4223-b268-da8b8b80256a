// ignore_for_file: unnecessary_null_comparison, avoid_print
import 'dart:async' show Future;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rrworkmanager/page/feedback/v/feedbackpage.dart';
import 'package:rrworkmanager/page/feedback/vm/feedbackViewModel.dart';
import 'package:rrworkmanager/rrworkmanagerplugin.dart';
import 'package:rrworkmanager/utils/AppInfoUtils.dart';
import 'package:rrworkmanager/utils/StringUtils.dart';

import 'page/feedback/m/feedbackSubTypeListApiBean.dart';
import 'page/feedback/v/feedbackDysfunctionPage.dart';
import 'page/feedback/v/feedbackInfoInputPage.dart';

class RRWorkManager {
  //通道
  static const MethodChannel _channel = MethodChannel('rrworkmanager');

  //获取系统版本号
  static Future<String?> get platformVersion async {
    final String? version = await _channel.invokeMethod('getPlatformVersion');
    return version;
  }

  //初始化获取设备信息
  static void initRRWorkManager(BuildContext context,
      {required String platform, required String baseUrl}) {
    RRWorkManagerPlugin.workContext = context;
    RRWorkManagerPlugin.baseUrl = baseUrl;
    RRWorkManagerPlugin.platform = platform;

    //获取设备标识
    AppInfo.getUniqueId().then((uuid) {
      if (uuid != null) {
        print("设备唯一标识$uuid");
        RRWorkManagerPlugin.uuid = uuid;
      }
    });

    //获取版本信息
    AppInfo.getAppVersion().then((version) {
      if (version != null) {
        print("应用版本信息$version");
        RRWorkManagerPlugin.version = version;
      }
    });

    //获取手机品牌
    AppInfo.getBrand().then((brand) {
      if (brand != null) {
        print("实际品牌$brand");
        RRWorkManagerPlugin.brand = brand;
      }
    });

    //获取设备名称
    AppInfo.getSeries().then((series) {
      if (series != null) {
        print("实际品牌$series");
        RRWorkManagerPlugin.series = series;
      }
    });

    //获取手机系统版本
    AppInfo.getSystemVersion().then((systemVersion) {
      if (systemVersion != null) {
        print("手机系统版本$systemVersion");
        RRWorkManagerPlugin.systemVersion = systemVersion;
      }
    });
  }

  //打开反馈与建议
  static void openFeedBackSuggestions(BuildContext context, Locale locale,
      {required Map arguments}) {
    RRWorkManagerPlugin.currentLocale = locale;
    Localization.initLocalization(locale);
    Widget page = ScreenUtilInit(

        ///屏幕适配
        designSize: const Size(750, 1334),
        builder: () {
          return const FeedbackSuggestionsPage();
        });
    RRWorkManagerPlugin.sourcesRouteName = "feedback";
    Navigator.push(
        context,
        MaterialPageRoute<void>(
          settings: const RouteSettings(name: "feedback"),
          builder: (BuildContext context) => page,
        ));
  }

  //打开交易投诉
  static void openTransactionComplaints(BuildContext context, Locale locale,
      {required Map arguments}) {
    RRWorkManagerPlugin.currentLocale = locale;
    Localization.initLocalization(locale);
    FeedbackViewModel().requestWorkOrderSubTypeList(context, "transaction",
        (result) {
      FeedbackSubTypeListApiBean apiBean = result;
      late Widget feedbackPage;
      if (apiBean.data!.isNotEmpty) {
        feedbackPage = FeedbackDysfunctionPage(
          workOrderType: "transaction",
          subTypes: apiBean.data!,
        );
      } else {
        feedbackPage = const FeedbackInfoInputPage(
          type: "transaction",
        );
      }
      Widget page = ScreenUtilInit(

          ///屏幕适配
          designSize: const Size(750, 1334),
          builder: () {
            return feedbackPage;
          });
      Navigator.push(
          context,
          MaterialPageRoute<void>(
            builder: (BuildContext context) => page,
          ));
    }, (error, code) {});
  }
}
