CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
CONFIGURATION_BUILD_DIR = ${PODS_CONFIGURATION_BUILD_DIR}/FlutterPluginRegistrant
DEFINES_MODULE = YES
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1
HEADER_SEARCH_PATHS = $(inherited) "${PODS_ROOT}/Headers/Private" "${PODS_ROOT}/Headers/Private/FlutterPluginRegistrant" "${PODS_ROOT}/Headers/Public"
OTHER_CFLAGS = $(inherited) -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/rrworkmanager/rrworkmanager.modulemap" -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation/shared_preferences_foundation.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Private/image_picker_ios/image_picker_ios.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/device_info/device_info.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/package_info/package_info.modulemap"
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_DEVELOPMENT_LANGUAGE = ${DEVELOPMENT_LANGUAGE}
PODS_ROOT = ${SRCROOT}
PODS_TARGET_SRCROOT = ${PODS_ROOT}/../xwalletpro_flutter/.ios/Flutter/FlutterPluginRegistrant
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
PRODUCT_BUNDLE_IDENTIFIER = org.cocoapods.${PRODUCT_NAME:rfc1034identifier}
SKIP_INSTALL = YES
SWIFT_INCLUDE_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/rrworkmanager" "${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation"
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
