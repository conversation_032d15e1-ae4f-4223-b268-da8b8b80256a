// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		0B1FDFCA459609DA39F0E16E /* Pods_RRXNNetwork_Tests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 13F77A0A4D20C8E7F48E088B /* Pods_RRXNNetwork_Tests.framework */; };
		1416383E90778E79664379F4 /* Pods_RRXNNetwork_Example.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 97BF6AF837C6FAC9AF256FE1 /* Pods_RRXNNetwork_Example.framework */; };
		6003F58E195388D20070C39A /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6003F58D195388D20070C39A /* Foundation.framework */; };
		6003F590195388D20070C39A /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6003F58F195388D20070C39A /* CoreGraphics.framework */; };
		6003F592195388D20070C39A /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6003F591195388D20070C39A /* UIKit.framework */; };
		6003F598195388D20070C39A /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 6003F596195388D20070C39A /* InfoPlist.strings */; };
		6003F59A195388D20070C39A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 6003F599195388D20070C39A /* main.m */; };
		6003F59E195388D20070C39A /* RRAppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 6003F59D195388D20070C39A /* RRAppDelegate.m */; };
		6003F5A7195388D20070C39A /* RRViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6003F5A6195388D20070C39A /* RRViewController.m */; };
		6003F5A9195388D20070C39A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 6003F5A8195388D20070C39A /* Images.xcassets */; };
		6003F5B0195388D20070C39A /* XCTest.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6003F5AF195388D20070C39A /* XCTest.framework */; };
		6003F5B1195388D20070C39A /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6003F58D195388D20070C39A /* Foundation.framework */; };
		6003F5B2195388D20070C39A /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6003F591195388D20070C39A /* UIKit.framework */; };
		6003F5BA195388D20070C39A /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 6003F5B8195388D20070C39A /* InfoPlist.strings */; };
		6003F5BC195388D20070C39A /* Tests.m in Sources */ = {isa = PBXBuildFile; fileRef = 6003F5BB195388D20070C39A /* Tests.m */; };
		71719F9F1E33DC2100824A3D /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 71719F9D1E33DC2100824A3D /* LaunchScreen.storyboard */; };
		873B8AEB1B1F5CCA007FD442 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 873B8AEA1B1F5CCA007FD442 /* Main.storyboard */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		6003F5B3195388D20070C39A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 6003F582195388D10070C39A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 6003F589195388D20070C39A;
			remoteInfo = RRXNNetwork;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0E57AF007C68421EAC3DBDA6 /* Pods-RRXNNetwork_Example.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RRXNNetwork_Example.release.xcconfig"; path = "Target Support Files/Pods-RRXNNetwork_Example/Pods-RRXNNetwork_Example.release.xcconfig"; sourceTree = "<group>"; };
		13F77A0A4D20C8E7F48E088B /* Pods_RRXNNetwork_Tests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_RRXNNetwork_Tests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		217A93570F74DB9C07DFA6C2 /* Pods-RRXNNetwork_Tests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RRXNNetwork_Tests.debug.xcconfig"; path = "Target Support Files/Pods-RRXNNetwork_Tests/Pods-RRXNNetwork_Tests.debug.xcconfig"; sourceTree = "<group>"; };
		6003F58A195388D20070C39A /* RRXNNetwork_Example.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = RRXNNetwork_Example.app; sourceTree = BUILT_PRODUCTS_DIR; };
		6003F58D195388D20070C39A /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		6003F58F195388D20070C39A /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		6003F591195388D20070C39A /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		6003F595195388D20070C39A /* RRXNNetwork-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "RRXNNetwork-Info.plist"; sourceTree = "<group>"; };
		6003F597195388D20070C39A /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6003F599195388D20070C39A /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		6003F59B195388D20070C39A /* RRXNNetwork-Prefix.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "RRXNNetwork-Prefix.pch"; sourceTree = "<group>"; };
		6003F59C195388D20070C39A /* RRAppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRAppDelegate.h; sourceTree = "<group>"; };
		6003F59D195388D20070C39A /* RRAppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRAppDelegate.m; sourceTree = "<group>"; };
		6003F5A5195388D20070C39A /* RRViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRViewController.h; sourceTree = "<group>"; };
		6003F5A6195388D20070C39A /* RRViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRViewController.m; sourceTree = "<group>"; };
		6003F5A8195388D20070C39A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		6003F5AE195388D20070C39A /* RRXNNetwork_Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = RRXNNetwork_Tests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		6003F5AF195388D20070C39A /* XCTest.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = XCTest.framework; path = Library/Frameworks/XCTest.framework; sourceTree = DEVELOPER_DIR; };
		6003F5B7195388D20070C39A /* Tests-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "Tests-Info.plist"; sourceTree = "<group>"; };
		6003F5B9195388D20070C39A /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6003F5BB195388D20070C39A /* Tests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = Tests.m; sourceTree = "<group>"; };
		606FC2411953D9B200FFA9A0 /* Tests-Prefix.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Tests-Prefix.pch"; sourceTree = "<group>"; };
		71719F9E1E33DC2100824A3D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		873B8AEA1B1F5CCA007FD442 /* Main.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = Main.storyboard; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		8BD4F96886D9F63EFC7E346D /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; name = LICENSE; path = ../LICENSE; sourceTree = "<group>"; };
		97BF6AF837C6FAC9AF256FE1 /* Pods_RRXNNetwork_Example.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_RRXNNetwork_Example.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		98BD4F7E21043B834E017F5D /* Pods-RRXNNetwork_Tests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RRXNNetwork_Tests.release.xcconfig"; path = "Target Support Files/Pods-RRXNNetwork_Tests/Pods-RRXNNetwork_Tests.release.xcconfig"; sourceTree = "<group>"; };
		C6EE26EE83CC3EF847B84D54 /* RRXNNetwork.podspec */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; name = RRXNNetwork.podspec; path = ../RRXNNetwork.podspec; sourceTree = "<group>"; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		D6ABDE17DE8D518A5D4AE953 /* README.md */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = net.daringfireball.markdown; name = README.md; path = ../README.md; sourceTree = "<group>"; };
		ED9D7B5AEAFFB1764BD8811E /* Pods-RRXNNetwork_Example.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RRXNNetwork_Example.debug.xcconfig"; path = "Target Support Files/Pods-RRXNNetwork_Example/Pods-RRXNNetwork_Example.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		6003F587195388D20070C39A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6003F590195388D20070C39A /* CoreGraphics.framework in Frameworks */,
				6003F592195388D20070C39A /* UIKit.framework in Frameworks */,
				6003F58E195388D20070C39A /* Foundation.framework in Frameworks */,
				1416383E90778E79664379F4 /* Pods_RRXNNetwork_Example.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6003F5AB195388D20070C39A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6003F5B0195388D20070C39A /* XCTest.framework in Frameworks */,
				6003F5B2195388D20070C39A /* UIKit.framework in Frameworks */,
				6003F5B1195388D20070C39A /* Foundation.framework in Frameworks */,
				0B1FDFCA459609DA39F0E16E /* Pods_RRXNNetwork_Tests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0179EA20EC704D09FF4A0EBA /* Pods */ = {
			isa = PBXGroup;
			children = (
				ED9D7B5AEAFFB1764BD8811E /* Pods-RRXNNetwork_Example.debug.xcconfig */,
				0E57AF007C68421EAC3DBDA6 /* Pods-RRXNNetwork_Example.release.xcconfig */,
				217A93570F74DB9C07DFA6C2 /* Pods-RRXNNetwork_Tests.debug.xcconfig */,
				98BD4F7E21043B834E017F5D /* Pods-RRXNNetwork_Tests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		6003F581195388D10070C39A = {
			isa = PBXGroup;
			children = (
				60FF7A9C1954A5C5007DD14C /* Podspec Metadata */,
				6003F593195388D20070C39A /* Example for RRXNNetwork */,
				6003F5B5195388D20070C39A /* Tests */,
				6003F58C195388D20070C39A /* Frameworks */,
				6003F58B195388D20070C39A /* Products */,
				0179EA20EC704D09FF4A0EBA /* Pods */,
			);
			sourceTree = "<group>";
		};
		6003F58B195388D20070C39A /* Products */ = {
			isa = PBXGroup;
			children = (
				6003F58A195388D20070C39A /* RRXNNetwork_Example.app */,
				6003F5AE195388D20070C39A /* RRXNNetwork_Tests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		6003F58C195388D20070C39A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				6003F58D195388D20070C39A /* Foundation.framework */,
				6003F58F195388D20070C39A /* CoreGraphics.framework */,
				6003F591195388D20070C39A /* UIKit.framework */,
				6003F5AF195388D20070C39A /* XCTest.framework */,
				97BF6AF837C6FAC9AF256FE1 /* Pods_RRXNNetwork_Example.framework */,
				13F77A0A4D20C8E7F48E088B /* Pods_RRXNNetwork_Tests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		6003F593195388D20070C39A /* Example for RRXNNetwork */ = {
			isa = PBXGroup;
			children = (
				6003F59C195388D20070C39A /* RRAppDelegate.h */,
				6003F59D195388D20070C39A /* RRAppDelegate.m */,
				873B8AEA1B1F5CCA007FD442 /* Main.storyboard */,
				6003F5A5195388D20070C39A /* RRViewController.h */,
				6003F5A6195388D20070C39A /* RRViewController.m */,
				71719F9D1E33DC2100824A3D /* LaunchScreen.storyboard */,
				6003F5A8195388D20070C39A /* Images.xcassets */,
				6003F594195388D20070C39A /* Supporting Files */,
			);
			name = "Example for RRXNNetwork";
			path = RRXNNetwork;
			sourceTree = "<group>";
		};
		6003F594195388D20070C39A /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				6003F595195388D20070C39A /* RRXNNetwork-Info.plist */,
				6003F596195388D20070C39A /* InfoPlist.strings */,
				6003F599195388D20070C39A /* main.m */,
				6003F59B195388D20070C39A /* RRXNNetwork-Prefix.pch */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		6003F5B5195388D20070C39A /* Tests */ = {
			isa = PBXGroup;
			children = (
				6003F5BB195388D20070C39A /* Tests.m */,
				6003F5B6195388D20070C39A /* Supporting Files */,
			);
			path = Tests;
			sourceTree = "<group>";
		};
		6003F5B6195388D20070C39A /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				6003F5B7195388D20070C39A /* Tests-Info.plist */,
				6003F5B8195388D20070C39A /* InfoPlist.strings */,
				606FC2411953D9B200FFA9A0 /* Tests-Prefix.pch */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		60FF7A9C1954A5C5007DD14C /* Podspec Metadata */ = {
			isa = PBXGroup;
			children = (
				C6EE26EE83CC3EF847B84D54 /* RRXNNetwork.podspec */,
				D6ABDE17DE8D518A5D4AE953 /* README.md */,
				8BD4F96886D9F63EFC7E346D /* LICENSE */,
			);
			name = "Podspec Metadata";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		6003F589195388D20070C39A /* RRXNNetwork_Example */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6003F5BF195388D20070C39A /* Build configuration list for PBXNativeTarget "RRXNNetwork_Example" */;
			buildPhases = (
				28D828CACAD17B617B4462AF /* [CP] Check Pods Manifest.lock */,
				6003F586195388D20070C39A /* Sources */,
				6003F587195388D20070C39A /* Frameworks */,
				6003F588195388D20070C39A /* Resources */,
				335DBE164D8AF3B0B0DCC59B /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = RRXNNetwork_Example;
			productName = RRXNNetwork;
			productReference = 6003F58A195388D20070C39A /* RRXNNetwork_Example.app */;
			productType = "com.apple.product-type.application";
		};
		6003F5AD195388D20070C39A /* RRXNNetwork_Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6003F5C2195388D20070C39A /* Build configuration list for PBXNativeTarget "RRXNNetwork_Tests" */;
			buildPhases = (
				B774A4A16752D87D74C7077C /* [CP] Check Pods Manifest.lock */,
				6003F5AA195388D20070C39A /* Sources */,
				6003F5AB195388D20070C39A /* Frameworks */,
				6003F5AC195388D20070C39A /* Resources */,
				63604E9A46DF7D8550ECEB1B /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				6003F5B4195388D20070C39A /* PBXTargetDependency */,
			);
			name = RRXNNetwork_Tests;
			productName = RRXNNetworkTests;
			productReference = 6003F5AE195388D20070C39A /* RRXNNetwork_Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		6003F582195388D10070C39A /* Project object */ = {
			isa = PBXProject;
			attributes = {
				CLASSPREFIX = RR;
				LastUpgradeCheck = 0720;
				ORGANIZATIONNAME = Shirley;
				TargetAttributes = {
					6003F589195388D20070C39A = {
						DevelopmentTeam = 4FY4X4HW77;
					};
					6003F5AD195388D20070C39A = {
						TestTargetID = 6003F589195388D20070C39A;
					};
				};
			};
			buildConfigurationList = 6003F585195388D10070C39A /* Build configuration list for PBXProject "RRXNNetwork" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
				Base,
			);
			mainGroup = 6003F581195388D10070C39A;
			productRefGroup = 6003F58B195388D20070C39A /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				6003F589195388D20070C39A /* RRXNNetwork_Example */,
				6003F5AD195388D20070C39A /* RRXNNetwork_Tests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		6003F588195388D20070C39A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				873B8AEB1B1F5CCA007FD442 /* Main.storyboard in Resources */,
				71719F9F1E33DC2100824A3D /* LaunchScreen.storyboard in Resources */,
				6003F5A9195388D20070C39A /* Images.xcassets in Resources */,
				6003F598195388D20070C39A /* InfoPlist.strings in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6003F5AC195388D20070C39A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6003F5BA195388D20070C39A /* InfoPlist.strings in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		28D828CACAD17B617B4462AF /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-RRXNNetwork_Example-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		335DBE164D8AF3B0B0DCC59B /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-RRXNNetwork_Example/Pods-RRXNNetwork_Example-frameworks.sh",
				"${BUILT_PRODUCTS_DIR}/MJExtension/MJExtension.framework",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/MJExtension.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-RRXNNetwork_Example/Pods-RRXNNetwork_Example-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		63604E9A46DF7D8550ECEB1B /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-RRXNNetwork_Tests/Pods-RRXNNetwork_Tests-frameworks.sh",
				"${BUILT_PRODUCTS_DIR}/Expecta/Expecta.framework",
				"${BUILT_PRODUCTS_DIR}/Expecta+Snapshots/Expecta_Snapshots.framework",
				"${BUILT_PRODUCTS_DIR}/FBSnapshotTestCase/FBSnapshotTestCase.framework",
				"${BUILT_PRODUCTS_DIR}/Specta/Specta.framework",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Expecta.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Expecta_Snapshots.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSnapshotTestCase.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Specta.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-RRXNNetwork_Tests/Pods-RRXNNetwork_Tests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		B774A4A16752D87D74C7077C /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-RRXNNetwork_Tests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		6003F586195388D20070C39A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6003F59E195388D20070C39A /* RRAppDelegate.m in Sources */,
				6003F5A7195388D20070C39A /* RRViewController.m in Sources */,
				6003F59A195388D20070C39A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6003F5AA195388D20070C39A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6003F5BC195388D20070C39A /* Tests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		6003F5B4195388D20070C39A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 6003F589195388D20070C39A /* RRXNNetwork_Example */;
			targetProxy = 6003F5B3195388D20070C39A /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		6003F596195388D20070C39A /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				6003F597195388D20070C39A /* en */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
		6003F5B8195388D20070C39A /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				6003F5B9195388D20070C39A /* en */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
		71719F9D1E33DC2100824A3D /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				71719F9E1E33DC2100824A3D /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		6003F5BD195388D20070C39A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.3;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		6003F5BE195388D20070C39A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.3;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		6003F5C0195388D20070C39A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = ED9D7B5AEAFFB1764BD8811E /* Pods-RRXNNetwork_Example.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				DEVELOPMENT_TEAM = 4FY4X4HW77;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "RRXNNetwork/RRXNNetwork-Prefix.pch";
				INFOPLIST_FILE = "RRXNNetwork/RRXNNetwork-Info.plist";
				MODULE_NAME = ExampleApp;
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.demo.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 4.0;
				WRAPPER_EXTENSION = app;
			};
			name = Debug;
		};
		6003F5C1195388D20070C39A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0E57AF007C68421EAC3DBDA6 /* Pods-RRXNNetwork_Example.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				DEVELOPMENT_TEAM = 4FY4X4HW77;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "RRXNNetwork/RRXNNetwork-Prefix.pch";
				INFOPLIST_FILE = "RRXNNetwork/RRXNNetwork-Info.plist";
				MODULE_NAME = ExampleApp;
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.demo.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 4.0;
				WRAPPER_EXTENSION = app;
			};
			name = Release;
		};
		6003F5C3195388D20070C39A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 217A93570F74DB9C07DFA6C2 /* Pods-RRXNNetwork_Tests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				FRAMEWORK_SEARCH_PATHS = (
					"$(PLATFORM_DIR)/Developer/Library/Frameworks",
					"$(inherited)",
					"$(DEVELOPER_FRAMEWORKS_DIR)",
				);
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Tests/Tests-Prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Tests/Tests-Info.plist";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.demo.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 4.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/RRXNNetwork_Example.app/RRXNNetwork_Example";
				WRAPPER_EXTENSION = xctest;
			};
			name = Debug;
		};
		6003F5C4195388D20070C39A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 98BD4F7E21043B834E017F5D /* Pods-RRXNNetwork_Tests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				FRAMEWORK_SEARCH_PATHS = (
					"$(PLATFORM_DIR)/Developer/Library/Frameworks",
					"$(inherited)",
					"$(DEVELOPER_FRAMEWORKS_DIR)",
				);
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Tests/Tests-Prefix.pch";
				INFOPLIST_FILE = "Tests/Tests-Info.plist";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.demo.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 4.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/RRXNNetwork_Example.app/RRXNNetwork_Example";
				WRAPPER_EXTENSION = xctest;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		6003F585195388D10070C39A /* Build configuration list for PBXProject "RRXNNetwork" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6003F5BD195388D20070C39A /* Debug */,
				6003F5BE195388D20070C39A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6003F5BF195388D20070C39A /* Build configuration list for PBXNativeTarget "RRXNNetwork_Example" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6003F5C0195388D20070C39A /* Debug */,
				6003F5C1195388D20070C39A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6003F5C2195388D20070C39A /* Build configuration list for PBXNativeTarget "RRXNNetwork_Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6003F5C3195388D20070C39A /* Debug */,
				6003F5C4195388D20070C39A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 6003F582195388D10070C39A /* Project object */;
}
