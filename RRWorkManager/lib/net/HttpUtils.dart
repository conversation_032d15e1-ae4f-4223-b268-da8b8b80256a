// ignore_for_file: null_argument_to_non_null_type

import 'dart:convert';

import 'package:dio/adapter.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:rrworkmanager/common_widget/LoadingDialog.dart';
import 'package:rrworkmanager/model/EntityFactory.dart';
import 'package:rrworkmanager/rrworkmanagerplugin.dart';
import 'package:rrworkmanager/utils/StringUtils.dart';

import 'LoginInterceptors.dart';
import 'PluginInterceptors.dart';

typedef Success<T> = void Function(T);
typedef Fail = void Function(dynamic error, String code);

///网络请求工具类
class HttpUtils {
  static Dio? _dio;
  static final HttpUtils _http = HttpUtils();

  static HttpUtils getInstance() {
    return _http;
  }

  HttpUtils() {
    if (_dio == null) {
      _dio = createDio();
      //解决POS请求证书验证问题 强行信任证书
      (_dio!.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate =
          (client) {
        client.badCertificateCallback = (cert, host, port) {
          return true;
        };
        return client;
      };
    }
  }

  ///创建Dio实例
  Dio createDio() {
    var dio = Dio(BaseOptions(
      ///链接超时
      connectTimeout: 60000,

      ///读取超时
      receiveTimeout: 60000,

      ///发送超时
      sendTimeout: 60000,

      ///baseUrl
      baseUrl: RRWorkManagerPlugin.baseUrl,

      ///响应类型
      responseType: ResponseType.plain,
    ));

    //添加拦截器
    dio.interceptors.addAll([SmsInterceptors(), LogInterceptors()]);
    return dio;
  }

  ///get请求
  Future<Response?>? getHttp<T>(BuildContext context, String url,
      Map<String, dynamic> queryParametersMap, Success success, Fail fail,
      {bool isShowLoading = false}) {
    return _requestHttp<T>(
        context, "get", url, queryParametersMap, success, fail, isShowLoading);
  }

  ///post请求
  Future<Response?>? posHttp<T>(BuildContext context, String url,
      Map<String, dynamic> dataMap, Success success, Fail fail,
      {bool isJson = true, bool isShowLoading = true}) {
    return _requestHttp<T>(
        context, "post", url, dataMap, success, fail, isJson, isShowLoading);
  }

  ///请求的根方法
  Future<Response?>? _requestHttp<T>(
      BuildContext context,
      String method,
      String url,
      Map<String, dynamic>? requestMap,
      Success<T>? success,
      Fail? fail,
      [bool isJson = true,
      bool isShowLoading = false]) async {
    Response? response;

    print("请求的参数==isShowLoading=======$requestMap==$isShowLoading==");
    if (isShowLoading) {
      //显示dialog
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            // return LoadingDialog(text: "加载中...");
            return const LoadingDialog();
          });
    }

    try {
      if (method == "get") {
        if (requestMap != null && requestMap.isNotEmpty) {
          ///有参数
          response = await _dio!.get(url, queryParameters: requestMap);
        } else {
          ///没有参数
          response = await _dio!.get(url);
        }
      } else if (method == "post") {
        Options option;
        if (isJson) {
          option = Options(method: "POST", contentType: "application/json");
        } else {
          option = Options(
              method: "POST", contentType: "application/x-www-form-urlencoded");
        }

        if (requestMap != null && requestMap.isNotEmpty) {
          ///有参数
          response = await _dio!.post(url, data: requestMap, options: option);
        } else {
          ///没有参数
          response = await _dio!.post(url,
              data: <String, dynamic>{}, options: option);
        }
      }
    } on DioError catch (e) {
      if (isShowLoading) {
        Navigator.pop(context);
      }
      fail!(trans("common_network_error"), e.toString());
      // ignore: null_argument_to_non_null_type
      return Future.value();
    }

    // print("响应结果$response");
    print("isShowLoading $isShowLoading  context  $context ");
    if (isShowLoading) {
      Navigator.pop(context);
    }

    if (response!.statusCode == 200) {
      Map<String, dynamic> data = json.decode(response.data);
      String status = data["status"];
      int code = data["code"];
      if (status == "0") {
        if (code == 401) {

        } else {
          //成功
          if (success != null) {
            T t = EntityFactory.generateOBJ<T>(data) as T;
            success(t);
          }
        }
      } else if (status == "-5" || status == "SME-05002001") {
        
      } else {
        fail!(data["message"], data["status"]);
      }
    } else {
      //失败
      if (fail != null) {
        fail(response.statusMessage, response.statusCode.toString());
      }
    }
    return Future.value();
  }
}
