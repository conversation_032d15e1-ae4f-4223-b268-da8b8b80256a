/*
 * @Description: 
 * @Author: <PERSON>
 * @Date: 2022-05-13 12:27:07
 * @LastEditTime: 2022-05-13 14:13:33
 * @LastEditors: <PERSON>
 * @Reference: 
 */
import 'package:dio/dio.dart';
import 'package:rrworkmanager/config/StaticFieldConfig.dart';
import 'package:rrworkmanager/rrworkmanagerplugin.dart';
import 'package:rrworkmanager/utils/SharedPreferencesUtils.dart';

class FileInterceptors extends InterceptorsWrapper {

  @override
  Future<void> onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {

    //添加Header
    Map<String, dynamic> headers = options.headers;
    SharedPreferencesUtils.getData(SaveParamConfig.securityToken).then(
      (token) => {
        if (token != null) {headers["security-token"] = token}
      },
    );

    //登录token
    // SharedPreferencesUtils.getData(SaveParamConfig.token).then(
    //   (token) => {
    //     if (token != null) {headers["token"] = token}
    //   },
    // );
    headers["token"] = RRWorkManagerPlugin.userInfo["token"];
    super.onRequest(options, handler);
  }
}
