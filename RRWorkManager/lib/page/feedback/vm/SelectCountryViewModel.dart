import 'package:azlistview/azlistview.dart';
import 'package:flutter/cupertino.dart';
import 'package:lpinyin/lpinyin.dart';
import 'package:rrworkmanager/base/BaseViewModel.dart';
import 'package:rrworkmanager/config/StaticFieldConfig.dart';
import 'package:rrworkmanager/net/HttpUtils.dart';
import 'package:rrworkmanager/page/feedback/m/SelectCountryBean.dart';
import 'package:rrworkmanager/utils/ToastUtils.dart';

class SelectCountryViewModel extends BaseViewModel {
  List<CountryBean>? allNationList;

  List<String>? allNationTagList;

  ///获取国家列表
  void requestAllNationQuery(BuildContext context) {
    HttpUtils.getInstance().posHttp<SelectCountryBean>(
        context, UrlConfig.requestAllNationQuery, {}, (data) {
      allNationList = [];
      allNationTagList = [];
      SelectCountryBean selectCountryBean = data;
      if (selectCountryBean.data!.isNotEmpty) {
        for (var element in selectCountryBean.data!) {
          CountryBean bean = element;
          String pinyin = PinyinHelper.getPinyinE(bean.value!);
          String tag = pinyin.substring(0, 1).toUpperCase();
          if (RegExp('[A-Z]').hasMatch(tag)) {
            bean.tagIndex = tag;
          } else {
            bean.tagIndex = '#';
          }
          allNationList!.add(bean);
          if (!allNationTagList!.contains(bean.tagIndex)) {
            allNationTagList!.add(bean.tagIndex);
          }
        }

        // A-Z sort.
        SuspensionUtil.sortListBySuspensionTag(allNationList);

        for (var element in selectCountryBean.data!) {
          //如果引用同一个对象 插入0下标位置会排序失败，所以此处需要新的对象重新排序
          CountryBean bean = CountryBean.fromJson(element.toJson());
          //常用国家
          if (bean.commonlyUsed == "1") {
            bean.tagIndex = '★';
            allNationList!.insert(0, bean);
          }
        }

        // show sus tag.
        SuspensionUtil.setShowSuspensionStatus(allNationList);

        sortIndexBarTagList(allNationTagList);

      }
      notifyListeners();
    }, (error, code) {
      ToastUtils.showToast(error);
    });
  }

  void sortIndexBarTagList(List<String>? list) {
    if (list == null || list.isEmpty) return;
    list.sort((a, b) {
      if (a == "@" || b == "#") {
        return -1;
      } else if (a == "#" || b == "@") {
        return 1;
      } else {
        return a.compareTo(b);
      }
    });
  }
}
