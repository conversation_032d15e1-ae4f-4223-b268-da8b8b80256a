import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class ToastUtils {
  ///根据toast的内容展示Toast
  static void showToast(String toastStr,
      {Color? backgroundColor, Color? textColor}) {
    Fluttertoast.showToast(
        msg: toastStr,
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: backgroundColor ?? Colors.black54,
        textColor: textColor ?? Colors.white,
        fontSize: 16.0);
  }
}
