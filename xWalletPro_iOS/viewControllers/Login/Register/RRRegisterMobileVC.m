//
//  RRRegisterMobileVC.m
//  xWalletPro_iOS
//
//  Created by 董宗广 on 2024/11/15.
//  Copyright © 2024 Shirley. All rights reserved.
//

#import "RRRegisterMobileVC.h"
#import "RRXNTextView.h"
#import "RRXNTextField.h"
#import "RRXNTipLabel.h"
#import "RRXNButton.h"
#import "RRVerifyCodeVC.h"
#import "RRCommonWebVC.h"
#import "RRRegisterProcessView.h"
#import "RRRegisterVC.h"
@interface RRRegisterMobileVC ()<UIScrollViewDelegate,UITextViewDelegate>

@property (nonatomic,strong) RRRegisterProcessView *processView;
@property (nonatomic,strong) UIScrollView *registerView;
@property (nonatomic,strong) UIView *registerConView;
@property (nonatomic,strong) RRXNTipLabel *mobileTip;
@property (nonatomic,strong) RRXNTextView *mobileNo;
@property (nonatomic,strong) UIButton *selectBtn;
@property (nonatomic,strong) UIView *policyView;
@property (nonatomic,strong) RRXNButton *nextBtn;
@end

@implementation RRRegisterMobileVC
{
    RRXNTextView *_currentTextView;
    //手机号前缀
    NSString *_hexMobile;
    //协议信息
    NSDictionary *_policyInfo;
}

#pragma mark- 控件懒加载

//进度
-(RRRegisterProcessView *)processView{
    if(!_processView){
        _processView = [[RRRegisterProcessView alloc] init];
        _processView.title = TRANSLATE(@"personal_title_mobile");
        _processView.currentIndex = @"2";
        _processView.total = @"6";
        [_processView loadView];
    }
    return _processView;
}

-(UIScrollView *)registerView
{
    if (!_registerView) {
        _registerView = [[UIScrollView alloc]init];
        _registerView.delegate = self;
        _registerView.showsHorizontalScrollIndicator = NO;
        _registerView.showsVerticalScrollIndicator = NO;
        _registerView.pagingEnabled = NO;
        _registerView.clipsToBounds = YES;
        _registerView.bounces = NO;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(tap)];
        [_registerView addGestureRecognizer:tap];
    }
    return _registerView;
}
-(UIView *)registerConView
{
    if (!_registerConView) {
        _registerConView = [[UIView alloc]init];
    }
    return _registerConView;
}
//手机号
-(RRXNTextView *)mobileNo
{
    if (!_mobileNo) {
        _mobileNo = [[RRXNTextView alloc]init];
        _mobileNo.tipTitle = TRANSLATE(@"personal_title_mobile");
        _mobileNo.isMobile = YES;
        _mobileNo.maxLength = 9;
        _hexMobile = [NSString stringWithFormat:@"+%@", DEFAULT_MOBILEHEX];
        _mobileNo.isFixedMobile = YES;
        _mobileNo.mobileHex = _hexMobile;
        _mobileNo.keyboardType = UIKeyboardTypeNumberPad;
        _mobileNo.clearButtonType = ClearButtonAppearWhenEditing;
        _mobileNo.isCurrentBlock = ^{
            self->_currentTextView = self->_mobileNo;
        };
        _mobileNo.mobileBtnClickBlock = ^{
        };
        [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(textChange) name:UITextViewTextDidChangeNotification object:_mobileNo.textView];
    }
    return _mobileNo;
}

-(UIButton *)selectBtn
{
    if (!_selectBtn) {
        _selectBtn = [[UIButton alloc]init];
        [_selectBtn setBackgroundImage:[UIImage imageNamed:@"icon_check_1"] forState:UIControlStateNormal];
        [_selectBtn setBackgroundImage:[UIImage imageNamed:@"icon_check_2"] forState:UIControlStateSelected];
        [_selectBtn addTarget:self action:@selector(selectBtnClick) forControlEvents:UIControlEventTouchUpInside];
    }
    return _selectBtn;
}
-(UIView *)policyView
{
    if (!_policyView) {
        _policyView = [[UIView alloc]init];
        _policyView.backgroundColor = [UIColor clearColor];
        [_policyView addSubview:self.selectBtn];
        [self.selectBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(15*AUTO_SIZE);
            make.top.mas_equalTo(_policyView).offset(13*AUTO_SIZE);
            make.height.and.width.mas_equalTo(17*AUTO_SIZE);
        }];
        
        UITextView *textView = [[UITextView alloc] init];
        textView.backgroundColor = [UIColor clearColor];
        textView.delegate = self;
        textView.font = FONT(14.f*FONT_SIZE);
        textView.editable = NO;
        textView.scrollEnabled = NO;
        textView.tintColor = MAIN_BLUE;
        textView.textContainerInset = UIEdgeInsetsZero;
        textView.linkTextAttributes = @{NSForegroundColorAttributeName : MAIN_BLUE};
        NSString *start = TRANSLATE(@"register_label_accept_the");
        NSString *end = TRANSLATE(@"register_label_terms_condition");
        NSString *linkStr = [NSString stringWithFormat:@"%@%@", start, end];
        NSMutableAttributedString *mutStr = [[NSMutableAttributedString alloc] initWithString:linkStr];
        [mutStr addAttributes:@{NSForegroundColorAttributeName:INF_LABEL_COLOR,NSFontAttributeName:FONT(14.f*FONT_SIZE)} range:NSMakeRange(0, linkStr.length)];
        [mutStr addAttributes:@{NSLinkAttributeName:@"active://policy",NSFontAttributeName:FONT(14.f*FONT_SIZE), NSForegroundColorAttributeName:MAIN_BLUE} range:NSMakeRange(start.length, end.length)];
        textView.attributedText = mutStr;
        [_policyView addSubview:textView];
        [textView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(self.selectBtn.mas_right).offset(8.5*AUTO_SIZE);
            make.right.mas_equalTo(_policyView.mas_right).offset(-15*AUTO_SIZE);
            make.top.mas_equalTo(self.selectBtn.mas_top);
            make.height.mas_equalTo(40*AUTO_SIZE);
        }];
    }
    return _policyView;
}
-(RRXNButton *)nextBtn
{
    if (!_nextBtn) {
        _nextBtn = [[RRXNButton alloc]init];
        [_nextBtn setTitle:TRANSLATE(@"common_btn_next") forState:UIControlStateNormal];
        [_nextBtn addTarget:self action:@selector(nextBtnClick) forControlEvents:UIControlEventTouchUpInside];
        //        [RRTools btnUnable:_nextBtn withDarkColor:BTN_DARK_COLOR];
        [RRTools btnEnable:_nextBtn withHighColor:BTN_DARK_COLOR];
    }
    return _nextBtn;
}

-(RRXNTipLabel *)mobileTip
{
    if (!_mobileTip) {
        _mobileTip = [[RRXNTipLabel alloc]init];
    }
    return _mobileTip;
}

#pragma mark -- UITextViewDelegate

- (BOOL)textView:(UITextView *)textView shouldInteractWithURL:(NSURL *)URL inRange:(NSRange)characterRange interaction:(UITextItemInteraction)interaction
{
    NSLog(@"shouldInteractWithURL");
    NSString *host = URL.host;
    if ([host containsString:@"policy"]) {
        if(_policyInfo != nil) {
            RRCommonWebVC *policyVC = [[RRCommonWebVC alloc] init];
            policyVC.titleString = _policyInfo[@"conditionTitle"];
            policyVC.content = _policyInfo[@"conditionContent"];
            policyVC.type = @"localHtml";
            [self.navigationController pushViewController:policyVC animated:YES];
        } else {
            
        }
    }
    return NO;//这里返回NO可以避免长按连接弹出actionSheet框
}

-(BOOL)canPerformAction:(SEL)action withSender:(id)sender
{
    
    if ([UIMenuController sharedMenuController]) {
        
        [UIMenuController sharedMenuController].menuVisible = NO;
        
    }
    return NO;
    
}

#pragma mark- privateMethod
-(void)tap
{
    [self.registerView endEditing:YES];
    [[UIApplication sharedApplication].keyWindow endEditing:YES];
}
-(void)textChange
{
    if(_currentTextView == self.mobileNo) {
        self.mobileTip.text = @"";
    }
    if (_mobileNo.textFieldText.length > 0 && _selectBtn.selected == 1) {
        [RRTools btnEnable:self.nextBtn withHighColor:BTN_HIGH_COLOR];
    }else{
        [RRTools btnUnable:self.nextBtn withDarkColor:BTN_DARK_COLOR];
    }
}

-(void)selectBtnClick
{
    [[UIApplication sharedApplication].keyWindow endEditing:YES];
    _selectBtn.selected = !_selectBtn.selected;
    if (_selectBtn.selected) {
        [self textChange];
    }else{
        [RRTools btnUnable:self.nextBtn withDarkColor:BTN_DARK_COLOR];
    }
}

-(void)policyBtnClick
{
    if(_policyInfo != nil) {
        RRCommonWebVC *policyVC = [[RRCommonWebVC alloc] init];
        policyVC.titleString = _policyInfo[@"conditionTitle"];
        policyVC.content = _policyInfo[@"conditionContent"];
        policyVC.type = @"localHtml";
        [self.navigationController pushViewController:policyVC animated:YES];
    } else {
        
    }
}
-(void)requestLatestTermCondition
{
    NSString *type = @"01";
    if([self.from isEqualToString:@"junior"]) {
        type = @"06";
    }
    NSMutableDictionary *jsonDic = [NSMutableDictionary dictionaryWithDictionary:@{@"conditionType":type}];
    [RRHttpRequest postSend:TERM_CONDITION urlString:nil jsonDic:jsonDic success:^(RRNetworkModel *model) {
        if ([RRTools isSuccess:model]) {
            self->_policyInfo = model.data;
        }
    } failure:^(NSError *error) {
        
    } time:60 encrypt:NO];
}

-(void)nextBtnClick
{
    [[UIApplication sharedApplication].keyWindow endEditing:YES];
    NSString *mobileNo = _mobileNo.textFieldText;
    
    if (![RRTools validateMobileNo:mobileNo]) {
        self.mobileTip.text = TRANSLATE(@"common_tip_error_mobile_tip");
        [self.mobileNo.textView becomeFirstResponder];
        return;
    }
    mobileNo = [NSString stringWithFormat:@"%@",_mobileNo.textFieldText];
    
    RRHUDView * hud = [RRHUDView defaultHUDViewForWindow];
    [TOPWINDOW addSubview:hud];
    [hud show];
    NSMutableDictionary *jsonDic = [NSMutableDictionary dictionary];
    [jsonDic setValue:DEFAULT_MOBILEHEX forKey:@"areaCode"];
    [jsonDic setValue:mobileNo forKey:@"mobile"];
    [RRHttpRequest postSend:USER_REGISTER_MOBILE_VALIDATE urlString:nil jsonDic:jsonDic success:^(RRNetworkModel *model) {
        [hud hide];
        if ([RRTools isSuccess:model]) {
            [RRTools getMainQueueDo:^{
                NSString *isWalletUser = [NSString stringWithFormat:@"%@", model.data[@"isWalletUser"]];
                NSString *accountType = [NSString stringWithFormat:@"%@", model.data[@"accountType"]];
                NSString *verifyStatus = [NSString stringWithFormat:@"%@", model.data[@"verifyStatus"]];
                NSString *isCardIdUsed =@"";
                NSString *userStatus = [NSString stringWithFormat:@"%@", model.data[@"userStatus"]];
                
                if([isWalletUser isEqualToString:@"0"]) {
                    [jsonDic setValue:self->_policyInfo[@"conditionVersionNo"] forKey:@"conditionVersionNo"];
                    [jsonDic setValue:self->_policyInfo[@"conditionTitle"] forKey:@"conditionTitle"];
                    [jsonDic setValue:self->_policyInfo[@"conditionType"] forKey:@"conditionType"];
                    RRVerifyCodeVC *codeVC = [[RRVerifyCodeVC alloc] init];
                    codeVC.from = @"register";
                    codeVC.phoneNo = mobileNo;
                    codeVC.mobileAreaCode = DEFAULT_MOBILEHEX;
                    codeVC.dataDic = jsonDic;
                    [self.navigationController pushViewController:codeVC animated:YES];
                } else if([isCardIdUsed isEqualToString:@"1"]) {
                    [RRTools showAlertWithTitle:TRANSLATE(@"common_title_notice") message:TRANSLATE(@"register_tips_have_regist_junior_ID") okTtitle:@"" okBlock:^{} cancelTitle:TRANSLATE(@"common_btn_close") cancelBlock:^{
                    }];
                } else {
                    if([userStatus isEqualToString:@"0"]||[userStatus isEqualToString:@"1"]) {
                        [RRTools showAlertWithTitle:TRANSLATE(@"common_title_notice") message:[NSString stringWithFormat:@"%@", TRANSLATE(@"register_tips_have_regist_start")] okTtitle:@"" okBlock:^{} cancelTitle:TRANSLATE(@"common_btn_close") cancelBlock:^{
                        }];
                    } else if ([userStatus isEqualToString:@"4"]) {
                        [RRTools showAlertWithTitle:TRANSLATE(@"common_title_notice") message:TRANSLATE(@"register_tips_under_line_authentication") okTtitle:@"" okBlock:^{} cancelTitle:TRANSLATE(@"common_btn_close") cancelBlock:^{
                        }];
                    }
                }
            }];
        }
    } failure:^(NSError *error) {
        [hud hide];
    } time:60 encrypt:YES];
}

#pragma mark- 重写方法
-(void)backToPuper
{
    [self.navigationController popViewControllerAnimated:YES];
}
//继承的方法实现，语言更改后，页面需要刷新的内容
-(void)languageChange:(NSNotification*)notification
{
    //将observer收到通知后的方法，放在子线程中执行，这种方式接收到通知还是同步，但是接收到通知后的处理改成异步
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        
    });
}

#pragma mark- 页面生命周期/布局约束方法

- (void)viewDidLoad {
    self.hasLeftBtn = YES;
    [super viewDidLoad];
    self.titleString = TRANSLATE(@"register_title_user_registration");
    self.view.backgroundColor = VC_BG_COLOR;
    
    [self setUp];
    [self addLoginViewConstraints];
    [self requestLatestTermCondition];
}

-(void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
}

-(void)setUp
{
    [self.view addSubview:self.processView];
    [self.view addSubview:self.registerView];
    [self.registerView addSubview:self.registerConView];
    
    [self.registerView addSubview:self.mobileNo];
    [self.registerView addSubview:self.mobileTip];
    [self.view addSubview:self.policyView];
    [self.policyView addSubview:self.nextBtn];
}

-(void)addLoginViewConstraints
{
    [self.processView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(0);
        make.top.equalTo(self.headerView.mas_bottom);
        make.height.mas_equalTo(44*AUTO_SIZE);
    }];
    [self.registerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.processView.mas_bottom);
        make.left.and.right.mas_equalTo(0);
        make.bottom.mas_equalTo(0);
    }];
    [self.registerConView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self.registerView);
        make.width.mas_equalTo(self.registerView);
    }];
    [self.mobileNo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(0);
        make.left.and.right.mas_equalTo(0);
    }];
    
    [self.mobileTip mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.mobileNo.mas_bottom);
        make.left.mas_equalTo(15*AUTO_SIZE);
        make.right.mas_equalTo(-15*AUTO_SIZE);
    }];
    
    [self.policyView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(0);
        make.right.mas_equalTo(0);
        make.height.mas_equalTo(100*AUTO_SIZE + kSafeAreaBottomHeight);
        make.top.mas_equalTo(self.mobileTip.mas_bottom).offset(32 * AUTO_SIZE);
        make.bottom.mas_equalTo(self.registerConView.mas_bottom).offset(kSafeAreaBottomHeight);
    }];
    [self.nextBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(15*AUTO_SIZE);
        make.right.mas_equalTo(-15*AUTO_SIZE);
        make.height.mas_equalTo(44*AUTO_SIZE);
        make.bottom.mas_equalTo(-bottomHeight);
    }];
}

@end
