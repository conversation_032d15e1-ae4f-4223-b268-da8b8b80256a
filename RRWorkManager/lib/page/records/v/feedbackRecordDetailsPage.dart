import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:rrworkmanager/common_widget/AppBarWidget.dart';
import 'package:rrworkmanager/page/records/m/feedbackRecordDetailsApiBean.dart';
import 'package:rrworkmanager/page/records/m/feedbackRecordsApiBean.dart';
import 'package:rrworkmanager/page/records/vm/feedbackRecordDetailsViewModel.dart';
import 'package:rrworkmanager/rrworkmanagerConfig.dart';
import 'package:rrworkmanager/rrworkmanagerplugin.dart';
import 'package:rrworkmanager/utils/Global.dart';
import 'package:rrworkmanager/utils/StringUtils.dart';

class FeedbackRecordDetailsPage extends StatefulWidget {
  //工单记录
  final FeedbackRecordBean bean;
  const FeedbackRecordDetailsPage({Key? key, required this.bean})
      : super(key: key);
  @override
  _FeedbackRecordDetailsPageState createState() =>
      _FeedbackRecordDetailsPageState();
}

class _FeedbackRecordDetailsPageState extends State<FeedbackRecordDetailsPage> {
  final FeedbackRecordDetailsViewModel _model =
      FeedbackRecordDetailsViewModel();

  //凭证图片大小
  final double pictureWidth = (1.sw - 5 * 30.w) * 0.25;

  @override
  void initState() {
    super.initState();
    _model.recordBean = widget.bean;
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _model.requestWorkOrderRecordDetailsInfo(
          context, widget.bean.workOrderId!);
    });
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<FeedbackRecordDetailsViewModel>(
      create: (_) => _model,
      child: Scaffold(
        appBar:
            AppBarUtils(title: trans("records_title_details")).build(context),
        body: _body(context),
        backgroundColor: Global.lightBackGroundColor,
      ),
    );
  }

  Widget _body(BuildContext context) {
    return Consumer<FeedbackRecordDetailsViewModel>(
        builder: ((context, model, child) {
      return SingleChildScrollView(
        child: Column(
          children: [
            _problemDetailsWidget(context),
            _transactionInfoWidget(context),
            _credentialsWidget(context),
            _processResultWidget(context),
            if (widget.bean.status == "2") ...[
              _starRatingWidget(context),
              _submitWidget(context)
            ]
          ],
        ),
      );
    }));
  }

  //问题详情
  Widget _problemDetailsWidget(BuildContext context) {
    return Consumer<FeedbackRecordDetailsViewModel>(
        builder: (context, model, child) {
      List<String> titles = [];
      if (model.detailsBean != null) {
        for (var key in model.problemInfo.keys) {
          titles.add(key);
        }
      }

      return Column(
        children: [
          Container(
            alignment: Alignment.centerLeft,
            margin: EdgeInsets.only(top: 24.w, bottom: 16.w),
            padding: EdgeInsets.symmetric(horizontal: 30.w),
            child: Text(
              trans("records_label_problem_Details"),
              style: TextStyle(color: Global.tipsTextColor, fontSize: 28.sp),
            ),
          ),
          if (model.detailsBean != null)
            Container(
              width: 1.sw,
              color: Colors.white,
              padding: EdgeInsets.only(top: 24.w, bottom: 4.w),
              child: Column(
                children: List.generate(titles.length, (index) {
                  String key = titles[index];
                  String? value = model.problemInfo[key];
                  return _infoItemWidget(titles[index], value!);
                }),
              ),
            )
        ],
      );
    });
  }

  //处理进度
  Widget _processResultWidget(BuildContext context) {
    return Consumer<FeedbackRecordDetailsViewModel>(
        builder: (context, model, child) {
      List<String> titles = [];
      if (model.detailsBean != null) {
        for (var key in model.processInfo.keys) {
          titles.add(key);
        }
      }
      return Column(
        children: [
          Container(
            alignment: Alignment.centerLeft,
            margin: EdgeInsets.only(top: 24.w, bottom: 16.w),
            padding: EdgeInsets.symmetric(horizontal: 30.w),
            child: Text(
              trans("records_label_processing_progress"),
              style: TextStyle(color: Global.tipsTextColor, fontSize: 28.sp),
            ),
          ),
          if (model.detailsBean != null)
            Container(
              width: 1.sw,
              color: Colors.white,
              padding: EdgeInsets.only(top: 24.w, bottom: 4.w),
              child: Column(
                children: List.generate(titles.length, (index) {
                  String key = titles[index];
                  String? value = model.processInfo[key];
                  return _infoItemWidget(titles[index], value!);
                }),
              ),
            )
        ],
      );
    });
  }

  //交易信息
  Widget _transactionInfoWidget(BuildContext context) {
    if (widget.bean.workOrderType == "TC") {
      return Consumer<FeedbackRecordDetailsViewModel>(
          builder: ((context, model, child) {
        if (model.detailsBean != null) {
          WorkOrderTransactionInfo transactionInfo = model.detailsBean!.transactionList!.first;
          return Container(
            width: 1.sw,
            margin: EdgeInsets.only(top: 24.w),
            padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 22.w),
            decoration: const BoxDecoration(color: Colors.white),
            child: Column(
              children: [
                Container(
                  margin: EdgeInsets.only(bottom: 16.w),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        transactionInfo.transType!,
                        style: TextStyle(
                            color: Global.mainTextColor, fontSize: 32.sp),
                      ),
                      Text(
                        transactionInfo.transAmt!.toString()+transactionInfo.currency!,
                        style: TextStyle(
                            color: Global.successColor, fontSize: 32.sp),
                      )
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(bottom: 16.w),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        transactionInfo.createdTime!,
                        style: TextStyle(
                            color: Global.tipsTextColor, fontSize: 24.sp),
                      ),
                      Text(
                        transactionInfo.orderStatus!,
                        style: TextStyle(
                            color: Global.tipsTextColor, fontSize: 24.sp),
                      )
                    ],
                  ),
                ),
              ],
            ),
          );
        }
        return Container();
      }));
    }
    return Container();
  }

  //凭证
  Widget _credentialsWidget(BuildContext context) {
    return Consumer<FeedbackRecordDetailsViewModel>(
        builder: (context, model, child) {
      String title = trans("records_label_credentials");
      return Container(
        width: 1.sw,
        color: Colors.white,
        margin: EdgeInsets.only(top: 24.w),
        padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 24.w),
        child: Column(
          children: [
            Container(
              alignment: Alignment.centerLeft,
              child: Text(
                title,
                style: TextStyle(color: Global.mainTextColor, fontSize: 32.sp),
              ),
            ),
            if (model.detailsBean != null)
              Container(
                  margin: EdgeInsets.only(top: 20.w),
                  alignment: Alignment.centerLeft,
                  child: Wrap(
                    spacing: 30.w,
                    runSpacing: 30.w,
                    children: List.generate(model.credentials.length, (index) {
                      return _pictureItemWidget(context,
                          image: model.credentials[index], ontap: () {
                        showBigPictureDialog(context, model.credentials[index]);
                      });
                    }),
                  )),
          ],
        ),
      );
    });
  }

  Widget _pictureItemWidget(BuildContext context,
      {required String image, Function? ontap,}) {
    return Container(
      width: pictureWidth,
      height: pictureWidth,
      clipBehavior: Clip.hardEdge,
      alignment: Alignment.center,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(8.r)),
      child: GestureDetector(
          onTap: () {
            if (ontap != null) {
              ontap();
            }
          },
          child: Image.network(image, width: pictureWidth, fit: BoxFit.contain, headers: {
            "token": RRWorkManagerPlugin.userInfo["token"]
          },)),
    );
  }

  Widget _infoItemWidget(String title, String value) {
    return Container(
        margin: EdgeInsets.only(bottom: 20.w),
        padding: EdgeInsets.symmetric(horizontal: 30.w),
        width: 1.sw,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(color: Global.tipsTextColor, fontSize: 22.sp),
            ),
            Container(
              margin: EdgeInsets.only(top: 4.w),
              alignment: Alignment.centerLeft,
              child: Text(
                value,
                style: TextStyle(color: Global.mainTextColor, fontSize: 32.sp),
              ),
            )
          ],
        ));
  }

  //星级评分
  Widget _starRatingWidget(BuildContext context) {
    return Consumer<FeedbackRecordDetailsViewModel>(
        builder: (context, model, child) {
      return Column(
        children: [
          Container(
            alignment: Alignment.centerLeft,
            margin: EdgeInsets.only(top: 24.w, bottom: 16.w),
            padding: EdgeInsets.symmetric(horizontal: 30.w),
            child: Text(
              trans("records_label_satisfaction_rating"),
              style: TextStyle(color: Global.tipsTextColor, fontSize: 28.sp),
            ),
          ),
          Container(
            width: 1.sw,
            padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
            color: Colors.white,
            child: Column(
              children: [
                Container(
                  margin: EdgeInsets.only(bottom: 24.w),
                  alignment: Alignment.centerLeft,
                  child: Text(
                    trans("records_tips_provide_points"),
                    style:
                        TextStyle(color: Global.mainTextColor, fontSize: 26.sp),
                  ),
                ),
                Container(
                  alignment: Alignment.center,
                  child: RatingBar(
                    direction: Axis.horizontal,
                    allowHalfRating: false,
                    initialRating: model.rat,
                    itemCount: 5,
                    ratingWidget: RatingWidget(
                      full: const Icon(
                        Icons.star,
                        color: Colors.amber,
                      ),
                      empty: const Icon(
                        Icons.star,
                        color: Color.fromARGB(255, 233, 233, 233),
                      ),
                      half: const Icon(
                        Icons.star,
                        color: Color.fromARGB(255, 210, 208, 208),
                      ),
                    ),
                    itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
                    onRatingUpdate: (rating) {
                      model.setStarRating(rating);
                    },
                  ),
                )
              ],
            ),
          )
        ],
      );
    });
  }

  //提交
  Widget _submitWidget(BuildContext context) {
    return Consumer<FeedbackRecordDetailsViewModel>(
        builder: (context, model, child) {
      return GestureDetector(
        onTap: () {
          _model.requestWorkOrdersatisfactionRating(context, (result) { });
        },
        child: Container(
          width: 1.sw - 60.w,
          height: 88.w,
          margin: EdgeInsets.only(bottom: 30.w, top: 40.w),
          alignment: Alignment.center,
          decoration: BoxDecoration(
              color: RRWorkManagerConfig.submitBtnColor,
              borderRadius: BorderRadius.circular(8.r)),
          child: Text(
            trans("records_btn_submit_rating"),
            style: TextStyle(color: Colors.white, fontSize: 36.sp),
          ),
        ),
      );
    });
  }

  ///查看图片弹窗
  void showBigPictureDialog(BuildContext context, String url) {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return Column(mainAxisAlignment: MainAxisAlignment.center, children: [
            Container(
              alignment: Alignment.bottomCenter,
              margin: EdgeInsets.only(bottom: 200.w),
              child: SizedBox(
                width: 600.w,
                height: 380.w,
                child: Image(
                        image: NetworkImage(url, headers: {
                          "token":RRWorkManagerPlugin.userInfo["token"]
                        }),
                        fit: BoxFit.contain),
              ),
            ),
            GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Image.asset(
                "resources/image/popup_close.png",
                package: Global.package,
              ),
            ),
          ]);
        });
  }
}
