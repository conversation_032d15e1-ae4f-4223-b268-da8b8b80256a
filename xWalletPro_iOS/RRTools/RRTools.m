//
//  RRTools.m
//  xmobile4.0
//
//  Created by <PERSON> on 12/16/19.
//  Copyright © 2019 Shirley. All rights reserved.
//

#import "RRTools.h"
#import "SecurityUtility.h"
#import <sys/utsname.h>
#import <Photos/Photos.h>
//生物识别框架
#import<LocalAuthentication/LocalAuthentication.h>
#import "RRLoginPwdUnlockVC.h"
#import "RRLoginUnlockVC.h"
#import "RRTabbarVC.h"
#import "PaymentOrderVC.h"
#import "BuyAirtimeBundleVC.h"
#import "RRLoginVC.h"

NSString * const KEY_UDID_INSTEAD_XWALLET = @"com.myapp.udid.test";

@implementation RRTools

#pragma mark keychain操作
//通过 key 获取数据
+ (NSMutableDictionary *)getKeychainQuery:(NSString *)service {
    return [NSMutableDictionary dictionaryWithObjectsAndKeys:
            (id)kSecClassGenericPassword,(id)kSecClass,
            service, (id)kSecAttrService,
            service, (id)kSecAttrAccount,
            (id)kSecAttrAccessibleAfterFirstUnlock,(id)kSecAttrAccessible,
            nil];
}
//保存数据至钥匙串中
+ (void)save:(NSString *)service data:(id)data {
    //Get search dictionary
    NSMutableDictionary *keychainQuery = [self getKeychainQuery:service];
    //Delete old item before add new item
    SecItemDelete((CFDictionaryRef)keychainQuery);
    //Add new object to search dictionary(Attention:the data format)
    if (VERSION >= 11.0) {
        [keychainQuery setObject:[NSKeyedArchiver archivedDataWithRootObject:data requiringSecureCoding:YES error:nil] forKey:(id)kSecValueData];
    }else{
        [keychainQuery setObject:[NSKeyedArchiver archivedDataWithRootObject:data] forKey:(id)kSecValueData];
    }
    
    //Add item to keychain with the search dictionary
    SecItemAdd((CFDictionaryRef)keychainQuery, NULL);
}
//从钥匙串中加载数据
+ (id)load:(NSString *)service {
    id ret = nil;
    NSMutableDictionary *keychainQuery = [self getKeychainQuery:service];
    //Configure the search setting
    //Since in our simple case we are expecting only a single attribute to be returned (the password) we can set the attribute kSecReturnData to kCFBooleanTrue
    [keychainQuery setObject:(id)kCFBooleanTrue forKey:(id)kSecReturnData];
    [keychainQuery setObject:(id)kSecMatchLimitOne forKey:(id)kSecMatchLimit];
    CFDataRef keyData = NULL;
    if (SecItemCopyMatching((CFDictionaryRef)keychainQuery, (CFTypeRef *)&keyData) == noErr) {
        @try {
            ret = [NSKeyedUnarchiver unarchiveObjectWithData:(__bridge NSData *)keyData];
        } @catch (NSException *e) {
            NSLog(@"Unarchive of %@ failed: %@", service, e);
        } @finally {
        }
    }
    if (keyData)
        CFRelease(keyData);
    return ret;
}
//删除数据
+ (void)deleteData:(NSString *)service {
    NSMutableDictionary *keychainQuery = [self getKeychainQuery:service];
    SecItemDelete((CFDictionaryRef)keychainQuery);
}

#pragma mark NSUserDefault操作
//保存日期对象
+(void)setDate:(NSDate *)date key:(NSString *)key
{
    //获取preference
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    //保存
    [defaults setObject:date forKey:key];
    //立即同步
    [defaults synchronize];
}
//取出日期对象
+(NSDate *)dateForKey:(NSString *)key
{
    //获取preference
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    //读取
    NSDate *date = (NSDate *)[defaults objectForKey:key];
    return date;
}
//移除日期对象
+(void)removeDateForKey:(NSString *)key{
    //获取preference
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    //保存
    [defaults removeObjectForKey:key];
    //立即同步
    [defaults synchronize];
}
//保存普通对象
+(void)setStr:(NSString *)str key:(NSString *)key{
    
    //    str = [RRTools ENCodebase64:str];
    //获取preference
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    
    //保存
    [defaults setObject:str forKey:key];
    
    //立即同步
    [defaults synchronize];
    
}

//读取
+(NSString *)strForKey:(NSString *)key{
    
    //获取preference
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    
    //读取
    NSString *str=(NSString *)[defaults objectForKey:key];
    //    str = [RRTools DECodebase64:str];
    
    return str;
}

//删除
+(void)removeStrForKey:(NSString *)key{
    
    [self setStr:nil key:key];
    
}
//保存普通对象
+(void)setDic:(NSMutableDictionary *)dic key:(NSString *)key{
    
    //    str = [RRTools ENCodebase64:str];
    NSDictionary * ddic = [NSDictionary dictionaryWithDictionary:dic];
    //获取preference
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    
    //保存
    [defaults setObject:ddic forKey:key];
    
    //立即同步
    [defaults synchronize];
    
}

//读取
+(NSMutableDictionary *)dicForKey:(NSString *)key{
    
    //获取preference
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    
    //读取
    NSMutableDictionary *dic=[NSMutableDictionary dictionaryWithDictionary:[defaults objectForKey:key]];
    
    return dic;
    
}

//删除
+(void)removeDicForKey:(NSString *)key{
    
    [self setDic:nil key:key];
    
}

//保存普通对象
+(void)setArray:(NSMutableArray *)array key:(NSString *)key{
    
    //获取preference
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    
    //保存
    [defaults setObject:array forKey:key];
    
    //立即同步
    [defaults synchronize];
    
}

//读取
+(NSMutableArray *)arrayForKey:(NSString *)key{
    
    //获取preference
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    
    //读取
    NSMutableArray *array=[NSMutableArray arrayWithArray:(NSMutableArray  *)[defaults objectForKey:key]];
    
    return array;
    
}

//删除
+(void)removeArrayForKey:(NSString *)key{
    
    [self setArray:nil key:key];
    
}


//保存int
+(void)setInt:(NSInteger)i key:(NSString *)key{
    //获取preference
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    
    //保存
    [defaults setInteger:i forKey:key];
    
    //立即同步
    [defaults synchronize];
    
}

//读取
+(NSInteger)intForKey:(NSString *)key{
    //获取preference
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    
    //读取
    NSInteger i=[defaults integerForKey:key];
    
    return i;
}

//保存float
+(void)setFloat:(CGFloat)floatValue key:(NSString *)key{
    
    //获取preference
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    
    //保存
    [defaults setFloat:floatValue forKey:key];
    
    //立即同步
    [defaults synchronize];
    
}
//读取
+(CGFloat)floatForKey:(NSString *)key{
    //获取preference
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    
    //读取
    CGFloat floatValue=[defaults floatForKey:key];
    
    return floatValue;
}

//保存bool
+(void)setBool:(BOOL)boolValue key:(NSString *)key{
    //获取preference
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    
    //保存
    [defaults setBool:boolValue forKey:key];
    
    //立即同步
    [defaults synchronize];
    
}
//读取
+(BOOL)boolForKey:(NSString *)key{
    //获取preference
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    
    //读取
    BOOL boolValue=[defaults boolForKey:key];
    
    return boolValue;
}
//获取联系人存储的key
+(NSString *)getContactStorageKey
{
    RRUserInfo *userInf = [RRUserInfo objectForKey:@"customerDic"];
    NSString *userId = [RRTools nvl:userInf.userId];
    NSString *key = [NSString stringWithFormat:@"%@%@",userId,@"contactList"];
    return key;
}
//获取联系人存储md5的key
+(NSString *)getContactMD5StorageKey
{
    RRUserInfo *userInf = [RRUserInfo objectForKey:@"customerDic"];
    NSString *userId = [RRTools nvl:userInf.userId];
    NSString *key = [NSString stringWithFormat:@"%@%@",userId,@"md5"];
    return key;
}

#pragma mark 适配相关
//适配相关
+(CGFloat)autoSize
{
    //获取系统字体
    UIFont *font = [UIFont systemFontOfSize:[UIFont systemFontSize]];
    CGFloat X = 1;
    if([font.fontName containsString:@"bold"]){
        X = 1.1;
    }
    CGFloat n = (VIEW_WIDTH/375.0) * X;
    NSString * s = [NSString stringWithFormat:@"%.2f",n];
    return s.floatValue;
}

+(CGFloat)fontSize
{
    CGFloat n = (VIEW_WIDTH/375.0);
    NSString * s = [NSString stringWithFormat:@"%.2f",n];
    return s.floatValue;
}


#pragma mark 设备信息-权限处理
/**
 本方法是得到 UUID 后存入系统中的 keychain 的方法
 不用添加 plist 文件
 程序删除后重装,仍可以得到相同的唯一标示
 但是当系统升级或者刷机后,系统中的钥匙串会被清空,此时本方法失效
 */
+(NSString *)getDeviceIDInKeychain
{
    NSString *getUDIDInKeychain = [RRTools strForKey:@"UDID"];
    if (!getUDIDInKeychain ||[getUDIDInKeychain isEqualToString:@""]||[getUDIDInKeychain isKindOfClass:[NSNull class]]) {
        
        getUDIDInKeychain = (NSString *)[RRTools load:KEY_UDID_INSTEAD_XWALLET];
        NSLog(@"从keychain中获取到的 UDID_INSTEAD %@",getUDIDInKeychain);
        if (!getUDIDInKeychain ||[getUDIDInKeychain isEqualToString:@""]||[getUDIDInKeychain isKindOfClass:[NSNull class]]) {
            CFUUIDRef puuid = CFUUIDCreate( nil );
            CFStringRef uuidString = CFUUIDCreateString( nil, puuid);
            NSString * result = (NSString *)CFBridgingRelease(CFStringCreateCopy( NULL, uuidString));
            CFRelease(puuid);
            CFRelease(uuidString);
            NSLog(@"\n \n \n _____重新存储 UUID _____\n \n \n  %@",result);
            [RRTools save:KEY_UDID_INSTEAD_XWALLET data:result];
            getUDIDInKeychain = (NSString *)[RRTools load:KEY_UDID_INSTEAD_XWALLET];
        }
        [RRTools setStr:getUDIDInKeychain key:@"UDID"];
        NSLog(@"最终 ———— UDID_INSTEAD %@",getUDIDInKeychain);
        return getUDIDInKeychain;
        
    }else{
        return getUDIDInKeychain;
    }
}
//获取指纹token
+(NSString *)getBiometricsToken
{
    RRUserInfo *userInf = [RRUserInfo objectForKey:@"customerDic"];
    NSString *userId = [RRTools nvl:userInf.userId];
    NSString *token = [NSString stringWithFormat:@"%@%@",userId,[RRTools getDeviceIDInKeychain]];
    return token;
}
//获取语音存储key
+(NSString *)getBroadcastKey
{
    RRUserInfo *userInf = [RRUserInfo objectForKey:@"customerDic"];
    NSString *userId = [RRTools nvl:userInf.userId];
    NSString *key = [NSString stringWithFormat:@"%@%@",userId,@"Broadcast"];
    return key;
}
//获取手机机型
+ (NSString*)getIphoneType {
    struct utsname systemInfo;
    uname(&systemInfo);
    NSString *platform = [NSString stringWithCString:systemInfo.machine encoding:NSASCIIStringEncoding];
    
    if ([platform isEqualToString:@"iPhone1,1"]) return @"iPhone 2G";
    if ([platform isEqualToString:@"iPhone1,2"]) return @"iPhone 3G";
    if ([platform isEqualToString:@"iPhone2,1"]) return @"iPhone 3GS";
    if ([platform isEqualToString:@"iPhone3,1"]) return @"iPhone 4";
    if ([platform isEqualToString:@"iPhone3,2"]) return @"iPhone 4";
    if ([platform isEqualToString:@"iPhone3,3"]) return @"iPhone 4";
    if ([platform isEqualToString:@"iPhone4,1"]) return @"iPhone 4S";
    if ([platform isEqualToString:@"iPhone5,1"]) return @"iPhone 5";
    if ([platform isEqualToString:@"iPhone5,2"]) return @"iPhone 5";
    if ([platform isEqualToString:@"iPhone5,3"]) return @"iPhone 5c";
    if ([platform isEqualToString:@"iPhone5,4"]) return @"iPhone 5c";
    if ([platform isEqualToString:@"iPhone6,1"]) return @"iPhone 5s";
    if ([platform isEqualToString:@"iPhone6,2"]) return @"iPhone 5s";
    if ([platform isEqualToString:@"iPhone7,1"]) return @"iPhone 6 Plus";
    if ([platform isEqualToString:@"iPhone7,2"]) return @"iPhone 6";
    if ([platform isEqualToString:@"iPhone8,1"]) return @"iPhone 6s";
    if ([platform isEqualToString:@"iPhone8,2"]) return @"iPhone 6s Plus";
    if ([platform isEqualToString:@"iPhone8,4"]) return @"iPhone SE";
    if ([platform isEqualToString:@"iPhone9,1"])    return @"iPhone 7";
    if ([platform isEqualToString:@"iPhone9,2"])    return @"iPhone 7 Plus";
    if ([platform isEqualToString:@"iPhone9,3"])    return @"iPhone 7";
    if ([platform isEqualToString:@"iPhone9,4"])    return @"iPhone 7 Plus";
    if ([platform isEqualToString:@"iPhone10,1"])   return @"iPhone 8";
    if ([platform isEqualToString:@"iPhone10,2"])   return @"iPhone 8 Plus";
    if ([platform isEqualToString:@"iPhone10,3"])   return @"iPhone X";
    if ([platform isEqualToString:@"iPhone10,4"])   return @"iPhone 8";
    if ([platform isEqualToString:@"iPhone10,5"])   return @"iPhone 8 Plus";
    if ([platform isEqualToString:@"iPhone10,6"])   return @"iPhone X";
    if ([platform isEqualToString:@"iPhone11,2"])   return @"iPhone XS";
    if ([platform isEqualToString:@"iPhone11,4"])   return @"iPhone XS Max";
    if ([platform isEqualToString:@"iPhone11,6"])   return @"iPhone XS Max";
    if ([platform isEqualToString:@"iPhone11,8"])   return @"iPhone XR";
    if ([platform isEqualToString:@"iPhone12,1"])   return @"iPhone 11";
    if ([platform isEqualToString:@"iPhone12,3"])   return @"iPhone 11 Pro";
    if ([platform isEqualToString:@"iPhone12,5"])   return @"iPhone 11 Pro Max";
    if ([platform isEqualToString:@"iPhone12,8"])   return @"iPhone SE2";
    if ([platform isEqualToString:@"iPhone13,1"])   return @"iPhone 12 mini";
    if ([platform isEqualToString:@"iPhone13,2"])   return @"iPhone 12";
    if ([platform isEqualToString:@"iPhone13,3"])   return @"iPhone 12 Pro";
    if ([platform isEqualToString:@"iPhone13,4"])   return @"iPhone 12 Pro Max";
    if ([platform isEqualToString:@"iPod1,1"])   return @"iPod Touch 1G";
    if ([platform isEqualToString:@"iPod2,1"])   return @"iPod Touch 2G";
    if ([platform isEqualToString:@"iPod3,1"])   return @"iPod Touch 3G";
    if ([platform isEqualToString:@"iPod4,1"])   return @"iPod Touch 4G";
    if ([platform isEqualToString:@"iPod5,1"])   return @"iPod Touch 5G";
    
    if ([platform isEqualToString:@"iPad1,1"])   return @"iPad 1G";
    if ([platform isEqualToString:@"iPad2,1"])   return @"iPad 2";
    if ([platform isEqualToString:@"iPad2,2"])   return @"iPad 2";
    if ([platform isEqualToString:@"iPad2,3"])   return @"iPad 2";
    if ([platform isEqualToString:@"iPad2,4"])   return @"iPad 2";
    if ([platform isEqualToString:@"iPad2,5"])   return @"iPad Mini 1G";
    if ([platform isEqualToString:@"iPad2,6"])   return @"iPad Mini 1G";
    if ([platform isEqualToString:@"iPad2,7"])   return @"iPad Mini 1G";
    if ([platform isEqualToString:@"iPad3,1"])   return @"iPad 3";
    if ([platform isEqualToString:@"iPad3,2"])   return @"iPad 3";
    if ([platform isEqualToString:@"iPad3,3"])   return @"iPad 3";
    if ([platform isEqualToString:@"iPad3,4"])   return @"iPad 4";
    if ([platform isEqualToString:@"iPad3,5"])   return @"iPad 4";
    if ([platform isEqualToString:@"iPad3,6"])   return @"iPad 4";
    if ([platform isEqualToString:@"iPad4,1"])   return @"iPad Air";
    if ([platform isEqualToString:@"iPad4,2"])   return @"iPad Air";
    if ([platform isEqualToString:@"iPad4,3"])   return @"iPad Air";
    if ([platform isEqualToString:@"iPad5,3"])   return @"iPad Air 2";
    if ([platform isEqualToString:@"iPad5,4"])   return @"iPad Air 2";
    if ([platform isEqualToString:@"iPad4,4"])   return @"iPad Mini 2G";
    if ([platform isEqualToString:@"iPad4,5"])   return @"iPad Mini 2G";
    if ([platform isEqualToString:@"iPad4,6"])   return @"iPad Mini 2G";
    if ([platform isEqualToString:@"iPad4,7"])   return @"iPad Mini 3";
    if ([platform isEqualToString:@"iPad4,8"])   return @"iPad Mini 3";
    if ([platform isEqualToString:@"iPad4,9"])   return @"iPad Mini 3";
    if ([platform isEqualToString:@"iPad5,1"])   return @"iPad Mini 4";
    if ([platform isEqualToString:@"iPad5,2"])   return @"iPad Mini 4";
    if ([platform isEqualToString:@"iPad6,3"])   return @"iPad Pro 9.7";
    if ([platform isEqualToString:@"iPad6,4"])   return @"iPad Pro 9.7";
    if ([platform isEqualToString:@"iPad6,7"])   return @"iPad Pro 12.9";
    if ([platform isEqualToString:@"iPad6,8"])   return @"iPad Pro 12.9";
    if ([platform isEqualToString:@"i386"])      return @"iPhone Simulator";
    if ([platform isEqualToString:@"x86_64"])    return @"iPhone Simulator";
    return platform;
}
//获取应用名
+(NSString*)getAppName
{
    NSDictionary *infoDictionary = [[NSBundle mainBundle] infoDictionary];
    NSString *app_Name = [infoDictionary objectForKey:@"CFBundleDisplayName"];
    return app_Name;
}
//获取应用版本号
+(NSString*)getAppVersion
{
    NSDictionary *infoDictionary = [[NSBundle mainBundle] infoDictionary];
    NSString *app_Version = [infoDictionary objectForKey:@"CFBundleShortVersionString"];
    return app_Version;
}
//获取手机是否可以支持touchId或faceId
+(BOOL)canSupportBiometrics
{
    //1.判断系统版本是否支持
    if(NSFoundationVersionNumber < NSFoundationVersionNumber_iOS_8_0){
        NSLog(@"系统版本不支持TouchID");
        return NO;
    }else{
        //2.创建本地验证上下文对象
        LAContext *context = [LAContext new];
        //错误对象
        NSError* error = nil;
        //3.判断能否使用指纹识别
        if([context canEvaluatePolicy:LAPolicyDeviceOwnerAuthenticationWithBiometrics error:&error]){
            //支持
            return YES;
        }else{
            //不支持
            //判断是否是因为指纹验证错误次数过多引起
            if([context canEvaluatePolicy:LAPolicyDeviceOwnerAuthentication error:&error]){
                return YES;
            }else{
                return YES;
            }
        }
    }
}
//验证touchId或faceId
+(void)checkBiometricsSuccessBlock:(void(^)(void))successBlock withChangeWayBlock:(void (^)(void))changeWayBlock withFailBlock:(void(^)(void))failBlock withCancelBlock:(void(^)(void))cancelBlock withInvalidBlock:(void(^)(void))invalidBlock
{
    [RRTools checkBiometricsWithFallbackTitle:@"" withSuccessBlock:successBlock withChangeWayBlock:changeWayBlock withFailBlock:failBlock withCancelBlock:cancelBlock withInvalidBlock:invalidBlock];
}
//需要标题
+(void)checkBiometricsWithFallbackTitle:(NSString *)fallbackTitle withSuccessBlock:(void(^)(void))successBlock withChangeWayBlock:(void (^)(void))changeWayBlock withFailBlock:(void(^)(void))failBlock withCancelBlock:(void(^)(void))cancelBlock withInvalidBlock:(void(^)(void))invalidBlock
{
    //创建本地验证上下文对象
    LAContext *context = [LAContext new];
    //错误对象
    NSError* error = nil;
    //验证提示
    NSString *localizedReason;
    if (IS_X) {
        localizedReason = TRANSLATE(@"common_alert_verify_faceid");
    }else{
        localizedReason = TRANSLATE(@"common_alert_verify_touchid");
    }
    if (fallbackTitle.length > 0) {
//        错误一次的feedback按钮
        context.localizedFallbackTitle = fallbackTitle;
    }
//    if (@available(iOS 10.0, *)) {
//        //取消按钮文案
//        context.localizedCancelTitle = TRANSLATE(@"Cancel");
//    }
    //判断能否使用指纹识别
    if([context canEvaluatePolicy:LAPolicyDeviceOwnerAuthenticationWithBiometrics error:&error]){
        //支持localizedReason为alert弹框的message内容
        [context evaluatePolicy:LAPolicyDeviceOwnerAuthenticationWithBiometrics localizedReason:localizedReason reply:^(BOOL success, NSError * _Nullable error) {
            if (success) {
                //验证成功，主线程处理UI
                [RRTools getMainQueueDo:^{
                    successBlock();
                }];
            }else{
                NSLog(@"验证失败:%@",error.description);
                switch(error.code) {
                    case LAErrorSystemCancel:
                    {
                        NSLog(@"系统取消授权，如其他APP切入");
                        [RRTools getMainQueueDo:^{
                            cancelBlock();
                        }];
                        break;
                    }
                    case LAErrorUserCancel:
                    {
                        NSLog(@"用户取消验证Touch ID");
                        [RRTools getMainQueueDo:^{
                            cancelBlock();
                        }];
                        break;
                    }
                    case LAErrorAuthenticationFailed:
                    {
                        NSLog(@"授权失败");
                        [RRTools getMainQueueDo:^{
                            failBlock();
                        }];
                        break;
                    }
                    case LAErrorUserFallback:
                    {
                        [RRTools getMainQueueDo:^{
                            changeWayBlock();
                        }];
                        break;
                    }
                    case LAErrorTouchIDLockout:
                    {
                        NSLog(@"用户错误次数太多，现在被锁住了");
                        [RRTools getMainQueueDo:^{
                            LAContext *context1 = [LAContext new];
                            NSError* error = nil;
                            //验证提示
                            NSString *localizedReason = TRANSLATE(@"common_label_verify_screen_lock");
                            if([context1 canEvaluatePolicy:LAPolicyDeviceOwnerAuthentication error:&error]){
                                [context1 evaluatePolicy:LAPolicyDeviceOwnerAuthentication localizedReason:localizedReason reply:^(BOOL success, NSError * _Nullable error) {
                                    if(success){
                                        [RRTools checkBiometricsSuccessBlock:successBlock withChangeWayBlock:changeWayBlock withFailBlock:failBlock withCancelBlock:cancelBlock withInvalidBlock:invalidBlock];
                                    }else{
                                        //取消验证
                                        cancelBlock();
                                    }
                                }];
                            }else{
                                
                            }
                        }];
                        break;
                    }
                    default:
                    {
                        [RRTools getMainQueueDo:^{
                            failBlock();
                        }];
                        break;
                    }
                }
            }
//            //废止这个context
//            [context invalidate];
        }];
    }else{
        //不支持
        switch(error.code) {
            case LAErrorPasscodeNotSet:
            {
                NSLog(@"系统未设置密码");
                [RRTools getMainQueueDo:^{
                    invalidBlock();
                }];
                break;
            }
            case LAErrorBiometryNotAvailable:
            {
                NSLog(@"设备Touch ID不可用，例如未打开");
                [RRTools getMainQueueDo:^{
//                    if (IS_X) {
//                        [RRTools showTips:TRANSLATE(@"Face ID is not available")];
//                    }else{
//                        [RRTools showTips:TRANSLATE(@"Touch ID is not available")];
//                    }
                    invalidBlock();
                }];
                break;
            }
            case LAErrorBiometryNotEnrolled:
            {
                NSLog(@"设备Touch ID不可用，用户未录入");
                [RRTools getMainQueueDo:^{
//                    if (IS_X) {
//                        [RRTools showTips:TRANSLATE(@"Face ID is not enrolled")];
//                    }else{
//                        [RRTools showTips:TRANSLATE(@"Touch ID is not enrolled")];
//                    }
                    invalidBlock();
                }];
                break;
            }
            case LAErrorInvalidContext:
            {
                NSLog(@"请求验证出错");
                [RRTools getMainQueueDo:^{
                    [RRTools showTips:@"请求验证出错"];
                    failBlock();
                }];
                break;
            }
            case LAErrorTouchIDLockout:
            {
                NSLog(@"用户错误次数太多，现在被锁住了");
                [RRTools getMainQueueDo:^{
                    NSError* error = nil;
                    //验证提示
                    NSString *localizedReason = TRANSLATE(@"common_label_verify_screen_lock");
                    if([context canEvaluatePolicy:LAPolicyDeviceOwnerAuthentication error:&error]){
                        [context evaluatePolicy:LAPolicyDeviceOwnerAuthentication localizedReason:localizedReason reply:^(BOOL success, NSError * _Nullable error) {
                            if(success){
                                [RRTools checkBiometricsSuccessBlock:successBlock withChangeWayBlock:changeWayBlock withFailBlock:failBlock withCancelBlock:cancelBlock withInvalidBlock:invalidBlock];
                            }else{
                                //取消验证
                                cancelBlock();
                            }
                        }];
                    }else{
                        failBlock();
                    }
                }];
                break;
            }
            default:
            {
                NSLog(@"Touch ID is not supported");
                [RRTools getMainQueueDo:^{
                    if (IS_X) {
                        [RRTools showTips:TRANSLATE(@"security_tip_face_notsupport")];
                    }else{
                        [RRTools showTips:TRANSLATE(@"security_tip_touch_notsupport")];
                    }
                }];
                break;
            }
        }
//        //废止这个context
//        [context invalidate];
    }
}
+(void)checkLoginStatuswithSuccessBlock:(void(^)(void))successBlock cancelBlock:(void(^)(void))cancelBlock{
    NSDate *backgroundTime = [RRTools dateForKey:@"backgroundTime"];
    NSDate *currentTime = [NSDate date];
    float minites = [RRTools dateDifferWithStartTime:backgroundTime EndTime:currentTime];
    if(minites > OVERTIME || [RRTools boolForKey:NEEDVERIFY]){//超过5分钟或者是冷启动
        if([RRTools boolForKey:[RRTools getBiometricsToken]]){
            [RRTools presentLoginLockVCwithSuccessBlock:^{
                //通过登录接口
                [RRTools setBool:NO key:NEEDVERIFY];
                [RRTools removeDateForKey:@"backgroundTime"];
                //校验通过，需要再次记录进入后台时间
                GETTHEAPP
                theApp.noNeedRecord = NO;
                successBlock();
            } cancelBlock:^{
                //校验没通过，不需要再次记录进入后台时间
                GETTHEAPP
                theApp.noNeedRecord = YES;
                cancelBlock();
            }];
        } else {
            [RRTools presentLoginPwdVCwithSuccessBlock:^{
                //通过登录接口
                [RRTools setBool:NO key:NEEDVERIFY];
                [RRTools removeDateForKey:@"backgroundTime"];
                //校验通过，需要再次记录进入后台时间
                GETTHEAPP
                theApp.noNeedRecord = NO;
                successBlock();
            } cancelBlock:^{
                //校验没通过，不需要再次记录进入后台时间
                GETTHEAPP
                theApp.noNeedRecord = YES;
                cancelBlock();
            }];
        }
    }else{
        successBlock();
    }
}

//弹出验证登录锁的页面
+(void)presentLoginLockVCwithSuccessBlock:(void(^)(void))successBlock cancelBlock:(void(^)(void))cancelBlock
{
    [RRTools presentLoginLockFromSlide:NO withSuccessBlock:successBlock cancelBlock:cancelBlock];
}
+(void)presentLoginLockFromSlide:(BOOL)isSlide withSuccessBlock:(void(^)(void))successBlock cancelBlock:(void(^)(void))cancelBlock
{
    [RRTools getMainQueueDo:^{
        
        //        RRUserInfo *userInf = [RRUserInfo objectForKey:@"customerDic"];
        //        if ([userInf.pinSetupFlag isEqualToString:@"0"]) {
        //
        //        } else {
        //
        //        }
        if ([RRTools boolForKey:[RRTools getBiometricsToken]]) {//开启了指纹锁
            RRLoginUnlockVC *loginLockVC = [[RRLoginUnlockVC alloc]init];
            loginLockVC.successBlock = successBlock;
            loginLockVC.cancelBlock = cancelBlock;
            if (isSlide) {
                loginLockVC.from = @"slideVC";
                RRNavigationController *nav = [[RRNavigationController alloc]initWithRootViewController:loginLockVC];
                [[RRTools getCurrentViewController] cw_presentViewController:nav];
            }else{
                RRNavigationController *nav = [[RRNavigationController alloc]initWithRootViewController:loginLockVC];
                [[RRTools getCurrentViewController] presentViewController:nav animated:YES completion:^{
                    
                }];
            }
        }else{
            RRUserInfo *userInf = [RRUserInfo objectForKey:@"customerDic"];
            if ([userInf.pinSetupFlag isEqualToString:@"0"]) {
                RRLoginVC *vc = [[RRLoginVC alloc]init];
                vc.from = @"loginUnlock";
                RRNavigationController *nav = [[RRNavigationController alloc]initWithRootViewController:vc];
    //            [UIApplication sharedApplication].keyWindow.rootViewController = nav;
                [[RRTools getCurrentViewController] presentViewController:nav animated:YES completion:^{
                    
                }];
            } else { RRLoginPwdUnlockVC *loginPwdVC = [[RRLoginPwdUnlockVC alloc]init];
                loginPwdVC.from = @"sessionTimeOut";
                loginPwdVC.successBlock = successBlock;
                loginPwdVC.cancelBlock = cancelBlock;
                RRNavigationController *nav = [[RRNavigationController alloc]initWithRootViewController:loginPwdVC];
                [[RRTools getCurrentViewController] presentViewController:nav animated:YES completion:^{
                    
                }];
            }
        }
    }];
}
//弹出验证登录密码的页面
+(void)presentLoginPwdVCwithSuccessBlock:(void(^)(void))successBlock cancelBlock:(void(^)(void))cancelBlock
{
    [RRTools getMainQueueDo:^{
        RRUserInfo *userInf = [RRUserInfo objectForKey:@"customerDic"];
        if ([userInf.pinSetupFlag isEqualToString:@"0"]) {
            RRLoginVC *vc = [[RRLoginVC alloc]init];
            vc.from = @"loginUnlock";
            RRNavigationController *nav = [[RRNavigationController alloc]initWithRootViewController:vc];
            [UIApplication sharedApplication].keyWindow.rootViewController = nav;
        } else { RRLoginPwdUnlockVC *loginPwdVC = [[RRLoginPwdUnlockVC alloc]init];
            loginPwdVC.from = @"sessionTimeOut";
            loginPwdVC.successBlock = successBlock;
            loginPwdVC.cancelBlock = cancelBlock;
            RRNavigationController *nav = [[RRNavigationController alloc]initWithRootViewController:loginPwdVC];
            [[RRTools getCurrentViewController] presentViewController:nav animated:YES completion:^{
                
            }];
        }
    }];
}
//弹出验证登录密码的页面
+(void)presentLoginPwdVCFromSlide:(BOOL)isSlide withSuccessBlock:(void(^)(void))successBlock cancelBlock:(void(^)(void))cancelBlock
{
    [RRTools getMainQueueDo:^{
        RRLoginPwdUnlockVC *loginPwdVC = [[RRLoginPwdUnlockVC alloc]init];
        loginPwdVC.successBlock = successBlock;
        loginPwdVC.cancelBlock = cancelBlock;
        if (isSlide) {
            loginPwdVC.from = @"slideVC";
            RRNavigationController *nav = [[RRNavigationController alloc]initWithRootViewController:loginPwdVC];
            [[RRTools getCurrentViewController] cw_presentViewController:nav];
        }else{
            RRNavigationController *nav = [[RRNavigationController alloc]initWithRootViewController:loginPwdVC];
            [[RRTools getCurrentViewController] presentViewController:nav animated:YES completion:^{
                
            }];
        }
    }];
}
//弹出收银台页面
+(void)presentCheckoutVCWithProduct:(NSString *)paymentProduct payFinishBlock:(void(^)(NSString *statusCode))successBlock cancelBlock:(void(^)(NSString *statusCode))cancelBlock
{
    RRHUDView * hud = [RRHUDView defaultHUDViewForWindow];
    [TOPWINDOW addSubview:hud];
    [hud show];
    GETTHEAPP
    NSMutableDictionary *jsonDic = [NSMutableDictionary dictionary];
    [jsonDic setObject:theApp.goodsInfString forKey:@"payToken"];
    //    [jsonDic setObject:_token forKey:@"payToken"];
    [jsonDic setObject:@"30" forKey:@"trxTransType"];
    //查询订单信息-查询营销信息-查询支付方式列表-查询手续费
    [RRHttpRequest postSend:PAY_ORDER_GET urlString:nil jsonDic:jsonDic success:^(RRNetworkModel *model) {
        [hud hide];
        if ([RRTools isSuccess:model ]) {
            [RRTools getMainQueueDo:^{
                PaymentOrderVC *vc = [[PaymentOrderVC alloc]init];
                vc.paymentProduct = paymentProduct;
                vc.payFinished = ^(NSString * _Nonnull statusCode) {
                    successBlock(statusCode);
                };
                vc.cancelPay = ^(NSString * _Nonnull statusCode) {
                    cancelBlock(statusCode);
                };
                RRNavigationController *nav = [[RRNavigationController alloc]initWithRootViewController:vc];
                [[RRTools getCurrentViewController] presentViewController:nav animated:YES completion:^{
                    
                }];
            }];
        } else {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [[NSNotificationCenter defaultCenter] postNotificationName:@"refreshScan" object:nil];
            });
        }
    } failure:^(NSError *error) {
        [hud hide];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [[NSNotificationCenter defaultCenter] postNotificationName:@"refreshScan" object:nil];
        });
    } time:60 encrypt:NO];
}
//弹出自动签约页面
+(void)presentBuyAirtimeBundleVCWithPayFinishBlock:(void(^)(NSString *statusCode))successBlock cancelBlock:(void(^)(NSString *statusCode))cancelBlock
{
    [RRTools getMainQueueDo:^{
        BuyAirtimeBundleVC *vc = [[BuyAirtimeBundleVC alloc]init];
        vc.payFinished = ^(NSString * _Nonnull statusCode) {
            successBlock(statusCode);
        };
        vc.cancelPay = ^(NSString * _Nonnull statusCode) {
            cancelBlock(statusCode);
        };
        RRNavigationController *nav = [[RRNavigationController alloc]initWithRootViewController:vc];
        [[RRTools getCurrentViewController] presentViewController:nav animated:YES completion:^{
            
        }];
    }];
}

//获取相机权限
+(void)requestCameraPemissionWithResult:(void(^)( BOOL granted))completion
{
    if ([AVCaptureDevice respondsToSelector:@selector(authorizationStatusForMediaType:)])
    {
        AVAuthorizationStatus permission =
        [AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo];
        switch (permission) {
            case AVAuthorizationStatusAuthorized:
                completion(YES);
                break;
            case AVAuthorizationStatusDenied:
                completion(NO);
                break;
            case AVAuthorizationStatusRestricted:
                completion(NO);
                break;
            case AVAuthorizationStatusNotDetermined:
            {
                [AVCaptureDevice requestAccessForMediaType:AVMediaTypeVideo completionHandler:^(BOOL granted) {
                    if (granted) {
                        completion(YES);
                    }else{
                        completion(NO);
                    }
                }];
                break;
            }
            default:
                break;
        }
    }
}
//获取相册
+(void)requestPhotoLibraryPemissionWithResult:(void(^)( BOOL granted))completion
{
    if([PHPhotoLibrary respondsToSelector:@selector(authorizationStatus)]){
        PHAuthorizationStatus status = [PHPhotoLibrary authorizationStatus];
        switch (status) {
            case PHAuthorizationStatusAuthorized:
                completion(YES);
                break;
            case PHAuthorizationStatusDenied:
                completion(NO);
                break;
            case PHAuthorizationStatusRestricted:
                completion(NO);
                break;
            case PHAuthorizationStatusNotDetermined:
            {
                [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus status) {
                    switch (status) {
                        case PHAuthorizationStatusAuthorized:
                            completion(YES);
                            break;
                        case PHAuthorizationStatusDenied:
                            completion(NO);
                            break;
                        case PHAuthorizationStatusRestricted:
                            completion(NO);
                            break;
                        default:
                            break;
                    }
                }];
            }
            default:
                break;
        }
    }
}

#pragma mark 获取控制器
//获取根控制器
+ (UIViewController *)getRootViewController{
    
    UIWindow* window = [[[UIApplication sharedApplication] delegate] window];
    NSAssert(window, @"The window is empty");
    return window.rootViewController;
}
//获取当前控制器
+ (UIViewController *)getCurrentViewController{
    
    UIViewController* currentViewController = [self getRootViewController];
    BOOL runLoopFind = YES;
    while (runLoopFind) {
        if (currentViewController.presentedViewController) {
            
            currentViewController = currentViewController.presentedViewController;
        } else if ([currentViewController isKindOfClass:[RRNavigationController class]]) {
            
            RRNavigationController* navigationController = (RRNavigationController* )currentViewController;
            currentViewController = [navigationController.childViewControllers lastObject];
            
        } else if ([currentViewController isKindOfClass:[UITabBarController class]]) {
            
            UITabBarController* tabBarController = (UITabBarController* )currentViewController;
            currentViewController = tabBarController.selectedViewController;
        } else {
            
            NSUInteger childViewControllerCount = currentViewController.childViewControllers.count;
            if (childViewControllerCount > 0) {
                
                currentViewController = currentViewController.childViewControllers.lastObject;
                
                return currentViewController;
            } else {
                return currentViewController;
            }
        }
        
    }
    return currentViewController;
}
//回到主线程操作
+(void)getMainQueueDo:(void (^)(void))block
{
    dispatch_async(dispatch_get_main_queue(), block);
}

#pragma mark 数据处理
+ (NSString *)nvl:(id)strValue
{
    if ([strValue isKindOfClass:[NSNull class]]) {
        return @"";
    }
    if (strValue == [NSNull null] || strValue == nil) {
        return @"";
    }
    return strValue;
}


+ (NSString *)nvl:(NSString *)strValue replace:(NSString *)replaceStr
{
    if (strValue == [NSNull null] || strValue == nil || [strValue isEqualToString:@""]) {
        return replaceStr;
    }
    return strValue;
}

//金额格式化
+(NSString *)formatAmount:(NSString *)amount
{
    amount = [amount stringByReplacingOccurrencesOfString:@"," withString:@""];
    amount = [amount stringByReplacingOccurrencesOfString:@"-" withString:@""];
    double s = amount.doubleValue;
    NSString * formatAmount = [NSString stringWithFormat:@"%.2f",s];
    return formatAmount;
    
}
+(NSString*)strmethodComma:(NSString*)string
{
    NSString *flag = @"";
    string = [string stringByReplacingOccurrencesOfString:@"," withString:@""];
    if(string.doubleValue == 0){
        return string;
    }
    if (string.doubleValue < 0) {   //负数需要去掉负号再格式化
        flag = @"-";
        string = [string substringFromIndex:1];
    }
    NSNumber *number = [NSNumber numberWithDouble:string.doubleValue];
    NSNumberFormatter *formatter = [[NSNumberFormatter alloc]init];
    //自定义格式化样式
//    formatter.positiveFormat = @"###,###,###,##0.00";
    //获取当前的语言为中文，英文，或其他语言
    NSString *currentLanguage = [NSBundle currentLanguage];
    if ([currentLanguage hasPrefix:@"zh"]) {
        //设置地区: zh-中国
        formatter.locale = [NSLocale localeWithLocaleIdentifier:@"zh"];
        formatter.positiveFormat = @"#,##0.00"; // 正数格式
    }else if([currentLanguage hasPrefix:@"sn"]){
        formatter.locale = [NSLocale localeWithLocaleIdentifier:@"sn"];
        formatter.positiveFormat = @"#,##0.00"; // 正数格式
    }else if([currentLanguage hasPrefix:@"nr"]){
//        formatter.locale = [NSLocale localeWithLocaleIdentifier:@"nr"];
        formatter.positiveFormat = @"#,##0.00"; // 正数格式
    }else {
        formatter.locale = [NSLocale localeWithLocaleIdentifier:@"en_GB"];
        formatter.positiveFormat = @"#,##0.00"; // 正数格式
    }
    NSString *formatAmount = [formatter stringFromNumber:number];
    formatAmount = [NSString stringWithFormat:@"%@%@",flag,formatAmount];
    return formatAmount;
}
//+(NSString*)strmethodComma:(NSString*)string
//{
//    NSString *flag = @"";
//    NSMutableString * string1 = [NSMutableString stringWithFormat:@"%@",string];
//    for (int i = 0; i<string1.length; i++) {
//
//        if ([[string1 substringWithRange:NSMakeRange(i, 1)] isEqualToString:@","]) {
//            [string1 deleteCharactersInRange:NSMakeRange(i, 1)];
//        }
//    }
//    string = [NSString stringWithFormat:@"%@",string1];
//
//    if (string.doubleValue < 0) {   //负数需要去掉负号再格式化
//        flag = @"-";
//        string = [string substringFromIndex:1];
//    }
//    float s = string.floatValue;
//    NSNumber *number = [NSNumber numberWithDouble:string.doubleValue];
//    NSNumberFormatter *formatter = [[NSNumberFormatter alloc]init];
//    formatter.numberStyle = kCFNumberFormatterCurrencyStyle;
//
//    NSString *formatAmount = [formatter stringFromNumber:number];
//    NSMutableString * mutableString = [NSMutableString stringWithString:formatAmount];
//
//    formatAmount = [formatAmount substringFromIndex:1];
//    formatAmount = [NSString stringWithFormat:@"%@%@",flag,formatAmount];
//    return formatAmount;
//    //   return string;
//}

+(NSString *)monthForEnglish:(NSString*)month
{
    if([month isEqualToString:@"01"]){
        month = TRANSLATE(@"Date_select_jan");
    } else if([month isEqualToString:@"02"]){
        month = TRANSLATE(@"Date_select_feb");
    } else if([month isEqualToString:@"03"]){
        month = TRANSLATE(@"Date_select_mar");
    } else if([month isEqualToString:@"04"]){
        month = TRANSLATE(@"Date_select_apr");
    } else if([month isEqualToString:@"05"]){
        month = TRANSLATE(@"Date_select_may");
    } else if([month isEqualToString:@"06"]){
        month = TRANSLATE(@"Date_select_jun");
    } else if([month isEqualToString:@"07"]){
        month = TRANSLATE(@"Date_select_jul");
    } else if([month isEqualToString:@"08"]){
        month = TRANSLATE(@"Date_select_aug");
    } else if([month isEqualToString:@"09"]){
        month = TRANSLATE(@"Date_select_sep");
    } else if([month isEqualToString:@"10"]){
        month = TRANSLATE(@"Date_select_oct");
    } else if([month isEqualToString:@"11"]){
        month = TRANSLATE(@"Date_select_nov");
    } else{
        month = TRANSLATE(@"Date_select_dec");
    }
    return month;
}
+(NSString *)monthFromEnglish:(NSString *)month
{
    if([month isEqualToString:TRANSLATE(@"Date_select_jan")]){
        month = @"01";
    } else if([month isEqualToString:TRANSLATE(@"Date_select_feb")]){
        month = @"02";
    } else if([month isEqualToString:TRANSLATE(@"Date_select_mar")]){
        month = @"03";
    } else if([month isEqualToString:TRANSLATE(@"Date_select_apr")]){
        month = @"04";
    } else if([month isEqualToString:TRANSLATE(@"Date_select_may")]){
        month = @"05";
    } else if([month isEqualToString:TRANSLATE(@"Date_select_jun")]){
        month = @"06";
    } else if([month isEqualToString:TRANSLATE(@"Date_select_jul")]){
        month = @"07";
    } else if([month isEqualToString:TRANSLATE(@"Date_select_aug")]){
        month = @"08";
    } else if([month isEqualToString:TRANSLATE(@"Date_select_sep")]){
        month = @"09";
    } else if([month isEqualToString:TRANSLATE(@"Date_select_oct")]){
        month = @"10";
    } else if([month isEqualToString:TRANSLATE(@"Date_select_nov")]){
        month = @"11";
    } else{
        month = @"12";
    }
    return month;
}
//将日期转换
+(NSString *)formatDate:(NSString *)date withOldFormat:(NSString *)old newFormat:(NSString *)format{
    if(date != nil && date.length>6){
        NSDateFormatter *oldFormatter = [[NSDateFormatter alloc] init];
        oldFormatter.dateFormat = old;
        NSDate *time = [oldFormatter dateFromString:date];
        NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
        formatter.dateFormat = format;
        return [formatter stringFromDate:time];
    }
    return @"";
}
//将201901转换成Jan. 2019
+(NSString *)formatMonthYear:(NSString *)date
{
    // 获取当前时间
    NSDate *now = [NSDate date];
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSUInteger unitFlags = NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay | NSCalendarUnitHour | NSCalendarUnitMinute| NSCalendarUnitSecond;
    NSDateComponents *dateComponent = [calendar components:unitFlags fromDate:now];
    NSInteger year = [dateComponent year];
    NSInteger month  = [dateComponent month];
    NSString *yearStr = [NSString stringWithFormat:@"%ld",(long)year];
    NSString *monthStr = [NSString stringWithFormat:@"%.2ld",(long)month];
    NSString *dateYear = [date substringToIndex:4];
    NSString *dateMon = [date substringFromIndex:4];
    if ([yearStr isEqualToString:dateYear]) {
        if ([monthStr isEqualToString:dateMon]) {
            return TRANSLATE(@"history_label_this_month");
        }
        //本年非本月
        return [NSString stringWithFormat:@"%@%@",[RRTools monthForEnglish:dateMon],@"."];
        
    }else{
        //非本年本月
        return [NSString stringWithFormat:@"%@%@ %@",[RRTools monthForEnglish:dateMon],@".",dateYear];
    }
}
//获取当月开始时间和结束时间
+(NSMutableDictionary *)getStartAndEndTimeWithYear:(NSString *)year withMonth:(NSString *)month {
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSTimeZone *timeZone = [NSTimeZone localTimeZone]; // 获取本地时区

    // 创建日期组件并设置年份和月份
    NSDateComponents *components = [[NSDateComponents alloc] init];
    components.year = [year intValue];
    components.month = [month intValue];
    components.day = 1;
    
    // 获取指定月份的开始时间
    NSDate *startOfMonth = [calendar dateFromComponents:components];
    
    // 获取下个月的开始时间，然后减去一天就是指定月份的结束时间
    components.month++;
    NSDate *startOfNextMonth = [calendar dateFromComponents:components];
    NSDate *endOfMonth = [startOfNextMonth dateByAddingTimeInterval:-1];
    
    // 设置时区
    calendar.timeZone = timeZone;
    
    // 格式化日期并输出
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
    [dateFormatter setTimeZone:timeZone];
    
    NSMutableDictionary *dic = [[NSMutableDictionary alloc] initWithDictionary:@{@"startTime":[dateFormatter stringFromDate:startOfMonth],@"endTime":[dateFormatter stringFromDate:endOfMonth]}];
    return dic;
}
//将201901转换成Jan. 2019
+(NSString *)formatMonthYear:(NSString *)date withLanguage:(NSString *)language
{
    NSString *dateYear = [date substringToIndex:4];
    NSString *dateMon = [date substringWithRange:NSMakeRange(5, 2)];
    if(language != nil && [language hasPrefix:@"zh"]) {
        NSString *monStr = dateMon;
        if([dateMon hasPrefix:@"0"]){
            monStr = [dateMon stringByReplacingOccurrencesOfString:@"0" withString:@""];
        }
        return [NSString stringWithFormat:@"%@年%@",dateYear, [RRTools monthForEnglish:dateMon]];
    } else {
        return [NSString stringWithFormat:@"%@%@ %@",[RRTools monthForEnglish:dateMon],@",",dateYear];
    }
}

//设置UItextField的placeholder样式
+(void)setPlaceHolderStyleWithTextField:(UITextField *)textField placeholder:(NSString *)placeholder font:(UIFont *)font textColor:(UIColor *)textColor
{
    NSString *placeHolderStr = placeholder;
    NSAttributedString *attributedString = [[NSAttributedString alloc] initWithString:placeHolderStr attributes:@{NSFontAttributeName : font,NSForegroundColorAttributeName : textColor}];
    textField.attributedPlaceholder = attributedString;
}

//获取条形码
+(UIImage *)getBarCodeImageWith:(NSString *)code withSize:(CGSize)size
{
    if([code isEqualToString:@""])return NULL;
    CIFilter *qrFilter = [CIFilter filterWithName:@"CICode128BarcodeGenerator"];
    NSData *contentData = [code dataUsingEncoding:NSUTF8StringEncoding];
    [qrFilter setValue:contentData forKey:@"inputMessage"];
    [qrFilter setValue:@(0.00) forKey:@"inputQuietSpace"];
    CIImage *image = qrFilter.outputImage;
    
    CGRect integralRect = CGRectIntegral(image.extent);
    CGFloat scale = MIN(size.width/CGRectGetWidth(integralRect), size.height/CGRectGetHeight(integralRect));
    
    size_t width = CGRectGetWidth(integralRect)*scale;
    size_t height = CGRectGetHeight(integralRect)*scale;
    CGColorSpaceRef colorSpaceRef = CGColorSpaceCreateDeviceGray();
    CGContextRef bitmapRef = CGBitmapContextCreate(nil, width, height, 8, 0, colorSpaceRef, (CGBitmapInfo)kCGImageAlphaNone);
    CIContext *context = [CIContext contextWithOptions:nil];
    CGImageRef bitmapImage = [context createCGImage:image fromRect:integralRect];
    CGContextSetInterpolationQuality(bitmapRef, kCGInterpolationNone);
    CGContextScaleCTM(bitmapRef, scale, scale);
    CGContextDrawImage(bitmapRef, integralRect, bitmapImage);
    
    CGImageRef scaledImage = CGBitmapContextCreateImage(bitmapRef);
    CGContextRelease(bitmapRef);
    CGImageRelease(bitmapImage);
    return [UIImage imageWithCGImage:scaledImage];
}
//获取二维码
+(UIImage *)getQrImageWith:(NSString *)code withSize:(CGFloat)size
{
    CIFilter *qrFilter = [CIFilter filterWithName:@"CIQRCodeGenerator"];
    NSData *contentData = [code dataUsingEncoding:NSUTF8StringEncoding];
    [qrFilter setValue:contentData forKey:@"inputMessage"];
    [qrFilter setValue:@"H" forKey:@"inputCorrectionLevel"];
    CIImage *image = qrFilter.outputImage;
    CGRect integralRect = CGRectIntegral(image.extent);
    CGFloat scale = MIN(size/CGRectGetWidth(integralRect), size/CGRectGetHeight(integralRect));
    
    size_t width = CGRectGetWidth(integralRect)*scale;
    size_t height = CGRectGetHeight(integralRect)*scale;
    CGColorSpaceRef colorSpaceRef = CGColorSpaceCreateDeviceGray();
    CGContextRef bitmapRef = CGBitmapContextCreate(nil, width, height, 8, 0, colorSpaceRef, (CGBitmapInfo)kCGImageAlphaNone);
    CIContext *context = [CIContext contextWithOptions:nil];
    CGImageRef bitmapImage = [context createCGImage:image fromRect:integralRect];
    CGContextSetInterpolationQuality(bitmapRef, kCGInterpolationNone);
    CGContextScaleCTM(bitmapRef, scale, scale);
    CGContextDrawImage(bitmapRef, integralRect, bitmapImage);
    
    CGImageRef scaledImage = CGBitmapContextCreateImage(bitmapRef);
    CGContextRelease(bitmapRef);
    CGImageRelease(bitmapImage);
    return [UIImage imageWithCGImage:scaledImage];
}
//获取首字母
+ (NSString *)firstLetter:(NSString *)string;
{
    NSLog(@"%hu",[string characterAtIndex:0]);
    if ([string characterAtIndex:0] >= 'A' && [string characterAtIndex:0] <= 'Z') {
        //大写字母
        return [NSString stringWithFormat:@"%c",[string characterAtIndex:0]];
        
    }else if ([string characterAtIndex:0] >= 'a' && [string characterAtIndex:0] <= 'z') {
        //小写字母
        return [[NSString stringWithFormat:@"%c",[string characterAtIndex:0]] uppercaseString];
        
    }else{
        NSMutableString *str = [NSMutableString stringWithString:string];
        CFStringTransform((CFMutableStringRef)str,NULL, kCFStringTransformMandarinLatin,NO);
        CFStringTransform((CFMutableStringRef)str,NULL, kCFStringTransformStripDiacritics,NO);
        NSString *pinYin = [str capitalizedString];
//        NSString *firatCharactorReturn = [pinYin substringToIndex:1];
//        s = [NSMutableString string];
//        for (int i = 0; i < pinYin.length; i++) {
        //        }
        if (([pinYin characterAtIndex:0] >= 'A' && [pinYin characterAtIndex:0] <= 'Z')) {
            return [pinYin substringToIndex:1];
        }else{
            return @"#";
        }
        return [pinYin substringToIndex:1];
    }
    return nil;
}
//在字符串中每四位增加一个空格
+(NSString *)insertSpacesEveryFourCharWithString:(NSString *)originalStr
{
    NSString *returnStr;
    if (originalStr.length > 0) {
        int m = (int)originalStr.length / 4;
        int n = (int)originalStr.length % 4;
        if(n == 0){
            m = m - 1;
        }
        NSMutableString *mutStr = [NSMutableString stringWithString:originalStr];
        for (int i = 0; i < m; i++) {
            [mutStr insertString:@" " atIndex:i * 5 + 4];
        }
        returnStr = [NSString stringWithFormat:@"%@",mutStr];
    }else{
        return originalStr;
    }
    return returnStr;
}
//条形码分隔规则
+(NSString *)barCodeStrWithSpace:(NSString *)originalStr
{
    NSString *returnStr;
    if (originalStr.length > 0) {
        NSString *firstStr = [originalStr substringToIndex:3];
        NSString *secondStr = [originalStr substringFromIndex:3];
        int m = (int)secondStr.length / 4;
        int n = (int)secondStr.length % 4;
        if(n == 0){
            m = m - 1;
        }
        NSMutableString *mutStr = [NSMutableString stringWithString:secondStr];
        for (int i = 0; i < m; i++) {
            [mutStr insertString:@" " atIndex:i * 5 + 4];
        }
        returnStr = [NSString stringWithFormat:@"%@ %@",firstStr,mutStr];
    }else{
        return originalStr;
    }
    return returnStr;
}
//判断两个时间相差的分钟数
+(double)dateDifferWithStartTime:(NSDate *)startTime EndTime:(NSDate *)endTime
{
    //计算时间间隔（单位是秒）
    NSTimeInterval time = [endTime timeIntervalSinceDate:startTime];//开始日期和结束日期的差距
    //计算天数、时、分、秒
    double minites = ((double)time)/(60);//开始日期和结束日期的差距天数 -为早 +为晚
    NSLog(@"两个日期相差%f分钟",minites);
    return minites;
}
//手机号掩码显示
+(NSString *)mobileNoMaskWithPhone:(NSString *)phoneNo areaCode:(NSString *)areaCode
{
    if (![areaCode containsString:@"+"]) {
        areaCode = [NSString stringWithFormat:@"%@%@",@"+",areaCode];
    }
    NSString *mobile = phoneNo;
    if (mobile.length > 7) {
        mobile = [NSString stringWithFormat:@"%@%@%@",[mobile substringToIndex:2],@"*****",[mobile substringFromIndex:mobile.length - 2]];
    }
    mobile = [NSString stringWithFormat:@"%@ %@",areaCode,mobile];
    return mobile;
}
//用户名掩码显示
+(NSString *)userNameMaskWithName:(NSString *)name
{
    // 分割字符串
    NSArray *components = [name componentsSeparatedByString:@" "];
    
    // 提取前缀
    NSString *prefix = @"";
    if (components.count > 0) {
        NSString *firstComponent = (NSString *)components[0];
        if (firstComponent.length > 2) {
            prefix = [firstComponent substringToIndex:MIN(2, firstComponent.length)];
            prefix = [NSString stringWithFormat:@"%@%@",prefix,@"****"];
        } else {
            prefix = firstComponent;
        }
        
    }
    
    // 提取后缀
    NSString *suffix = @"";
    if (components.count > 0) {
        NSString *lastComponent = (NSString *)[components lastObject];
        if (lastComponent.length > 2) {
            suffix = [lastComponent substringFromIndex:MAX(0, lastComponent.length - 2)];
            suffix = [NSString stringWithFormat:@"%@%@",@"****",suffix];
        } else {
            suffix = lastComponent;
        }
    }
    
    // 处理中间字段
    NSMutableArray *middleComponents = [NSMutableArray array];
    for (int i = 1; i < components.count - 1; i++) {
        NSString *component = (NSString *)components[i];
        if (component.length > 0) {
            [middleComponents addObject:@"****"];
        } else {
            [middleComponents addObject:@""];
        }
    }
    
    // 组合结果
    NSMutableString *result = [NSMutableString string];
    [result appendString:prefix];
    [result appendString:@" "];
    [result appendString:[middleComponents componentsJoinedByString:@" "]];
    if (middleComponents.count > 0) {
        [result appendString:@" "];
    }
    [result appendString:suffix];
    
    return result;
}
//根据图片名从bundle获取图片
+(UIImage *)getImageWithName:(NSString *)imageName
{
    NSString *bundlePath = [[NSBundle mainBundle] pathForResource:@"ShareSDK" ofType :@"bundle"];
    NSString *imgPath= [bundlePath stringByAppendingPathComponent:imageName];
    UIImage *image=[UIImage imageWithContentsOfFile:imgPath];
    return image;
}
//将图片按照图片名进行存储
+(BOOL)savaImage:(UIImage *)tempImage WithName:(NSString *)imageName
{
    NSData* imageData = UIImagePNGRepresentation(tempImage);
    NSArray *documentPaths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory,NSUserDomainMask,YES);
    NSString * holderPath = [NSString stringWithFormat:@"%@/temp/img", documentPaths[0]];
    NSString *theRealPath = [NSString stringWithFormat:@"%@/%@.png", holderPath,imageName];
    NSFileManager *fileManager = [NSFileManager defaultManager];
    if (![fileManager fileExistsAtPath:holderPath]) {
        //创建Zips文件夹
        [fileManager createDirectoryAtPath:holderPath
               withIntermediateDirectories:YES
                                attributes:nil
                                     error:nil];
    }
    
    if ([fileManager fileExistsAtPath:theRealPath]) {
        [fileManager removeItemAtPath:theRealPath error:nil];
    }
    BOOL success = [imageData writeToFile:theRealPath atomically:NO];
    if (success) {
        return YES;
    }else{
        [RRTools showTips:@"图片获取失败"];
        return NO;
    }
}
//按照图片名从文件系统中取图片
+(UIImage *)getImageDataWithName:(NSString *)imageName
{
    NSArray *documentPaths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory,NSUserDomainMask,YES);
    NSString * holderPath = [NSString stringWithFormat:@"%@/temp/img", documentPaths[0]];
    NSString *theRealPath = [NSString stringWithFormat:@"%@/%@.png", holderPath,imageName];
    NSData * data = [NSData dataWithContentsOfFile:theRealPath];
    UIImage * image = [UIImage imageWithData:data];
    return image;
}
//按照图片名从文件系统中取图片路径
+(NSString *)getImageUrlWithName:(NSString *)imageName
{
    NSArray *documentPaths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory,NSUserDomainMask,YES);
    NSString * holderPath = [NSString stringWithFormat:@"%@/temp/img", documentPaths[0]];
    NSString *theRealPath = [NSString stringWithFormat:@"%@/%@.png", holderPath,imageName];
    return theRealPath;
}
//打开外部浏览器
+ (void)openExternalBrowserWithUrlString:(NSString *)urlString
{
    NSURL *URL = [NSURL URLWithString:urlString];
    if ([[[UIDevice currentDevice]systemVersion] floatValue] >= 10.0) {
        /**
         ios 10 以后使用  openURL: options: completionHandler:
         这个函数异步执行，但在主队列中调用 completionHandler 中的回调
         openURL:打开的网址
         options:用来校验url和applicationConfigure是否配置正确，是否可用。
         唯一可用@{UIApplicationOpenURLOptionUniversalLinksOnly:@YES}。
         不需要不能置nil，需@{}为置空。
         ompletionHandler:如不需要可置nil
         **/
        [[UIApplication sharedApplication] openURL:URL options:@{} completionHandler:^(BOOL success) {
            
        }];
    }else{
        /**
         ios 9 之前使用
         openURL:打开的网址
         **/
        [[UIApplication sharedApplication]openURL:URL];
    }
}
//银行卡号掩码展示
+(NSString *)bankCardIdMaskWithCardId:(NSString *)cardId {
    NSString *maskCardId;
    if (cardId.length >= 6) {
        maskCardId = [NSString stringWithFormat:@"%@%@%@",[cardId substringToIndex:2],@"****",[cardId substringFromIndex:cardId.length - 3]];
    }else{
        maskCardId = cardId;
    }
    return maskCardId;
}
//商户外部订单号掩码展示
+(NSString *)merchantOrderNoMaskWithOrderNo:(NSString *)merOrderNo {
    NSString *maskMerOrderNo;
    if (merOrderNo.length >= 4) {
        maskMerOrderNo = [NSString stringWithFormat:@"%@%@",@"****",[merOrderNo substringFromIndex:merOrderNo.length - 4]];
    }else{
        maskMerOrderNo = merOrderNo;
    }
    return maskMerOrderNo;
}

//JSON字符串转化为字典
+ (NSDictionary *)dictionaryWithJsonString:(NSString *)jsonString {
    if (jsonString == nil) {
        return nil;
    }
    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    NSError *err;
    NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData
                                                        options:NSJSONReadingMutableContainers
                                                          error:&err];
    if(err) {
        NSLog(@"json解析失败：%@",err);
        return nil;
    }
    return dic;
}

// 字典转json字符串方法
+ (NSString *)convertToJsonData:(NSDictionary *)dict
{
    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dict options:NSJSONWritingPrettyPrinted error:&error];
    NSString *jsonString;
    if (!jsonData) {
        NSLog(@"%@",error);
    }else{
        jsonString = [[NSString alloc]initWithData:jsonData encoding:NSUTF8StringEncoding];
    }
    NSMutableString *mutStr = [NSMutableString stringWithString:jsonString];
    NSRange range = {0,jsonString.length};
    //去掉字符串中的空格
    [mutStr replaceOccurrencesOfString:@" " withString:@"" options:NSLiteralSearch range:range];
    NSRange range2 = {0,mutStr.length};
    //去掉字符串中的换行符
    [mutStr replaceOccurrencesOfString:@"\n" withString:@"" options:NSLiteralSearch range:range2];
    return mutStr;
}

+ (BOOL)isEmpty:(NSString *)value
{
    if (value == nil) {
        return YES;
    }
    value = [NSString stringWithFormat:@"%@",value];
    if (value == [NSNull null] || [value isEqualToString:@""] || value == nil) {
        return YES;
    }
    return NO;
}

+ (BOOL)isDicEmpty:(NSDictionary *)value
{
    if (value !=nil && [value isKindOfClass:[NSDictionary class]]) {
        return NO;
    }
 
    return YES;
}
#pragma mark 弹框系列
//拨打电话
+(void)callPhoneNumber:(NSString *)phoneNo
{
    [[UIApplication sharedApplication] openURL:[NSURL URLWithString:[NSString stringWithFormat:@"tel://%@",phoneNo]] options:@{} completionHandler:^(BOOL success) {
            
    }];
}
+(void)nslog:(NSString *)s
{
    NSString * str = s;
    NSLog(@"%@",str);
}
//弹出提示tip
+ (void)showTips:(NSString *)msg
{
    msg = NSLocalizedString(msg, nil);
    [RRTools getMainQueueDo:^{
        static NSMutableArray * viewArray;
        if (!viewArray) {
            viewArray = [NSMutableArray array];
        }
        if (viewArray.count > 0) {
            for (UIView * subView in viewArray) {
                subView.hidden = YES;
            }
        }
        UIView * view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, VIEW_WIDTH, VIEW_HEIGHT)];
        view.backgroundColor = [UIColor clearColor];
        view.userInteractionEnabled = NO;
        UILabel * label = [[UILabel alloc]init];
        label.backgroundColor = [UIColor blackColor];
        label.textColor = [UIColor whiteColor];
        label.textAlignment = NSTextAlignmentCenter;
        label.font = [UIFont systemFontOfSize:font_Size];
        label.text = msg;
        label.layer.cornerRadius = 5;
        label.layer.masksToBounds = YES;
        label.alpha = 0.9;
        [view addSubview:label];
        if (msg.length >10) {
            NSInteger lineNUM = (msg.length+1)/10+1;
            label.frame = CGRectMake((VIEW_WIDTH - 10*(font_Size+1))/2.0, VIEW_HEIGHT/2.0, 10*(font_Size+1), (font_Size+1)*lineNUM+20);
            label.numberOfLines = lineNUM;
        }else{
            label.frame = CGRectMake((VIEW_WIDTH - 10*(font_Size+1))/2.0, VIEW_HEIGHT/2.0, 10*(font_Size+1), (font_Size+1)+20);
        }
        label.center = view.center;
//        [[UIApplication sharedApplication].keyWindow endEditing:YES];
        UIWindow *win = [[UIApplication sharedApplication] keyWindow];
        //    UIView *topView = [win.subviews firstObject];
        [win addSubview:view];
        //    [topView addSubview:view];
        [viewArray addObject:view];
        [UIView animateWithDuration:3.0 animations:^{
            view.alpha = 0.8;
        }completion:^(BOOL finished) {
            view.hidden = YES;
            [viewArray removeObject:view];
            [view removeFromSuperview];
        }];
    }];
}
//弹框提示
+(void)showAlertWithTitle:(nullable NSString *)title message:(nullable NSString *)message okTtitle:(nonnull NSString *)okTitle okBlock:(nonnull void (^)(void))okBlock cancelTitle:(nonnull NSString *)cancelTitle cancelBlock:(nonnull void (^)(void))cancelBlock {
    [RRTools showAlertWithTitle:title message:message okTtitle:okTitle okBlock:okBlock cancelTitle:cancelTitle cancelBlock:cancelBlock titleSeparated:NO];
}
+(void)showAlertWithTitle:(nullable NSString *)title message:(nullable NSString *)message okTtitle:(nonnull NSString *)okTitle okBlock:(nonnull void (^)(void))okBlock cancelTitle:(nonnull NSString *)cancelTitle cancelBlock:(nonnull void (^)(void))cancelBlock titleSeparated:(BOOL)titleSeparated
{
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:UIAlertControllerStyleAlert];
    UIView *subView = (UIView *)alertController.view.subviews[0];
    UIView *alertContentView = (UIView *)subView.subviews[0];
    alertContentView.backgroundColor = [UIColor whiteColor];
    alertContentView.layer.cornerRadius = 18.0;
    
//    //改变title的大小和颜色
//    NSMutableAttributedString *titleAtt = [[NSMutableAttributedString alloc] initWithString:title];
//    [titleAtt addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:16] range:NSMakeRange(0, title.length)];
//    [titleAtt addAttribute:NSForegroundColorAttributeName value:[UIColor orangeColor] range:NSMakeRange(0, title.length)];
//    [alertController setValue:titleAtt forKey:@"attributedTitle"];
    //更改title字体颜色及样式
    NSMutableAttributedString *attr1 = [[NSMutableAttributedString alloc]initWithString:title attributes:@{NSForegroundColorAttributeName:MAIN_TEXT_COLOR,NSFontAttributeName:FONT(16*FONT_SIZE)}];
    [alertController setValue:attr1 forKey:@"attributedTitle"];
    
    //改变message的大小和颜色
    NSArray<NSString *> *messageArr = [message componentsSeparatedByString:@"?"];
    if (messageArr.count > 1 && titleSeparated) {
        NSMutableAttributedString *messageAtt = [[NSMutableAttributedString alloc] initWithString:message];
        [messageAtt addAttribute:NSFontAttributeName value:FONT(16*FONT_SIZE) range:NSMakeRange(0, messageArr[0].length + 1)];
        [messageAtt addAttribute:NSForegroundColorAttributeName value:COLOR(37, 37, 37) range:NSMakeRange(0, messageArr[0].length + 1)];
        
        [messageAtt addAttribute:NSFontAttributeName value:FONT(12*FONT_SIZE) range:NSMakeRange(messageArr[0].length + 1, messageArr[1].length)];
        [messageAtt addAttribute:NSForegroundColorAttributeName value:COLOR(237, 39, 62) range:NSMakeRange(messageArr[0].length + 1, messageArr[1].length)];
        [alertController setValue:messageAtt forKey:@"attributedMessage"];
    }else{
        NSMutableAttributedString *attr = [[NSMutableAttributedString alloc]initWithString:message attributes:@{NSForegroundColorAttributeName:SUB_TEXT_COLOR,NSFontAttributeName:FONT(14*FONT_SIZE)}];
        [alertController setValue:attr forKey:@"attributedMessage"];
    }
    if(okTitle.length > 0){
        UIAlertAction * okAction = [UIAlertAction actionWithTitle:okTitle style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            if (okBlock) {
                okBlock();
            }
        }];
        //设置okAction的title颜色
        [okAction setValue:SUB_TEXT_COLOR forKey:@"titleTextColor"];
        
        [alertController addAction:okAction];
    }
    if(cancelTitle.length > 0){
        UIAlertAction * cancelAction = [UIAlertAction actionWithTitle:cancelTitle style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            if (cancelBlock) {
                cancelBlock();
            }
        }];
        //设置okAction的title颜色
        [cancelAction setValue:MAIN_BLUE forKey:@"titleTextColor"];
        [alertController addAction:cancelAction];
    }
    
    [[RRTools getCurrentViewController] presentViewController:alertController animated:YES completion:^{
        
    }];
}

+(void)showAlertWithokBlock:(void(^)())okBlock fromController:(UIViewController *)controller
{
    // 1.实例化UIAlertController对象
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"标题"
                                                                   message:@"请输入IP"
                                                            preferredStyle:UIAlertControllerStyleAlert];
    
    // 2.1添加输入文本框
    [alert addTextFieldWithConfigurationHandler:^(UITextField * _Nonnull textField) {
        textField.placeholder = @"请输入IP";
        NSString *serverIp = [RRTools strForKey:@"SVERENTRY"];
        textField.text = serverIp;
    }];
    
    // 2.2实例化UIAlertAction按钮:确定按钮
    UIAlertAction *confirmAction = [UIAlertAction actionWithTitle:@"确定"
                                                            style:UIAlertActionStyleDefault
                                                          handler:^(UIAlertAction * _Nonnull action) {
                                                              UITextField *ipTextField = alert.textFields.firstObject;
                                                              NSString *ip = ipTextField.text;
                                                              NSLog(@"读取输入ip：%@",ip);
                                                              [RRTools setStr:ip key:@"SVERENTRY"];
                                                              okBlock();
                                                          }];
    [alert addAction:confirmAction];
    
    //  3.显示alertController
    [controller presentViewController:alert animated:YES completion:nil];
}

#pragma mark - 加密相关
+(NSString *)changeBase64:(NSString *)orginString
{
    NSMutableArray * array = [NSMutableArray arrayWithArray:[orginString componentsSeparatedByString:@"<"]];
    //    [array removeObject:@""];
    NSMutableString * string = [NSMutableString stringWithString:@""];
    for (int i = 0; i< array.count; i++) {
        [string appendString:array[i]];
        if (i==array.count-1) {
            
        }else{
            [string appendString:@"+"];
        }
    }
    return string;
}


//获取当前时间戳（秒级别）
+(NSString *)getCurrentTimeStringToSecond
{
    NSString *timeStamp = [NSString stringWithFormat:@"%ld",(long)([[NSDate date] timeIntervalSince1970])];
    return timeStamp;
}
//获取当前时间戳（毫秒级别）
+(NSString *)getCurrentTimeStringToMilliSecond
{
    NSString *timeStamp = [NSString stringWithFormat:@"%ld",(long)([[NSDate date] timeIntervalSince1970]*1000)];
    return timeStamp;
}
//获取随机字符串,30位
//+(NSString *)getRandomStr
//{
//    NSString *udid = [RRTools getDeviceIDInKeychain];
//    udid = [udid stringByReplacingOccurrencesOfString:@"-" withString:@""];
//    NSString *timeStamp = [RRTools getCurrentTimeStringToMilliSecond];
//    timeStamp = [timeStamp substringFromIndex:timeStamp.length - 8];
//    NSString *randomStr = [NSString stringWithFormat:@"%@%@",udid,timeStamp];
//    return randomStr;
//}
//获取随机字符串-数字+大小写字母
+(NSString *)getRandomStr
{
    //先初始化一个空数组，存放字符；
    NSMutableArray *cArr = [NSMutableArray array];
    //再初始化一个字符串，包含数字，大写字母，小写字母
    NSString *hexDigits = @"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    //从hexDigits中随机取字符形成随机字符串
    for(int i = 0; i < 26; i++){
        int random = arc4random_uniform(62);
        cArr[i] = [hexDigits substringWithRange:NSMakeRange(random, 1)];
    }
    NSString *cStr = [cArr componentsJoinedByString:@""];
    NSLog(@"%@",cStr);
    
    return cStr;
}
//sha256加密
+(NSString *)sha256WithInputStr:(NSString *)inputStr
{
    return [SecurityUtility sha256HashFor:inputStr];
}

//MD5摘要
+(NSMutableString *)MD5WithInputStr:(NSString *)inputStr
{
    return [SecurityUtility stringMD5:inputStr];
}

//uuidGet() {
//    let s = [];
//    let hexDigits =
//    "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
//    for (let i = 0; i < 26; i++) {
//        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x26), 1);
//    }
//    s[14] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
//    s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
//
//    let timeStamp = new Date().getTime().toString().substr(-7);
//    s[8] = timeStamp;
//    let uuid = s.join("");
//    this.uuid = uuid;
//},
//getIns() {
//    let params = {
//    userName: "LunaLovegood",
//    password: "Luna1040"
//    };
//    //声明MD5
//    let md5 = crypto.createHash("md5");
//    //声明sha256
//    const sha256 = require("js-sha256").sha256; //这里用的是require方法，所以没用import
//    // let s = '11111'
//    // s = sha256(s)//要加密的密码
//    // md5.update(s); //this.pw2这是你要加密的密码
//    // s = md5.digest("hex"); //this.pw这就是你加密完的密码，这个往后台传就行了
//
//    // let s =
//    let baseData = JSON.stringify(params);
//    let s = md5.update(sha256(baseData));
//    s =
//    baseData +
//    md5
//    .digest("hex")
//    .toString(16)
//    .toUpperCase();
//    // crypto转16位        md5加密  256加密
//    // console.log(s);
//}

#pragma mark - UI相关
//按钮可用
+(void)btnEnable:(UIButton *)btn withHighColor:(UIColor *)highColor
{
    [RRTools getMainQueueDo:^{
        btn.userInteractionEnabled = YES;
        btn.backgroundColor = highColor;
    }];
    
}
//按钮不可用
+(void)btnUnable:(UIButton *)btn withDarkColor:(UIColor *)darkColor
{
    [RRTools getMainQueueDo:^{
        btn.userInteractionEnabled = NO;
        btn.backgroundColor = darkColor;
    }];
}
//style 表示渐变模式   1：上下渐变   2：左下到右上渐变  3:左上到右下渐变
+(void)getGradientColor:(UIView *)theView withCGColor1:(CGColorRef)color1 withCGColor2:(CGColorRef)color2 style:(NSString *)style
{
    CAGradientLayer *gradientLayer = [CAGradientLayer layer];
    gradientLayer.frame = theView.bounds;
    
    //将CAGradientlayer对象添加在我们要设置背景色的视图的layer层
//        [theView.layer addSublayer:gradientLayer];
    [theView.layer insertSublayer:gradientLayer atIndex:0];
    
    //设置渐变区域的起始和终止位置（范围为0-1）
    if([style isEqualToString:@"1"]){//上下渐变
        gradientLayer.startPoint = CGPointMake(0, 0);
        gradientLayer.endPoint = CGPointMake(0, 1);
    }else if ([style isEqualToString:@"2"]){//左下到右上渐变
        gradientLayer.startPoint = CGPointMake(0, 1);
        gradientLayer.endPoint = CGPointMake(1, 0);
    }else if ([style isEqualToString:@"3"]){//左上到右下渐变
        gradientLayer.startPoint = CGPointMake(0, 0);
        gradientLayer.endPoint = CGPointMake(1, 1);
    }else if ([style isEqualToString:@"4"]){//左右渐变
        gradientLayer.startPoint = CGPointMake(0, 0);
        gradientLayer.endPoint = CGPointMake(1, 0);
    }
    
    //设置颜色数组
    gradientLayer.colors = @[(__bridge id)color1,
                             (__bridge id)color2];
    
    //设置颜色分割点（范围：0-1）
    gradientLayer.locations = @[@(0.0f), @(1.0f)];
}


#pragma mark - 报文判断
/*
 根据报文内容判断通讯交易是否成功
 */
+(BOOL)isSuccessWithAlert:(RRNetworkModel *)model
{
    return [RRTools isSuccess:model withSuccessBlock:nil cancelBlock:nil errorBlock:^(NSString *errmsg) {
        [RRTools getMainQueueDo:^{
            [RRTools showAlertWithTitle:TRANSLATE(@"common_alert_prompt") message:errmsg okTtitle:TRANSLATE(@"common_alert_ok") okBlock:^{
                
            } cancelTitle:@"" cancelBlock:^{
                
            }];
        }];
    }];
}
+(BOOL)isSuccess:(RRNetworkModel *)model
{
    return [RRTools isSuccess:model withSuccessBlock:nil cancelBlock:nil errorBlock:^(NSString *errmsg) {
        if (![model.status isEqualToString:@"RRB-05009001"] && ![model.status isEqualToString:@"RRP-60000020"]) {
            [RRTools showTips:errmsg];
        }
    }];
}
+(BOOL)isSuccess:(RRNetworkModel *)model withSuccessBlock:(nullable void(^)(void))successBlock cancelBlock:(nullable void(^)(void))cancekBlock errorBlock:(nullable void(^)(NSString *errmsg))errorBlock
{
    return [RRTools isSuccess:model withSuccessBlock:successBlock cancelBlock:cancekBlock errorBlock:^(NSString *errmsg) {
        if (![model.status isEqualToString:@"RRB-05009001"] && ![model.status isEqualToString:@"RRP-60000020"]) {
            [RRTools showTips:errmsg];
        }
    } firstBlock:nil];
}
+(BOOL)isSuccess:(RRNetworkModel *)model withSuccessBlock:(nullable void(^)(void))successBlock cancelBlock:(nullable void(^)(void))cancekBlock errorBlock:(nullable void(^)(NSString *errmsg))errorBlock firstBlock:(nullable void(^)(void))firstBlock
{
    NSString * status = [NSString stringWithFormat:@"%@",model.status];
    if ([status isEqualToString:@"0"]){
        return YES;
    }else{
        if (firstBlock) {
            firstBlock();
        }
        NSString *msg = model.message;
        if (msg.length > 0) {
            if([@"-5" isEqualToString:status]){
                [RRTools getMainQueueDo:^{
                    GETTHEAPP
                    theApp.hasLogin = NO;
                    [RRTools setBool:YES key:NEEDVERIFY];
//                    [RRTools showAlertWithTitle:TRANSLATE(@"Your session has timed out. Please login again.") message:@"" okTtitle:TRANSLATE(@"OK") okBlock:^{
                        //开启指纹则验证指纹锁,未开启验证登录密码页面
                        if([RRTools boolForKey:[RRTools getBiometricsToken]]){
                            BOOL hasVC = NO;
                            for(UIViewController*vc in [RRTools getCurrentViewController].navigationController.viewControllers) {
                                if([vc isKindOfClass:[RRLoginUnlockVC class]]) {
                                    hasVC = YES;
                                }
                            }
                            if (hasVC == NO) {
                                [RRTools presentLoginLockVCwithSuccessBlock:^{
                                    [RRTools getMainQueueDo:^{
                                        //验证成功回调,切回到首页，清空记录的进入后台时间
                                        [RRTools removeDateForKey:@"backgroundTime"];
                                        //设置需要验证登录锁
                                        [RRTools setBool:NO key:NEEDVERIFY];
                                        GETTHEAPP
                                        theApp.hasLogin = YES;
                                        //校验通过，需要再次记录进入后台时间
                                        theApp.noNeedRecord = NO;
                                        if(successBlock){
                                            successBlock();
                                        }else{
                                            [[RRTools getCurrentViewController].navigationController popToRootViewControllerAnimated:YES];
                                            [RRTools getCurrentViewController].tabBarController.selectedIndex = 0;
                                        }
                                    }];
                                } cancelBlock:^{
                                    //校验通过，需要再次记录进入后台时间
                                    GETTHEAPP
                                    theApp.noNeedRecord = YES;
                                    if(cancekBlock){
                                        cancekBlock();
                                    }
                                }];
                            }
                        }else{
                            BOOL hasVC = NO;
                            for(UIViewController*vc in [RRTools getCurrentViewController].navigationController.viewControllers) {
                                if([vc isKindOfClass:[RRLoginPwdUnlockVC class]]) {
                                    hasVC = YES;
                                }
                            }
                            if (hasVC == NO) {
                                [RRTools presentLoginPwdVCwithSuccessBlock:^{
                                    [RRTools getMainQueueDo:^{
                                        //验证成功回调，切回到首页，清空记录的进入后台时间
                                        [RRTools removeDateForKey:@"backgroundTime"];
                                        ///设置需要验证登录锁
                                        [RRTools setBool:NO key:NEEDVERIFY];
                                        GETTHEAPP
                                        theApp.hasLogin = YES;
                                        //校验通过，需要再次记录进入后台时间
                                        theApp.noNeedRecord = NO;
                                        if(successBlock){
                                            successBlock();
                                        }else{
                                            //跳转主页面
                                            [[RRTools getCurrentViewController].navigationController popToRootViewControllerAnimated:YES];
                                            [RRTools getCurrentViewController].tabBarController.selectedIndex = 0;
                                        }
                                    }];
                                } cancelBlock:^{
                                    //校验通过，需要再次记录进入后台时间
                                    GETTHEAPP
                                    theApp.noNeedRecord = YES;
                                    if(cancekBlock){
                                        cancekBlock();
                                    }
                                }];
                            }
                        }
                        
//                    } cancelTitle:@"" cancelBlock:nil];
                }];
            }else{
                if(errorBlock){
                    errorBlock(msg);
                }
            }
        }
        return NO;
    }
    return NO;
}


#pragma mark - 前端校验规则
+ (BOOL)validateFullName: (NSString *)fullName
{
//    NSString *regex = @"^[a-zA-Z/@\\-., '`]+$";
    NSString *regex = @"^[a-zA-Z]{1,50}$";
    NSPredicate *test = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", regex];
    return [test evaluateWithObject:fullName];
}
+ (BOOL)validateIdCard: (NSString *)nric
{
    NSString *regex = @"^(?:(?:\\d{9}[a-hj-np-tv-zA-HJ-NP-TV-Z]\\d{2})|(?:\\d{8}[a-hj-np-tv-zA-HJ-NP-TV-Z]\\d{2}))$";
    NSPredicate *test = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",regex];
    return [test evaluateWithObject:nric];
}

+ (BOOL)validateJuniorID: (NSString *)juniorID
{
    NSString *regex = @"^[0-9a-zA-Z]{1,20}$";
    NSPredicate *test = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",regex];
    return [test evaluateWithObject:juniorID];
}
+ (BOOL)validatePassport: (NSString *)passport
{
    NSString *regex = @"^[0-9a-zA-Z]{1,50}$";
    NSPredicate *test = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",regex];
    return [test evaluateWithObject:passport];
}
+ (BOOL)validateMobileNo: (NSString *)mobileNo
{
    NSString *regex = @"^7\\d{8}$";
    NSPredicate *test = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",regex];
    return [test evaluateWithObject:mobileNo];
}
+ (BOOL)validateEmail: (NSString *)email
{
    NSString *regex = @"^[A-Za-z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$";
    NSPredicate *test = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", regex];
    return [test evaluateWithObject:email];
}
+ (BOOL)validateUserName: (NSString *)userName
{
    NSString *regex = @"^[a-zA-Z]{1,50}$";
    NSPredicate *test = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", regex];
    return [test evaluateWithObject:userName];
}
+ (BOOL)validatePwd: (NSString *)pwd
{
    //    NSString *regex = @"^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[.~!@#$%^&*()_\\-+={}<>])[a-zA-Z0-9.~!@#$%^&*()_\\-+={}<>]{8,20}$";
    //    NSPredicate *test = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", regex];
    //    return [test evaluateWithObject:pwd];

    //字母相同的的个数
    int lenSame = 0;
    //递减次数
    int subLen = 0;
    //递增次数
    int addLen = 0;
    for (NSUInteger i = 0; i < pwd.length - 1; i++) {
        unichar char1 = [pwd characterAtIndex:i];
        unichar char2 = [pwd characterAtIndex:i + 1];
        if (![[NSCharacterSet decimalDigitCharacterSet] characterIsMember:char1]
            && ![[NSCharacterSet decimalDigitCharacterSet] characterIsMember:char2]) {
            return NO;
        }
        if(char1 == char2){
            lenSame += 1;
        }
        if((char1-char2) == 1){
            addLen += 1;
        }
        if((char1-char2) == -1){
            subLen += 1;
        }
    }
    if(lenSame==3||addLen==3||subLen==3) {
        return NO;
    }
    return YES;
}
+ (BOOL)validateAddress: (NSString *)address
{
//    a-zA-z0-9-#.,;:\-'&/ ()
    NSString *regex = @"^[a-zA-Z0-9#.,;:\\-'&/ ()]+$";
    address = [RRTools convertFullAngleToHalfAngleWithString:address];
    NSPredicate *test = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", regex];
    return [test evaluateWithObject:address];
}
+ (BOOL)validatePostCode: (NSString *)postCode
{
    NSString *regex = @"^[\\d]{5,10}$";
    NSPredicate *test = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",regex];
    return [test evaluateWithObject:postCode];
}
+ (BOOL)validateBankCardNo:(NSString *)cardNo
{
    NSString *regex = @"^[0-9]{6,30}";
    NSPredicate *test = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", regex];
    return [test evaluateWithObject:cardNo];
}
//全角转半角
+ (NSString *)convertFullAngleToHalfAngleWithString:(NSString *)string{
    NSMutableString *convertedString = [string mutableCopy];
    CFStringTransform((CFMutableStringRef)convertedString, NULL, kCFStringTransformFullwidthHalfwidth, false);
    return [NSString stringWithString:convertedString];
}
//半角转全角
+ (NSString *)convertHalfAngleToFullAngleWithString:(NSString *)string{
    NSMutableString *convertedString = [string mutableCopy];
    CFStringTransform((CFMutableStringRef)convertedString, NULL, kCFStringTransformHiraganaKatakana, false);
    return [NSString stringWithString:convertedString];
}

#pragma mark - 读取文件数据
//存储数组到指定的plist文件中
+ (void)savePhotoCodeArr:(NSArray *)phoneCodeArr withFileName:(NSString *)fileName {
    NSString *documentPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    NSString *arrPath = [documentPath stringByAppendingPathComponent:fileName];
    NSFileManager* fm = [NSFileManager defaultManager];
    if (![fm fileExistsAtPath:arrPath]) {
       BOOL rrr = [fm createFileAtPath:arrPath contents:nil attributes:nil];
        if (rrr) {
            NSLog(@"创建文件成功");
        } else {
            NSLog(@"创建文件失败");
        }
    }else{
        //删除原先已存在的文件
        [fm removeItemAtPath:arrPath error:nil];
    }
    // 序列化，把字典存入plist文件
    BOOL ret = [phoneCodeArr writeToFile:arrPath atomically:YES];
    if (!ret) {
        NSLog(@"写入本地失败");
    }else{
        NSLog(@"写入本地成功");
    }
}
//根据文件名称从plist文件中读取数据
+ (NSArray *)getPhoneCodeArrWithFileName:(NSString *)fileName {
    NSString *documentPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    NSString *arrPath = [documentPath stringByAppendingPathComponent:fileName];
    // 反序列化，把plist文件数据读取出来，转为字典
    NSArray *phoneArr = [NSArray arrayWithContentsOfFile:arrPath];
    return phoneArr;
}
//读取区号数据到安装路径中
+(void)copyPhoneCodeToDocument
{
    NSFileManager * FM = [NSFileManager defaultManager];
    NSString *documentPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    NSString *filePath_en = [[NSBundle mainBundle]pathForResource:@"phoneCode_en" ofType:@"plist"];
    NSString *targetPath_en = [documentPath stringByAppendingPathComponent:@"phoneCode_en.plist"];
    if ([FM fileExistsAtPath:targetPath_en]) {
        [FM removeItemAtPath:targetPath_en error:nil];
        [FM copyItemAtPath:filePath_en toPath:targetPath_en error:nil];
    }else{
        [FM copyItemAtPath:filePath_en toPath:targetPath_en error:nil];
    }
    NSString *filePath_zh = [[NSBundle mainBundle]pathForResource:@"phoneCode_zh" ofType:@"plist"];
    NSString *targetPath_zh = [documentPath stringByAppendingPathComponent:@"phoneCode_zh.plist"];
    if ([FM fileExistsAtPath:targetPath_zh]) {
        [FM removeItemAtPath:targetPath_zh error:nil];
        [FM copyItemAtPath:filePath_zh toPath:targetPath_zh error:nil];
    }else{
        [FM copyItemAtPath:filePath_zh toPath:targetPath_zh error:nil];
    }
//    if ([FM fileExistsAtPath:filePath]) {
//        NSError * error;
//        NSString * str = [NSString stringWithContentsOfFile:filePath encoding:NSUTF8StringEncoding error:&error];
//        NSArray * array = [str componentsSeparatedByString:@"|"];
//        NSMutableArray *phonePickerDataArr = [NSMutableArray array];
//        for(int i = 0; i < array.count; i++){
//            NSString *str = [NSString stringWithFormat:@"%@",array[i]];
//            str = [str stringByReplacingOccurrencesOfString:@"\n" withString:@""];
//            str = [str stringByReplacingOccurrencesOfString:@"{" withString:@""];
//            str = [str stringByReplacingOccurrencesOfString:@"}" withString:@""];
//            str = [str stringByReplacingOccurrencesOfString:@" " withString:@""];
//            NSArray * array1 = [str componentsSeparatedByString:@";"];
//            NSString *areaCode;
//            NSString *nationName;
//            for (int i = 0; i < array1.count; i++) {
//                NSArray *array2 = [array1[i] componentsSeparatedByString:@"="];
//                if ([array2[0] isEqualToString:@"areaCode"]) {
//                    areaCode = array2[1];
//                }
//                if ([array2[0] isEqualToString:@"nationName"]) {
//                    nationName = array2[1];
//                }
//            }
//            NSString *nationStr = [NSString stringWithFormat:@"%@%@ %@",@"+",areaCode,nationName];
//            [phonePickerDataArr addObject:nationStr];
//        }
//        [RRTools setArray:phonePickerDataArr key:PHONECODE];
//    }
}
//读取区号数据到本地存储
+ (void)readPhoneCodeToUserDefault
{
    NSArray *phoneCodeArr_en = [RRTools getPhoneCodeArrWithFileName:@"phoneCode_en.plist"];
    NSArray *phoneCodeArr_zh = [RRTools getPhoneCodeArrWithFileName:@"phoneCode_zh.plist"];
    NSMutableArray *phoneCodeArr;
    phoneCodeArr = [NSMutableArray array];
    for(int i = 0; i < phoneCodeArr_en.count; i++){
        NSDictionary *nationDic = (NSDictionary *)phoneCodeArr_en[i];
        NSString *areaCode = nationDic[@"areaCode"];
        NSString *nationName = nationDic[@"nationName"];
        NSString *nationStr = [NSString stringWithFormat:@"%@%@ %@",@"+",areaCode,nationName];
        [phoneCodeArr addObject:nationStr];
    }
    [RRTools setArray:phoneCodeArr key:PHONECODE_EN];
    
    [phoneCodeArr removeAllObjects];
    for(int i = 0; i < phoneCodeArr_zh.count; i++){
        NSDictionary *nationDic = (NSDictionary *)phoneCodeArr_zh[i];
        NSString *areaCode = nationDic[@"areaCode"];
        NSString *nationName = nationDic[@"nationName"];
        NSString *nationStr = [NSString stringWithFormat:@"%@%@ %@",@"+",areaCode,nationName];
        [phoneCodeArr addObject:nationStr];
    }
    [RRTools setArray:phoneCodeArr key:PHONECODE_ZH];
}
//获取联系人列表的手机号拼接md5值
+ (NSString *)md5OfContactArr:(NSArray *)contactList
{
    //联系人列表数据排序后做md5摘要
    NSMutableArray *mobileArr = [NSMutableArray array];
    for (RRContact *contact in contactList) {
        [mobileArr addObject:[contact.mobile stringByReplacingOccurrencesOfString:@"+" withString:@""]];
    }
    [mobileArr sortUsingComparator:^NSComparisonResult(id  _Nonnull obj1, id  _Nonnull obj2) {
        NSString *str1=(NSString *)obj1;
        NSString *str2=(NSString *)obj2;
        return [str1 compare:str2];
    }];
    NSString *originStr = [mobileArr componentsJoinedByString:@""];
    NSString *md5Str = [RRTools MD5WithInputStr:originStr];
    return md5Str;
}

//获取exception文件存储的路径
+ (NSString *)getExceptionFilePath
{
    NSString *docPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    NSString *filePath = [docPath stringByAppendingPathComponent:@"XNExceptionHandler.txt"];
    return filePath;
}

@end
