//url 管理配置
class UrlConfig {
  ///工单子类型
  static const String workOrderSubTypeQuery = "/api/merchant/workSubOrder/querySub";
  ///工单创建
  static const String workOrderCreate = "/api/merchant/workOrderList/create";
  ///工单记录查询
  static const String workOrderRecordsQuery = "/api/merchant/workOrderList/queryByPage";
  ///工单详情查询
  static const String workOrderRecordDetailsQuery = "/api/merchant/workOrderList/detailQuery";
  ///满意度反馈
  static const String workOrderSatisLevelFeedback = "/api/merchant/satisFeedback/add";
  ///上传图片
  static const String workOrderUploadImage = "/api/oss/uploadImage";
  ///获取国家列表
  static const String requestAllNationQuery =
      "/api/merchant/data/allNationQuery";
  ///协议须知
  static const String requestTransactionComplaintNotice = "/api/merchant/workOrderList/getComplaintNotic";
}

class RequestParamConfig {
  //工单类型
  static const String typeAttribution = "typeAttribution";

  //平台类型
  static const String visibleConfig = "visibleConfig";

  //是否展示已删除的类型
  static const String isViewDeleted = "isViewDeleted";

  //工单id
  static const String workOrderId = "workOrderId";

  //工单满意度
  static const String satisLevel = "satisLevel";

  //工单类型
  static const String workOrderType = "workOrderType";

  //工单子类型
  static const String workOrderSubType = "workOrderSubType";

  //问题描述
  static const String description = "description";

  //凭证列表
  static const String voucherList = "voucherList";

  //联系电话区号
  static const String phoneAreaCode = "phoneAreaCode";

  //联系电话号码
  static const String customerPhone = "customerPhone";

  //交易类型
  static const String transType = "transType";

  //订单编号
  static const String orderNo = "orderNo";

  //提交人类型
  static const String submitterType = "submitterType";

  //提交人id
  static const String submitterId = "submitterId";

  //提交人名称
  static const String submitterName = "submitterName";

  //提交人id或姓名
  static const String submitterIdOrName = "submitterIdOrName";

  //提交人类型
  static const String submitterTypeList = "submitterTypeList";

  //工单状态
  static const String statusList = "statusList";

  //工单id
  static const String noticeId = "id";
}

class SaveParamConfig {
  ///校验token
  static const String securityToken = "securityToken";

  ///登录token
  static const String authorization = "authorization";

  ///代理商token
  static const String token = "token";
}