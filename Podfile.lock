PODS:
  - CRBoxInputView (1.1.7):
    - Masonry
  - CWLateralSlide (1.6.5)
  - device_info (0.0.1):
    - Flutter
  - FirebaseCore (8.9.0):
    - FirebaseCoreDiagnostics (~> 8.0)
    - GoogleUtilities/Environment (~> 7.6)
    - GoogleUtilities/Logger (~> 7.6)
  - FirebaseCoreDiagnostics (8.15.0):
    - GoogleDataTransport (~> 9.1)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
    - nanopb (~> 2.30908.0)
  - FirebaseInstallations (8.15.0):
    - FirebaseCore (~> 8.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/UserDefaults (~> 7.7)
    - PromisesObjC (< 3.0, >= 1.2)
  - FirebaseMessaging (8.9.0):
    - FirebaseCore (~> 8.0)
    - FirebaseInstallations (~> 8.0)
    - GoogleDataTransport (~> 9.1)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.6)
    - GoogleUtilities/Environment (~> 7.6)
    - GoogleUtilities/Reachability (~> 7.6)
    - GoogleUtilities/UserDefaults (~> 7.6)
    - nanopb (~> 2.30908.0)
  - Flutter (1.0.0)
  - flutter_secure_storage (3.3.1):
    - Flutter
  - FlutterPluginRegistrant (0.0.1):
    - device_info
    - Flutter
    - flutter_secure_storage
    - fluttertoast
    - image_picker_ios
    - package_info
    - rrworkmanager
    - shared_preferences_foundation
    - video_player_avfoundation
    - wakelock
    - webview_flutter_wkwebview
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - GMObjC (3.3.7):
    - GMOpenSSL
  - GMOpenSSL (3.0.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (6.2.1):
    - GoogleMaps/Maps (= 6.2.1)
  - GoogleMaps/Base (6.2.1)
  - GoogleMaps/Maps (6.2.1):
    - GoogleMaps/Base
  - GooglePlaces (6.2.1)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - image_picker_ios (0.0.1):
    - Flutter
  - IQKeyboardManager (6.5.19)
  - LBXScan/LBXNative (2.5.1):
    - LBXScan/Types (~> 2.2)
  - LBXScan/Types (2.5.1)
  - LBXScan/UI (2.5.1):
    - LBXScan/Types (~> 2.2)
  - lottie-ios (2.5.3)
  - Masonry (1.1.0)
  - MBProgressHUD (1.2.0)
  - MJExtension (3.2.0)
  - MJRefresh (3.7.9)
  - nanopb (2.30908.0):
    - nanopb/decode (= 2.30908.0)
    - nanopb/encode (= 2.30908.0)
  - nanopb/decode (2.30908.0)
  - nanopb/encode (2.30908.0)
  - package_info (0.0.1):
    - Flutter
  - PromisesObjC (2.4.0)
  - rrworkmanager (0.0.1):
    - Flutter
    - GMObjC
  - RRXNNetwork (1.0.5):
    - MJExtension (= 3.2.0)
  - SDCycleScrollView (1.82):
    - SDWebImage (>= 5.0.0)
  - SDWebImage (5.19.7):
    - SDWebImage/Core (= 5.19.7)
  - SDWebImage/Core (5.19.7)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - Toast (4.1.1)
  - video_player_avfoundation (0.0.1):
    - Flutter
  - wakelock (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
  - xwalletpro_flutter (0.0.1):
    - Flutter

DEPENDENCIES:
  - CRBoxInputView (~> 1.1.7)
  - CWLateralSlide (~> 1.6.5)
  - device_info (from `../../xwalletpro_flutter/.ios/.symlinks/plugins/device_info/ios`)
  - FirebaseCore (= 8.9.0)
  - FirebaseMessaging (= 8.9.0)
  - Flutter (from `../../xwalletpro_flutter/.ios/Flutter/engine`)
  - flutter_secure_storage (from `../../xwalletpro_flutter/.ios/.symlinks/plugins/flutter_secure_storage/ios`)
  - FlutterPluginRegistrant (from `../../xwalletpro_flutter/.ios/Flutter/FlutterPluginRegistrant`)
  - fluttertoast (from `../../xwalletpro_flutter/.ios/.symlinks/plugins/fluttertoast/ios`)
  - GMObjC
  - GMOpenSSL (= 3.0.0)
  - GoogleMaps
  - GooglePlaces
  - image_picker_ios (from `../../xwalletpro_flutter/.ios/.symlinks/plugins/image_picker_ios/ios`)
  - IQKeyboardManager (~> 6.5.4)
  - LBXScan/LBXNative
  - LBXScan/UI
  - lottie-ios (= 2.5.3)
  - Masonry
  - MBProgressHUD
  - MJExtension (= 3.2.0)
  - MJRefresh
  - package_info (from `../../xwalletpro_flutter/.ios/.symlinks/plugins/package_info/ios`)
  - rrworkmanager (from `../../xwalletpro_flutter/.ios/.symlinks/plugins/rrworkmanager/ios`)
  - RRXNNetwork (= 1.0.5)
  - SDCycleScrollView (>= 1.80)
  - SDWebImage
  - shared_preferences_foundation (from `../../xwalletpro_flutter/.ios/.symlinks/plugins/shared_preferences_foundation/ios`)
  - video_player_avfoundation (from `../../xwalletpro_flutter/.ios/.symlinks/plugins/video_player_avfoundation/ios`)
  - wakelock (from `../../xwalletpro_flutter/.ios/.symlinks/plugins/wakelock/ios`)
  - webview_flutter_wkwebview (from `../../xwalletpro_flutter/.ios/.symlinks/plugins/webview_flutter_wkwebview/ios`)
  - xwalletpro_flutter (from `../../xwalletpro_flutter/.ios/Flutter`)

SPEC REPOS:
  https://github.com/CocoaPods/Specs.git:
    - CRBoxInputView
    - CWLateralSlide
    - FirebaseCore
    - FirebaseCoreDiagnostics
    - FirebaseInstallations
    - FirebaseMessaging
    - GMObjC
    - GMOpenSSL
    - GoogleDataTransport
    - GoogleMaps
    - GooglePlaces
    - GoogleUtilities
    - IQKeyboardManager
    - LBXScan
    - lottie-ios
    - Masonry
    - MBProgressHUD
    - MJExtension
    - MJRefresh
    - nanopb
    - PromisesObjC
    - SDCycleScrollView
    - SDWebImage
    - Toast
  https://gitlab.vimbug.com/Shirley/XNSpecs.git:
    - RRXNNetwork

EXTERNAL SOURCES:
  device_info:
    :path: "../../xwalletpro_flutter/.ios/.symlinks/plugins/device_info/ios"
  Flutter:
    :path: "../../xwalletpro_flutter/.ios/Flutter/engine"
  flutter_secure_storage:
    :path: "../../xwalletpro_flutter/.ios/.symlinks/plugins/flutter_secure_storage/ios"
  FlutterPluginRegistrant:
    :path: "../../xwalletpro_flutter/.ios/Flutter/FlutterPluginRegistrant"
  fluttertoast:
    :path: "../../xwalletpro_flutter/.ios/.symlinks/plugins/fluttertoast/ios"
  image_picker_ios:
    :path: "../../xwalletpro_flutter/.ios/.symlinks/plugins/image_picker_ios/ios"
  package_info:
    :path: "../../xwalletpro_flutter/.ios/.symlinks/plugins/package_info/ios"
  rrworkmanager:
    :path: "../../xwalletpro_flutter/.ios/.symlinks/plugins/rrworkmanager/ios"
  shared_preferences_foundation:
    :path: "../../xwalletpro_flutter/.ios/.symlinks/plugins/shared_preferences_foundation/ios"
  video_player_avfoundation:
    :path: "../../xwalletpro_flutter/.ios/.symlinks/plugins/video_player_avfoundation/ios"
  wakelock:
    :path: "../../xwalletpro_flutter/.ios/.symlinks/plugins/wakelock/ios"
  webview_flutter_wkwebview:
    :path: "../../xwalletpro_flutter/.ios/.symlinks/plugins/webview_flutter_wkwebview/ios"
  xwalletpro_flutter:
    :path: "../../xwalletpro_flutter/.ios/Flutter"

SPEC CHECKSUMS:
  CRBoxInputView: 72b35e2f3df36739a444eadefc9ef0dbf1fe576e
  CWLateralSlide: 8939c8dea91a24e40d0793553118cb57cd67f64b
  device_info: d7d233b645a32c40dfdc212de5cf646ca482f175
  FirebaseCore: 599ee609343eaf4941bd188f85e3aa077ffe325b
  FirebaseCoreDiagnostics: 92e07a649aeb66352b319d43bdd2ee3942af84cb
  FirebaseInstallations: 40bd9054049b2eae9a2c38ef1c3dd213df3605cd
  FirebaseMessaging: 82c4a48638f53f7b184f3cc9f6cd2cbe533ab316
  Flutter: bdfa2e8fe0e2880a2c6a58a0b1a8675c262a07af
  flutter_secure_storage: 7953c38a04c3fdbb00571bcd87d8e3b5ceb9daec
  FlutterPluginRegistrant: d70511628df23104629007c1cd389d8397dd28db
  fluttertoast: 6122fa75143e992b1d3470f61000f591a798cc58
  GMObjC: d47a3c0f87a3fa22dbb6ccd929e0cc31c29c894a
  GMOpenSSL: 753ecadaa28028dcce9e0ca250f126a33696ad58
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMaps: 20d7b12be49a14287f797e88e0e31bc4156aaeb4
  GooglePlaces: 94974aa119573d5acc2a35a699948dac838abd73
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  image_picker_ios: b786a5dcf033a8336a657191401bfdf12017dabb
  IQKeyboardManager: c8665b3396bd0b79402b4c573eac345a31c7d485
  LBXScan: 90ca10d0c38fb4a5a6980d7782354f3f69f50093
  lottie-ios: a50d5c0160425cd4b01b852bb9578963e6d92d31
  Masonry: 678fab65091a9290e40e2832a55e7ab731aad201
  MBProgressHUD: 3ee5efcc380f6a79a7cc9b363dd669c5e1ae7406
  MJExtension: e22bed65fdcd38e957242982b7384f2984c9088e
  MJRefresh: ff9e531227924c84ce459338414550a05d2aea78
  nanopb: a0ba3315591a9ae0a16a309ee504766e90db0c96
  package_info: 873975fc26034f0b863a300ad47e7f1ac6c7ec62
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  rrworkmanager: 2c75aa79154f0f4171af4c7eed4d6cbfed5a4831
  RRXNNetwork: 3a2dbbcd308c26002868d497a710f6881ccd0568
  SDCycleScrollView: a0d74c3384caa72bdfc81470bdbc8c14b3e1fbcf
  SDWebImage: 8a6b7b160b4d710e2a22b6900e25301075c34cb3
  shared_preferences_foundation: 297b3ebca31b34ec92be11acd7fb0ba932c822ca
  Toast: ****************************************
  video_player_avfoundation: e489aac24ef5cf7af82702979ed16f2a5ef84cff
  wakelock: d0fc7c864128eac40eba1617cb5264d9c940b46f
  webview_flutter_wkwebview: b7e70ef1ddded7e69c796c7390ee74180182971f
  xwalletpro_flutter: e756b991869b7b6340bbb156f84ecafd44d50cd5

PODFILE CHECKSUM: 74116033d1c8cb9da65f53645d5f873a0b7d13e0

COCOAPODS: 1.16.2
