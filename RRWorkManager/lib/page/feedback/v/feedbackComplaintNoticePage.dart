import 'package:flutter/material.dart';
import 'package:html_unescape/html_unescape.dart';
import 'package:rrworkmanager/common_widget/AppBarWidget.dart';
import 'package:rrworkmanager/utils/Global.dart';

class FeedbackComplaintNoticePage extends StatelessWidget {
  final String? conditionTitle;

  final String? conditionContent;

  const FeedbackComplaintNoticePage(
      {Key? key, this.conditionTitle, this.conditionContent})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final unescape = HtmlUnescape();
    final cleanedContent = unescape.convert(conditionContent ?? '');

    return Scaffold(
        appBar: AppBarUtils(title: conditionTitle ?? "").build(context),
        body: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.all(16.0),
            child: Text(
              cleanedContent,
              style: TextStyle(fontSize: 14, height: 1.4),
            ),
          ),
        ),
        backgroundColor: Global.lightBackGroundColor);
  }
}
