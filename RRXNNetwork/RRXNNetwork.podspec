#
# Be sure to run `pod lib lint RRXNNetwork.podspec' to ensure this is a
# valid spec before submitting.
#
# Any lines starting with a # are optional, but their use is encouraged
# To learn more about a Podspec see https://guides.cocoapods.org/syntax/podspec.html
#

Pod::Spec.new do |s|
  s.name             = 'RRXNNetwork'
  s.version          = '1.1.0'
  s.summary          = 'RRXNNetwork为xWallet网络框架'

# This description is used to generate tags and improve search results.
#   * Think: What does it do? Why did you write it? What is the focus?
#   * Try to keep it short, snappy and to the point.
#   * Write the description between the DESC delimiters below.
#   * Finally, don't worry about the indent, CocoaPods strips it!

  s.description      = <<-DESC
TODO: Add long description of the pod here.
                       DESC

  s.homepage         = 'http://**************:8989/mfs/component/RRXNNetwork'
  # s.screenshots     = 'www.example.com/screenshots_1', 'www.example.com/screenshots_2'
  s.license          = { :type => 'MIT', :file => 'LICENSE' }
  s.author           = { 'Shirley' => '<EMAIL>' }
  s.source           = { :git => 'http://**************:8989/mfs/component/RRXNNetwork.git', :tag => s.version.to_s }
  # s.social_media_url = 'https://twitter.com/<TWITTER_USERNAME>'
  s.ios.deployment_target = '9.0'

  # 支持静态框架如.a文件
  s.static_framework = true
  s.source_files = 'RRXNNetwork/Classes/*.h'
  s.vendored_libraries = 'RRXNNetwork/Classes/libRRXNNetwork.a'

  # 向外提供的库
  #  s.ios.vendored_frameworks = "xxx/**/*.framework"
  
  
  # s.resource_bundles = {
  #   'RRXNNetwork' => ['RRXNNetwork/Assets/*.png']
  # }

  # s.public_header_files = 'Pod/Classes/**/*.h'
  # s.frameworks = 'UIKit', 'MapKit'
  # s.dependency 'AFNetworking', '~> 2.3'
    s.dependency 'MJExtension', '~> 3.4.0'
end

