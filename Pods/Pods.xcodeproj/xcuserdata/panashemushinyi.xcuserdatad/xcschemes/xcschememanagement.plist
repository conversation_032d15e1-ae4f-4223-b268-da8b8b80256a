<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>SchemeUserState</key>
	<dict>
		<key>CRBoxInputView.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>CWLateralSlide.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseCore.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseCoreDiagnostics.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseInstallations.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseMessaging.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Flutter.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FlutterPluginRegistrant.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GMObjC-GMObjC.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GMObjC.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GMOpenSSL.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleDataTransport-GoogleDataTransport_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleDataTransport.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleMaps.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GooglePlaces.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleUtilities-GoogleUtilities_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleUtilities.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>IQKeyboardManager-IQKeyboardManager.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>IQKeyboardManager.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>LBXScan.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>MBProgressHUD.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>MJExtension-MJExtension.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>MJExtension.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>MJRefresh-MJRefresh.Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>MJRefresh.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Masonry.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Pods-xWalletPro_iOS.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>PromisesObjC-FBLPromises_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>PromisesObjC.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RRXNNetwork.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SDCycleScrollView.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SDWebImage-SDWebImage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SDWebImage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Toast-Toast.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Toast.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>device_info.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_secure_storage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>fluttertoast.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>image_picker_ios-image_picker_ios_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>image_picker_ios.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>lottie-ios.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>nanopb.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>package_info.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>rrworkmanager.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>shared_preferences_foundation-shared_preferences_foundation_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>shared_preferences_foundation.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
	</dict>
	<key>SuppressBuildableAutocreation</key>
	<dict/>
</dict>
</plist>
