#import "RrworkmanagerPlugin.h"
#if __has_include(<rrworkmanager/rrworkmanager-Swift.h>)
#import <rrworkmanager/rrworkmanager-Swift.h>
#else
// Support project import fallback if the generated compatibility header
// is not copied when this plugin is created as a library.
// https://forums.swift.org/t/swift-static-libraries-dont-copy-generated-objective-c-header/19816
#import "rrworkmanager-Swift.h"
#endif

@implementation RRWorkManagerPlugin
+ (void)registerWithRegistrar:(NSObject<FlutterPluginRegistrar>*)registrar {
  [SwiftRrworkmanagerPlugin registerWithRegistrar:registrar];
}
@end
