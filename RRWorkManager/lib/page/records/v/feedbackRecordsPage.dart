import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:rrworkmanager/common_widget/AppBarWidget.dart';
import 'package:rrworkmanager/page/records/m/feedbackRecordsApiBean.dart';
import 'package:rrworkmanager/page/records/v/feedbackRecordDetailsPage.dart';
import 'package:rrworkmanager/page/records/vm/FeedbackRecordsViewModel.dart';
import 'package:rrworkmanager/rrworkmanager.dart';
import 'package:rrworkmanager/utils/Global.dart';
import 'package:rrworkmanager/utils/StringUtils.dart';

class FeedbackRecordsPage extends StatefulWidget {
  const FeedbackRecordsPage({Key? key}) : super(key: key);
  @override
  _FeedbackRecordsPageState createState() => _FeedbackRecordsPageState();
}

class _FeedbackRecordsPageState extends State<FeedbackRecordsPage> {
  final FeedbackRecordsViewModel _model = FeedbackRecordsViewModel();

  final RefreshController _refreshController =
      RefreshController(initialRefresh: true);
  ScrollController scrollController = ScrollController();

  final typeList = [
    {"type": "-", "typeDesc": trans("records_label_all_types")},
    {"type": "1", "typeDesc": trans("feedback_label_dysfunction")},
    {"type": "2", "typeDesc": trans("feedback_label_product_recommendations")},
    {
      "type": "3",
      "typeDesc": trans("transaction_title_transaction_complaints")
    },
  ];

  final stateList = [
    {"type": "-", "typeDesc": trans("records_label_all_types")},
    {"type": "0", "typeDesc": trans("records_label_status_received")},
    {"type": "1", "typeDesc": trans("records_label_status_following_up")},
    {
      "type": "2",
      "typeDesc": trans("records_label_status_completed")
    },
  ];

  //反馈类型
  String selectType = "";

  //工单状态
  String selectStates = "";

  void _onRefresh() {
    _model.requestWorkOrderRecordList(context,
        workOrderType: selectType,
        status: selectStates,
        refreshController: _refreshController);
  }

  void _onLoading() {
    _model.requestWorkOrderRecordListMore(context,
        workOrderType: selectType,
        status: selectStates,
        refreshController: _refreshController);
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _onRefresh();
    });
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<FeedbackRecordsViewModel>(
      create: (_) => _model,
      child: Scaffold(
        appBar: AppBarUtils(title: trans("records_title_feedback_records"))
            .build(context),
        body: _body(context),
        backgroundColor: Global.lightBackGroundColor,
      ),
    );
  }

  Widget _body(BuildContext context) {
    return Consumer<FeedbackRecordsViewModel>(builder: (context, model, child) {
      return Column(
        children: [
          _filterWidget(context),
          Expanded(child: _feedbackRecordsWidget(context))
        ],
      );
    });
  }

  //筛选条件
  Widget _filterWidget(BuildContext context) {
    return Consumer<FeedbackRecordsViewModel>(
        builder: ((context, model, child) {
      String typeDesc = trans("records_label_all_types");
      String stateDesc = trans("records_label_all_states");
      bool isAllType = true;
      bool isAllState = true;
      if (model.feedbackType == "1") {
        typeDesc = trans("feedback_label_dysfunction");
        isAllType = false;
      } else if (model.feedbackType == "2") {
        typeDesc = trans("feedback_label_dysfunction");
        isAllType = false;
      } else if (model.feedbackType == "3") {
        typeDesc = trans("transaction_title_transaction_complaints");
        isAllType = false;
      } 
      if (model.feedbackStates == "0") {
        stateDesc = trans("records_label_status_received");
        isAllState = false;
      } else if (model.feedbackStates == "1") {
        stateDesc = trans("records_label_status_following_up");
        isAllState = false;
      } else if (model.feedbackStates == "2") {
        stateDesc = trans("records_label_status_completed");
        isAllState = false;
      } 
      return Container(
        width: 1.sw,
        height: 90.w,
        decoration: const BoxDecoration(
            color: Colors.white,
            border: Border(
                bottom: BorderSide(color: Global.inputBorderColor, width: 1))),
        child: Row(
          children: [
            Expanded(
                child: GestureDetector(
              onTap: () {
                _showSelectFilter(context, typeList, "feedbackType");
              },
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
                child: Text(
                  typeDesc,
                  style:
                      TextStyle(color: isAllType ? Global.tipsTextColor : Global.mainTextColor, fontSize: 28.sp),
                ),
              ),
            )),
            const VerticalDivider(
              width: 1.0,
              color: Global.inputBorderColor,
            ),
            Expanded(
                child: GestureDetector(
              onTap: () {
                _showSelectFilter(context, stateList, "feedbackState");
              },
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
                child: Text(
                  stateDesc,
                  style:
                      TextStyle(color: isAllState ? Global.tipsTextColor : Global.mainTextColor, fontSize: 28.sp),
                ),
              ),
            )),
          ],
        ),
      );
    }));
  }

  Widget _feedbackRecordsWidget(BuildContext context) {
    return Consumer<FeedbackRecordsViewModel>(builder: (context, model, child) {
      if (model.records.isEmpty) {
        return _noFeedbackItem(context);
      }
      return Container(
          margin: EdgeInsets.only(top: 18.w),
          child: SmartRefresher(
            onRefresh: _onRefresh,
            enablePullUp: true,
            enablePullDown: false,
            onLoading: _onLoading,
            footer: CustomFooter(
              builder: (BuildContext context, LoadStatus? mode) {
                Widget? body;
                if (mode == LoadStatus.noMore) {
                  body = const Text("");
                }
                return SizedBox(
                  height: 55.0,
                  child: Center(child: body),
                );
              },
            ),
            controller: _refreshController,
            child: ListView.separated(
                cacheExtent: 180.w,
                itemBuilder: (context, index) {
                  FeedbackRecordBean bean = model.records[index];
                  return _recordItemWidget(context, bean);
                },
                separatorBuilder: (context, index) {
                  return Divider(
                    height: 16.w,
                    color: Global.lightBackGroundColor,
                  );
                },
                itemCount: model.records.length),
          ));
    });
  }

  //无记录
  Widget _noFeedbackItem(BuildContext context) {
    return Center(
      child: Container(
        height: 1.sh,
        alignment: Alignment.center,
        padding: EdgeInsets.only(bottom: 150.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              "resources/image/icon_norecord.png",
              package: Global.package,
            ),
            SizedBox(
              height: 24.h,
            ),
            Text(
              trans("records_label_no_record"),
              style: TextStyle(color: Global.mainTextColor, fontSize: 28.sp),
            ),
          ],
        ),
      ),
    );
  }

  Widget _recordItemWidget(
    BuildContext context,
    FeedbackRecordBean bean,
  ) {
    String workOrderType = "";
    if (bean.workOrderType! == "AF") {
      workOrderType =
          trans("feedback_label_dysfunction");
    } else if (bean.workOrderType! == "PS") {
      workOrderType = trans("feedback_label_product_recommendations");
    } else {
      workOrderType = trans("transaction_title_transaction_complaints");
    }
    if (bean.workOrderSubType != null && bean.workOrderSubType!.isNotEmpty) {
      workOrderType += "-"+bean.workOrderSubType!;
    }
    String status = trans("records_label_status_received");
    Color statusColor = Global.tipsTextColor;
    if (bean.status == "1") {
      status = trans("records_label_status_following_up");
      statusColor = RRWorkManagerConfig.mainColor;
    } else if (bean.status == "2") {
      status = trans("records_label_status_completed");
      statusColor = Global.successColor;
    }

    return GestureDetector(
      onTap: () {
        Navigator.push(
            context,
            MaterialPageRoute<void>(
              builder: (BuildContext context) => FeedbackRecordDetailsPage(
                bean: bean,
              ),
            ));
      },
      child: Container(
        width: 1.sw,
        height: 180.w,
        padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 24.w),
        color: Colors.white,
        child: Row(
          children: [
            Expanded(
                child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text.rich(TextSpan(children: [
                  TextSpan(
                      text: workOrderType,
                      style: TextStyle(
                          color: Global.mainTextColor, fontSize: 28.sp)),
                  WidgetSpan(
                      child: Container(
                    margin: EdgeInsets.only(left: 10.w),
                    padding:
                        EdgeInsets.symmetric(horizontal: 7.w, vertical: 3.w),
                    decoration: BoxDecoration(
                        border: Border.all(color: statusColor, width: 1),
                        borderRadius: BorderRadius.circular(4.r)),
                    child: Text(status,
                        style: TextStyle(
                          color: statusColor,
                          fontSize: 18.sp,
                        )),
                  )),
                ])),
                Text(
                  bean.createTime!,
                  style:
                      TextStyle(color: Global.tipsTextColor, fontSize: 24.sp),
                )
              ],
            )),
            Container(
              alignment: Alignment.center,
              margin: EdgeInsets.only(left: 30.w),
              child: Text(
                bean.status == "2"
                    ? trans("records_label_to_rate")
                    : trans("records_label_check"),
                style: TextStyle(color: Global.mainTextColor, fontSize: 24.sp),
              ),
            ),
            SizedBox(
              width: 36.w,
              child: Image.asset(
                "resources/image/icon_right.png",
                package: Global.package,
              ),
            )
          ],
        ),
      ),
    );
  }

  //照片选择方式
  void _showSelectFilter(BuildContext inputContext, List list, String type) {
    String selectString = "";
    if (type == "feedbackType") {
      selectString = selectType;
    } else {
      selectString = selectStates;
    }

    showModalBottomSheet(
        context: inputContext,
        isDismissible: true,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return GestureDetector(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: Container(
              color: Colors.transparent,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    width: 1.sw - 60.w,
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4.r)),
                    child: Column(
                      children: List.generate(list.length, (index) {
                        String select = list[index]["type"];
                        String value = list[index]["typeDesc"];
                        return GestureDetector(
                          onTap: () {
                            Navigator.of(context).pop();
                            if (type == "feedbackType") {
                              selectType = select;
                              if(select == "-") {
                                selectType = "";
                              }
                              _model.setFeedbackType(selectType);
                            } else {
                              selectStates = select;
                                         if(select == "-") {
                                selectStates = "";
                              }
                              _model.setFeedbackStates(selectStates);
                            }
                            _onRefresh();
                          },
                          child: Container(
                            height: 88.w,
                            width: 1.sw - 70.w,
                            padding: EdgeInsets.symmetric(horizontal: 10.w),
                            alignment: Alignment.center,
                            child: Row(
                              children: [
                                SizedBox(
                                  width: 36.w,
                                ),
                                Expanded(
                                    child: Text(
                                  value,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                      color: selectString == select
                                          ? RRWorkManagerConfig.mainColor
                                          : Global.mainTextColor,
                                      fontSize: 32.sp),
                                )),
                                selectString == select
                                    ? Image.asset(
                                        "resources/image/icon_check.png",
                                        width: 36.w,
                                        package: Global.package,
                                        color: Colors.blue,
                                      )
                                    : SizedBox(
                                        width: 36.w,
                                      )
                              ],
                            ),
                          ),
                        );
                      }),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Container(
                      width: 1.sw - 60.w,
                      height: 88.w,
                      margin: EdgeInsets.only(top: 40.w, bottom: 40.w),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4.r)),
                      child: Text(
                        trans("common_btn_cancel"),
                        style: TextStyle(
                            color: Global.mainTextColor, fontSize: 32.sp),
                      ),
                    ),
                  )
                ],
              ),
            ),
          );
        });
  }
}
