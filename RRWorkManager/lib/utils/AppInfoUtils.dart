// ignore_for_file: avoid_print
import 'dart:io';

import 'package:device_info/device_info.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:package_info/package_info.dart';
import 'package:rrworkmanager/rrworkmanagerplugin.dart';

///获取设备信息的方法
class AppInfo {
  ///获取设备唯一标识
  static Future<String> getUniqueId() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    RRWorkManagerPlugin.storage ??= const FlutterSecureStorage();
    String? uniqueID;
    if (Platform.isIOS) {
      uniqueID = (await RRWorkManagerPlugin.storage!.read(
        key: "WorkOrder-iOSDeviceId",
      ));
      print("===WorkOrder-iOSDeviceId=======$uniqueID=========");
      if (uniqueID == null) {
        IosDeviceInfo iosDeviceInfo = await deviceInfo.iosInfo;
        print("ios唯一设备码：" + iosDeviceInfo.identifierForVendor);
        uniqueID = iosDeviceInfo.identifierForVendor;
        await RRWorkManagerPlugin.storage!.write(
          key: "WorkOrder-iOSDeviceId",
          value: iosDeviceInfo.identifierForVendor,
        );
      }
      return uniqueID;
    } else {
      uniqueID = (await RRWorkManagerPlugin.storage!.read(
        key: "WorkOrder-androidDeviceId",
      ));
      print("===WorkOrder-androidDeviceId=======$uniqueID=========");
      if (uniqueID == null) {
        AndroidDeviceInfo androidDeviceInfo = await deviceInfo.androidInfo;
        print("WorkOrder-android唯一设备码：" + androidDeviceInfo.androidId);
        uniqueID = androidDeviceInfo.androidId;
        await RRWorkManagerPlugin.storage!.write(
          key: "WorkOrder-androidDeviceId",
          value: androidDeviceInfo.androidId,
        );
      }
      return uniqueID;
      // ===iOSDeviceId=======20B3B076-F5D8-4635-8336-310DE069E90F=========
    }
  }

  ///获取应用版本号
  static Future<String> getAppVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    return packageInfo.version;
  }

  ///获取手机品牌
  static Future<String> getBrand() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      return androidInfo.brand;
    } else if (Platform.isIOS) {
      return "Apple";
    }
    return "";
  }

  ///获取制造商
  static Future<String> getSeries() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      return androidInfo.model;
      // print(
      //     "==${androidInfo.version}=${androidInfo.board}=${androidInfo.bootloader}=${androidInfo.brand}==");
      // print(
      //     "==${androidInfo.device}=${androidInfo.display}=${androidInfo.fingerprint}=${androidInfo.hardware}==");
      // print(
      //     "==${androidInfo.host}=${androidInfo.id}=${androidInfo.manufacturer}=${androidInfo.model}=${androidInfo.product}==");
      // print(
      //     "==${androidInfo.tags}=${androidInfo.type}=${androidInfo.isPhysicalDevice}=${androidInfo.androidId}==");
      // print(
      //     "==${androidInfo.version.baseOS}=${androidInfo.version.previewSdkInt}=${androidInfo.version.securityPatch}=${androidInfo.version.codename}=${androidInfo.version.incremental}==");
      // print(
      //     "==${androidInfo.version.release}=${androidInfo.version.sdkInt}==");
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      String machine = iosInfo.utsname.machine;
      String series;
      switch (machine) {
        case "iPhone1,1":
          series = "iPhone 2G";
          break;
        case "iPhone1,2":
          series = "iPhone 3G";
          break;
        case "iPhone2,1":
          series = "iPhone 3GS";
          break;
        case "iPhone3,1":
        case "iPhone3,2":
        case "iPhone3,3":
          series = "iPhone 4";
          break;
        case "iPhone4,1":
          series = "iPhone 4S";
          break;
        case "iPhone5,1":
        case "iPhone5,2":
          series = "iPhone 5";
          break;
        case "iPhone5,3":
        case "iPhone5,4":
          series = "iPhone 5c";
          break;
        case "iPhone6,1":
        case "iPhone6,2":
          series = "iPhone 5s";
          break;
        case "iPhone7,1":
          series = "iPhone 6 Plus";
          break;
        case "iPhone7,2":
          series = "iPhone 6";
          break;
        case "iPhone8,1":
          series = "iPhone 6s";
          break;
        case "iPhone8,2":
          series = "iPhone 6s Plus";
          break;
        case "iPhone8,4":
          series = "iPhone SE";
          break;
        case "iPhone9,1":
        case "iPhone9,3":
          series = "iPhone 7";
          break;
        case "iPhone9,2":
        case "iPhone9,4":
          series = "iPhone 7 Plus";
          break;
        case "iPhone10,1":
        case "iPhone10,4":
          series = "iPhone 8";
          break;
        case "iPhone10,2":
        case "iPhone10,5":
          series = "iPhone 8 Plus";
          break;
        case "iPhone10,3":
        case "iPhone10,6":
          series = "iPhone X";
          break;
        case "iPhone11,2":
          series = "iPhone XS";
          break;
        case "iPhone11,4":
        case "iPhone11,6":
          series = "iPhone XS Max";
          break;
        case "iPhone11,8":
          series = "iPhone XR";
          break;
        case "iPhone12,1":
          series = "iPhone 11";
          break;
        case "iPhone12,3":
          series = "iPhone 11 Pro";
          break;
        case "iPhone12,5":
          series = "iPhone 11 Pro Max";
          break;
        case "iPhone12,8":
          series = "iPhone SE2";
          break;
        case "iPhone13,1":
          series = "iPhone 12 mini";
          break;
        case "iPhone13,2":
          series = "iPhone 12";
          break;
        case "iPhone13,3":
          series = "iPhone 12 Pro";
          break;
        case "iPhone13,4":
          series = "iPhone 12 Pro Max";
          break;
        default:
          series = machine;
          break;
      }
      // print("==${iosInfo.name}=${iosInfo.systemName}=${iosInfo.systemVersion}=${iosInfo.model}==");
      // print("==${iosInfo.localizedModel}=${iosInfo.identifierForVendor}=${iosInfo.isPhysicalDevice}==");
      // print("==${iosInfo.utsname.sysname}=${iosInfo.utsname.nodename}=${iosInfo.utsname.release}=${iosInfo.utsname.version}=${iosInfo.utsname.machine}==");
      return series;
    }
    return "";
  }

  ///获取系统版本
  static Future<String> getSystemVersion() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      // return androidInfo.version.sdkInt.toString();
      return androidInfo.version.release.toString();
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      return iosInfo.systemName + " " + iosInfo.systemVersion;
    }
    return "";
  }
}
