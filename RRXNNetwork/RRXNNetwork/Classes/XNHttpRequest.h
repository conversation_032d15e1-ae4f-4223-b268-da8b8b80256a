//
//  XNHttpRequest.h
//  WuZhouPush
//
//  Created by <PERSON> on 17/12/7.
//  Copyright © 2017年 卜雪妮. All rights reserved.
//

#import <Foundation/Foundation.h>
//正常数据请求Model
#import "XNNetworkModel.h"
//sdk请求Model
#import "XNSDKNetworkModel.h"

@interface XNHttpRequest : NSObject

/*
 * 请求类实例化
 */
+ (id _Nonnull )shareInstance;

/*
 新增请求添加请求头方法
 */
+ (void)addHTTPHeaderField:(NSDictionary * _Nonnull )requestHeaderDic;

/*
 新增请求添加请求公共参数
 */
+ (void)addPublicField:(NSDictionary * _Nonnull )requestPublicDic;


#pragma mark - publicMethod

#pragma mark -- xWallet加密方式
/*
 * 原生post请求
 * reqCode:接口地址
 * serve:服务器地址
 * 实际请求地址为 serve + reqCode
 * jsonDic:请求入参
 * success:成功回调
 * fail:失败回调
 * time:请求超时时间
 * encrypt 是否需要敏感数据加密
 * encryptKeyArr 需要做加密的key数组
 * encryptReqStrCode 如果需要敏感数据加密，这里传入获取公钥key的接口地址
 * language:当前语言
 */
+ (void)postSend:(NSString *)reqCode urlString:(NSString *)serve jsonDic:(NSMutableDictionary *)jsonDic success:(void (^)(XNNetworkModel *model))success
         failure:(void (^)(NSError *error))failure
            time:(NSTimeInterval)time encrypt:(BOOL)encrypt encryptKeyArr:(NSArray *)encryptKeyArr encryptReqStrCode:(NSString *)encryptReqStrCode language:(NSString *)currentLanguage;

/*
 * 原生post请求上传图片组
 * reqCode:接口地址
 * serve:服务器地址
 * 实际请求地址为 serve + reqCode
 * jsonDic:请求入参
 * success:成功回调
 * fail:失败回调
 * time:请求超时时间
 * encrypt 是否需要敏感数据加密
 * encryptKeyArr 需要做加密的key数组
 * encryptReqStrCode 如果需要敏感数据加密，这里传入获取公钥key的接口地址
 * language:当前语言
 */
+(void)upLoadSend:(NSString *)reqCode urlString:(NSString *)serve andImageArray:(NSMutableArray *)imageArray jsonDic:(NSMutableDictionary *)jsonDic success:(void (^)(XNNetworkModel *model))success
          failure:(void (^)(NSError *error))failure
             time:(NSTimeInterval)time encrypt:(BOOL)encrypt  encryptKeyArr:(NSArray *)encryptKeyArr encryptReqStrCode:(NSString *)encryptReqStrCode language:(NSString *)currentLanguage;


#pragma mark -- xStore加密方式
/*
 * xStore原生post请求 - 加密方式与原有的加密方式不同
 * reqCode:接口地址
 * serve:服务器地址
 * 实际请求地址为 serve + reqCode
 * jsonDic:请求入参
 * success:成功回调
 * fail:失败回调
 * time:请求超时时间
 * rsaPublicKey 用于做报文rsa加密的公钥key
 * language:当前语言
 * dealerNo 代理商编号，需要根据代理商编号进行解密验签
 */
+ (void)sdkPostSend:(NSString *)reqCode urlString:(NSString *)serve jsonDic:(NSMutableDictionary *)jsonDic success:(void (^)(XNSDKNetworkModel *model))success
         failure:(void (^)(NSError *error))failure
               time:(NSTimeInterval)time rsaPublicKey:(NSString *)rsaPublicKey language:(NSString *)currentLanguage dealerNo:(NSString *)dealerNo;


#pragma mark -- PB加密方式

/// pb原生post请求
/// @param reqCode 接口地址
/// @param serve 服务器地址   实际请求地址为 reqCode + serve
/// @param jsonDic 请求入参
/// @param success 成功回调
/// @param failure 失败回调
/// @param rsaPublicKey 用于做报文rsa加密的公钥key
/// @param time 请求超时时间
/// @param currentLanguage 当前语言
+ (void)pbPostSend:(NSString *)reqCode urlString:(NSString *)serve jsonDic:(NSMutableDictionary *)jsonDic success:(void (^)(XNSDKNetworkModel *model))success
            failure:(void (^)(NSError *error))failure rsaPublicKey:(NSString *)rsaPublicKey rsaPrivateKey:(NSString *)rsaPrivateKey time:(NSTimeInterval)time language:(NSString *)currentLanguage;

/// pb原生post请求上传图片组
/// @param reqCode 接口地址
/// @param serve 服务器地址   实际请求地址为 serve + reqCode
/// @param imageAddressArray 上传的图片文件路径地址List
/// @param jsonDic 请求入参
/// @param success 成功回调
/// @param failure 失败回调
/// @param rsaPublicKey 用于做报文rsa加密的公钥key
/// @param time 请求超时时间
/// @param currentLanguage 当前语言
/// @param encryptKeyArr 需要做加密的key数组
/// @param encrypt 是否需要敏感数据加密
+ (void)pbUpLoadSend:(NSString *)reqCode urlString:(NSString *)serve andImageAddressArray:(NSMutableArray *)imageAddressArray jsonDic:(NSMutableDictionary *)jsonDic success:(void (^)(XNSDKNetworkModel *model))success
             failure:(void (^)(NSError *error))failure encrypt:(BOOL)encrypt encryptKeyArr:(NSArray *)encryptKeyArr rsaPublicKey:(NSString *)rsaPublicKey time:(NSTimeInterval)time language:(NSString *)currentLanguage;


@end
