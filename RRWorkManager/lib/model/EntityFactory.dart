/*
 * @Description: <PERSON>生成工厂类
 * @Author: <PERSON>
 * @Date: 2021-11-16 14:32:34
 * @LastEditTime: 2023-05-15 15:08:38
 * @LastEditors: Shirley
 * @Reference: 
 */

import 'package:rrworkmanager/page/feedback/m/SelectCountryBean.dart';
import 'package:rrworkmanager/page/feedback/m/UploadImageBean.dart';
import 'package:rrworkmanager/page/feedback/m/feedbackComplaintNoticeBean.dart';
import 'package:rrworkmanager/page/feedback/m/feedbackSubTypeListApiBean.dart';
import 'package:rrworkmanager/page/records/m/feedbackRecordDetailsApiBean.dart';
import 'package:rrworkmanager/page/records/m/feedbackRecordsApiBean.dart';

///实体类工程方法
class EntityFactory {
  static T? generateOBJ<T>(json) {
    if (T.toString() == "FeedbackSubTypeListApiBean") {
      return FeedbackSubTypeListApiBean.fromJson(json) as T;
    } else if (T.toString() == "UpLoadImageBean") {
      return UpLoadImageBean.fromJson(json) as T;
    } else if (T.toString() == "FeedbackRecordsApiBean") {
      return FeedbackRecordsApiBean.fromJson(json) as T;
    } else if (T.toString() == "FeedbackRecordDetailsApiBean") {
      return FeedbackRecordDetailsApiBean.fromJson(json) as T;
    } else if (T.toString() == "SelectCountryBean") {
      return SelectCountryBean.fromJson(json) as T;
    } else if (T.toString() == "FeedbackComplaintNoticeBean") {
      return FeedbackComplaintNoticeBean.fromJson(json) as T;
    } else {
      return null;
    }
  }
}
