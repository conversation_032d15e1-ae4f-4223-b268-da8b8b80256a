CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
EMBEDDED_CONTENT_CONTAINS_SWIFT = YES
FRAMEWORK_SEARCH_PATHS = $(inherited) "${PODS_ROOT}/GMOpenSSL/Frameworks" "${PODS_ROOT}/GoogleMaps/Base/Frameworks" "${PODS_ROOT}/GoogleMaps/Maps/Frameworks" "${PODS_ROOT}/GooglePlaces/Frameworks" "${PODS_XCFRAMEWORKS_BUILD_DIR}/GMOpenSSL"
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1 $(inherited) PB_FIELD_32BIT=1 PB_NO_PACKED_STRUCTS=1 PB_ENABLE_MALLOC=1
HEADER_SEARCH_PATHS = $(inherited) "${PODS_ROOT}/Headers/Public" "${PODS_ROOT}/Headers/Public/CRBoxInputView" "${PODS_ROOT}/Headers/Public/CWLateralSlide" "${PODS_ROOT}/Headers/Public/FirebaseCore" "${PODS_ROOT}/Headers/Public/FirebaseCoreDiagnostics" "${PODS_ROOT}/Headers/Public/FirebaseInstallations" "${PODS_ROOT}/Headers/Public/FirebaseMessaging" "${PODS_ROOT}/Headers/Public/FlutterPluginRegistrant" "${PODS_ROOT}/Headers/Public/GMObjC" "${PODS_ROOT}/Headers/Public/GoogleDataTransport" "${PODS_ROOT}/Headers/Public/GoogleUtilities" "${PODS_ROOT}/Headers/Public/IQKeyboardManager" "${PODS_ROOT}/Headers/Public/LBXScan" "${PODS_ROOT}/Headers/Public/MBProgressHUD" "${PODS_ROOT}/Headers/Public/MJExtension" "${PODS_ROOT}/Headers/Public/MJRefresh" "${PODS_ROOT}/Headers/Public/Masonry" "${PODS_ROOT}/Headers/Public/PromisesObjC" "${PODS_ROOT}/Headers/Public/RRXNNetwork" "${PODS_ROOT}/Headers/Public/SDCycleScrollView" "${PODS_ROOT}/Headers/Public/SDWebImage" "${PODS_ROOT}/Headers/Public/Toast" "${PODS_ROOT}/Headers/Public/device_info" "${PODS_ROOT}/Headers/Public/flutter_secure_storage" "${PODS_ROOT}/Headers/Public/fluttertoast" "${PODS_ROOT}/Headers/Public/image_picker_ios" "${PODS_ROOT}/Headers/Public/lottie-ios" "${PODS_ROOT}/Headers/Public/nanopb" "${PODS_ROOT}/Headers/Public/package_info" "${PODS_ROOT}/Headers/Public/rrworkmanager"
LD_RUNPATH_SEARCH_PATHS = $(inherited) /usr/lib/swift
LIBRARY_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/CRBoxInputView" "${PODS_CONFIGURATION_BUILD_DIR}/CWLateralSlide" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreDiagnostics" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging" "${PODS_CONFIGURATION_BUILD_DIR}/FlutterPluginRegistrant" "${PODS_CONFIGURATION_BUILD_DIR}/GMObjC" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities" "${PODS_CONFIGURATION_BUILD_DIR}/IQKeyboardManager" "${PODS_CONFIGURATION_BUILD_DIR}/LBXScan" "${PODS_CONFIGURATION_BUILD_DIR}/MBProgressHUD" "${PODS_CONFIGURATION_BUILD_DIR}/MJExtension" "${PODS_CONFIGURATION_BUILD_DIR}/MJRefresh" "${PODS_CONFIGURATION_BUILD_DIR}/Masonry" "${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC" "${PODS_CONFIGURATION_BUILD_DIR}/SDCycleScrollView" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage" "${PODS_CONFIGURATION_BUILD_DIR}/Toast" "${PODS_CONFIGURATION_BUILD_DIR}/device_info" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_secure_storage" "${PODS_CONFIGURATION_BUILD_DIR}/fluttertoast" "${PODS_CONFIGURATION_BUILD_DIR}/image_picker_ios" "${PODS_CONFIGURATION_BUILD_DIR}/lottie-ios" "${PODS_CONFIGURATION_BUILD_DIR}/nanopb" "${PODS_CONFIGURATION_BUILD_DIR}/package_info" "${PODS_CONFIGURATION_BUILD_DIR}/rrworkmanager" "${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation" "${PODS_ROOT}/../RRXNNetwork/RRXNNetwork/Classes" "${TOOLCHAIN_DIR}/usr/lib/swift/${PLATFORM_NAME}" /usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)/ $(SDKROOT)/usr/lib/swift
OTHER_CFLAGS = $(inherited) -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/rrworkmanager/rrworkmanager.modulemap" -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation/shared_preferences_foundation.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Private/image_picker_ios/image_picker_ios.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/FBLPromises/PromisesObjC.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/FlutterPluginRegistrant/FlutterPluginRegistrant.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/GMObjC/GMObjC.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/device_info/device_info.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/package_info/package_info.modulemap" -isystem "${PODS_ROOT}/Headers/Public"
OTHER_LDFLAGS = $(inherited) -ObjC -l"CRBoxInputView" -l"CWLateralSlide" -l"FirebaseCore" -l"FirebaseCoreDiagnostics" -l"FirebaseInstallations" -l"FirebaseMessaging" -l"FlutterPluginRegistrant" -l"GMObjC" -l"GoogleDataTransport" -l"GoogleUtilities" -l"IQKeyboardManager" -l"LBXScan" -l"MBProgressHUD" -l"MJExtension" -l"MJRefresh" -l"Masonry" -l"PromisesObjC" -l"RRXNNetwork" -l"SDCycleScrollView" -l"SDWebImage" -l"Toast" -l"c++" -l"device_info" -l"flutter_secure_storage" -l"fluttertoast" -l"image_picker_ios" -l"lottie-ios" -l"nanopb" -l"package_info" -l"rrworkmanager" -l"shared_preferences_foundation" -l"sqlite3" -l"z" -framework "AVFoundation" -framework "Accelerate" -framework "CoreData" -framework "CoreGraphics" -framework "CoreImage" -framework "CoreLocation" -framework "CoreTelephony" -framework "CoreText" -framework "Foundation" -framework "GLKit" -framework "GoogleMaps" -framework "GoogleMapsBase" -framework "GoogleMapsCore" -framework "GooglePlaces" -framework "ImageIO" -framework "Metal" -framework "OpenGLES" -framework "OpenSSL" -framework "QuartzCore" -framework "Security" -framework "SystemConfiguration" -framework "UIKit" -weak_framework "UserNotifications"
OTHER_MODULE_VERIFIER_FLAGS = $(inherited) "-F${PODS_CONFIGURATION_BUILD_DIR}/CRBoxInputView" "-F${PODS_CONFIGURATION_BUILD_DIR}/CWLateralSlide" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreDiagnostics" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging" "-F${PODS_CONFIGURATION_BUILD_DIR}/Flutter" "-F${PODS_CONFIGURATION_BUILD_DIR}/FlutterPluginRegistrant" "-F${PODS_CONFIGURATION_BUILD_DIR}/GMObjC" "-F${PODS_CONFIGURATION_BUILD_DIR}/GMOpenSSL" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleMaps" "-F${PODS_CONFIGURATION_BUILD_DIR}/GooglePlaces" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities" "-F${PODS_CONFIGURATION_BUILD_DIR}/IQKeyboardManager" "-F${PODS_CONFIGURATION_BUILD_DIR}/LBXScan" "-F${PODS_CONFIGURATION_BUILD_DIR}/MBProgressHUD" "-F${PODS_CONFIGURATION_BUILD_DIR}/MJExtension" "-F${PODS_CONFIGURATION_BUILD_DIR}/MJRefresh" "-F${PODS_CONFIGURATION_BUILD_DIR}/Masonry" "-F${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC" "-F${PODS_CONFIGURATION_BUILD_DIR}/RRXNNetwork" "-F${PODS_CONFIGURATION_BUILD_DIR}/SDCycleScrollView" "-F${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage" "-F${PODS_CONFIGURATION_BUILD_DIR}/Toast" "-F${PODS_CONFIGURATION_BUILD_DIR}/device_info" "-F${PODS_CONFIGURATION_BUILD_DIR}/flutter_secure_storage" "-F${PODS_CONFIGURATION_BUILD_DIR}/fluttertoast" "-F${PODS_CONFIGURATION_BUILD_DIR}/image_picker_ios" "-F${PODS_CONFIGURATION_BUILD_DIR}/lottie-ios" "-F${PODS_CONFIGURATION_BUILD_DIR}/nanopb" "-F${PODS_CONFIGURATION_BUILD_DIR}/package_info" "-F${PODS_CONFIGURATION_BUILD_DIR}/rrworkmanager" "-F${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation"
OTHER_SWIFT_FLAGS = $(inherited) -D COCOAPODS -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/rrworkmanager/rrworkmanager.modulemap" -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation/shared_preferences_foundation.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Private/image_picker_ios/image_picker_ios.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/FBLPromises/PromisesObjC.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/FlutterPluginRegistrant/FlutterPluginRegistrant.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/GMObjC/GMObjC.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/device_info/device_info.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/package_info/package_info.modulemap"
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_PODFILE_DIR_PATH = ${SRCROOT}/.
PODS_ROOT = ${SRCROOT}/Pods
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
SWIFT_INCLUDE_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/rrworkmanager" "${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation"
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
