PODS:
  - Expecta (1.0.6)
  - "Expecta+Snapshots (3.1.1)":
    - Expecta (~> 1.0)
    - FBSnapshotTestCase/Core (~> 2.0)
    - Specta (~> 1.0)
  - FBSnapshotTestCase (2.1.4):
    - FBSnapshotTestCase/SwiftSupport (= 2.1.4)
  - FBSnapshotTestCase/Core (2.1.4)
  - FBSnapshotTestCase/SwiftSupport (2.1.4):
    - FBSnapshotTestCase/Core
  - MJExtension (3.2.0)
  - RRXNNetwork (0.0.22):
    - MJExtension (= 3.2.0)
  - Specta (1.0.7)

DEPENDENCIES:
  - Expecta
  - "Expecta+Snapshots"
  - FBSnapshotTestCase
  - RRXNNetwork (from `../`)
  - Specta

SPEC REPOS:
  trunk:
    - Expecta
    - "Expecta+Snapshots"
    - FBSnapshotTestCase
    - MJExtension
    - Specta

EXTERNAL SOURCES:
  RRXNNetwork:
    :path: "../"

SPEC CHECKSUMS:
  Expecta: 3b6bd90a64b9a1dcb0b70aa0e10a7f8f631667d5
  "Expecta+Snapshots": dcff217eef506dabd6dfdc7864ea2da321fafbb8
  FBSnapshotTestCase: 094f9f314decbabe373b87cc339bea235a63e07a
  MJExtension: e22bed65fdcd38e957242982b7384f2984c9088e
  RRXNNetwork: 72783ee163bf14d8c862da32d6b935862b3f9968
  Specta: 3e1bd89c3517421982dc4d1c992503e48bd5fe66

PODFILE CHECKSUM: 9529f379699e51f96c92a811fdb4ce1c79c20598

COCOAPODS: 1.10.1
