import 'package:azlistview/azlistview.dart';

class SelectCountryBean {
  int? code;
  List<CountryBean>? data;
  bool? success;
  String? message;
  String? status;

  SelectCountryBean(
      {this.code, this.data, this.success, this.message, this.status});

  SelectCountryBean.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    if (json['data'] != null) {
      data = <CountryBean>[];
      json['data'].forEach((v) {
        data!.add(CountryBean.fromJson(v));
      });
    }
    success = json['success'];
    message = json['message'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['success'] = success;
    data['message'] = message;
    data['status'] = status;
    return data;
  }
}

class CountryBean extends ISuspensionBean {
  String? areaCode; //电话区号
  String? commonlyUsed; //是否常用 0-非常用 1-常用
  String? value; //国家名称
  String? key; //国家表主键 唯一
  late String tagIndex;

  CountryBean({this.areaCode, this.commonlyUsed, this.value, this.key});

  CountryBean.fromJson(Map<String, dynamic> json) {
    areaCode = json['areaCode'];
    commonlyUsed = json['commonlyUsed'];
    value = json['value'];
    key = json['key'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['areaCode'] = areaCode;
    data['commonlyUsed'] = commonlyUsed;
    data['value'] = value;
    data['key'] = key;
    return data;
  }

  @override
  String getSuspensionTag() => tagIndex;
}
