import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rrworkmanager/common_widget/NavButton.dart';
import 'package:rrworkmanager/utils/Global.dart';

///appbar的工具类
class AppBarUtils {
  final String title;
  final bool canBack;
  final bool centerTitle;
  final TextStyle? textStyle;

  final List<Widget>? action;
  final GestureTapCallback? callback;

  final double elevation;
  final Color backgroundColor;
  final PreferredSizeWidget? bottom;

  final IconData? iconData;
  final String navBack;
  final Color bottomLlineColor;

  AppBarUtils({
    required this.title,
    this.navBack = "resources/image/nav_back_black.png",
    this.canBack = true,
    this.centerTitle = false,
    this.textStyle,
    this.action,
    this.callback,
    this.elevation = 0,
    this.backgroundColor = Colors.white,
    this.bottom,
    this.bottomLlineColor = Global.lineColor,
    this.iconData,
  });

  ///构建appBar的工具类
  PreferredSizeWidget build(BuildContext context) {
    return AppBar(
      leadingWidth: 88.w,
      leading: canBack
          ? Container(
              // margin: EdgeInsets.only(left: .w),
              child: NavButton(
                  iconHeight: 42.w,
                  iconWidth: 42.w,
                  iconRes: navBack,
                  hasFlag: false,
                  onPressed: () {
                    callback != null ? callback!() : Navigator.of(context).pop();
                  }),
            )
          : null,
      centerTitle: centerTitle,
      title: Container(
        // margin: EdgeInsets.only(right: 30.w),
        child: Row(
          children: [
            // Container(
            //   margin: EdgeInsets.only(right: 20.w),
            //   child: Image.asset(
            //     "resources/image/nav_logo.png",
            //     width: 141.w,
            //     height: 48.w,
            //   ),
            // ),
            SizedBox(
              width: action != null && action!.isNotEmpty
                  ? 68.w * action!.length
                  : 0,
            ),
            Container(
              width: action != null && action!.isNotEmpty
                  ? 1.sw - 88.w * 2 - 68.w * action!.length*2
                  : 1.sw - 88.w * 2,
              alignment: Alignment.center,
              child: Text(title,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: textStyle ??
                      TextStyle(fontSize: 32.sp, fontWeight: FontWeight.w500,
                      color: Global.mainTextColor
                      )),
            )
          ],
        ),
      ),
      titleSpacing: 0.0,
      automaticallyImplyLeading: canBack,
      backgroundColor: backgroundColor,
      actions: action,
      elevation: elevation,
      bottom: bottom,
      shape: UnderlineInputBorder(
       borderSide: BorderSide(width: 0.5.w,color: bottomLlineColor)
      ),
    );
  }
}
