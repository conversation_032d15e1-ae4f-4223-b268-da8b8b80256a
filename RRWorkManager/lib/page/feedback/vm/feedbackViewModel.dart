import 'package:flutter/material.dart';
import 'package:rrworkmanager/base/BaseViewModel.dart';
import 'package:rrworkmanager/config/StaticFieldConfig.dart';
import 'package:rrworkmanager/net/HttpUtils.dart';
import 'package:rrworkmanager/page/feedback/m/feedbackSubTypeListApiBean.dart';
import 'package:rrworkmanager/rrworkmanagerplugin.dart';
import 'package:rrworkmanager/utils/StringUtils.dart';
import 'package:rrworkmanager/utils/ToastUtils.dart';

class FeedbackViewModel extends BaseViewModel {
  //工单类型
  List typeList = [
    trans("feedback_label_dysfunction"),
    trans("feedback_label_product_recommendations")
  ];

  ///查询功能异常子类型
  void requestWorkOrderSubTypeList(
      BuildContext context, String workOrderType, Success success, Fail fail) {
    String visibleConfig = "01";
    if (RRWorkManagerPlugin.platform == "Merchant") {
      visibleConfig = "02";
    } else if (RRWorkManagerPlugin.platform == "User") {
      visibleConfig = "03";
    }
    String workType = "AF";
    if (workOrderType == "product") {
      workType = "PS";
    } else if (workOrderType == "transaction") {
      workType = "TC";
    }

    HttpUtils.getInstance().posHttp<FeedbackSubTypeListApiBean>(
        context, UrlConfig.workOrderSubTypeQuery, {
      RequestParamConfig.typeAttribution: workType,
      RequestParamConfig.visibleConfig: visibleConfig,
      RequestParamConfig.isViewDeleted: false,
    }, (data) {
      success(data);
      notifyListeners();
    }, (error, code) {
      fail(error, code);
      ToastUtils.showToast(error);
    }, isShowLoading: true);
  }
}
