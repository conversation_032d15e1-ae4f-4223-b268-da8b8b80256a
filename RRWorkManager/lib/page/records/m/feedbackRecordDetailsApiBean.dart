class FeedbackRecordDetailsApiBean {
  int? code;
  FeedbackRecordDetailsDataBean? data;
  bool? success;
  String? message;
  String? status;
  int? timestamp;

  FeedbackRecordDetailsApiBean(
      {this.code,
      this.data,
      this.success,
      this.message,
      this.status,
      this.timestamp});

  FeedbackRecordDetailsApiBean.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    data = json['data'] != null ? FeedbackRecordDetailsDataBean.fromJson(json['data']) : null;
    success = json['success'];
    message = json['message'];
    status = json['status'];
    timestamp = json['timestamp'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['success'] = success;
    data['message'] = message;
    data['status'] = status;
    data['timestamp'] = timestamp;
    return data;
  }
}

class FeedbackRecordDetailsDataBean {
  String? phoneAreaCode;
  List<WorkOrderAssignRecord>? assignRecordList;
  String? customerPhone;
  List<String>? voucherList;
  List<WorkOrderTransactionInfo>? transactionList;
  WorkOrderProcessRecord? processRecord;
  WorkOrderRateBean? satisfactionRating;

  FeedbackRecordDetailsDataBean(
      {this.phoneAreaCode,
      this.assignRecordList,
      this.customerPhone,
      this.voucherList,
      this.transactionList,
      this.processRecord,
      this.satisfactionRating});

  FeedbackRecordDetailsDataBean.fromJson(Map<String, dynamic> json) {
    phoneAreaCode = json['phoneAreaCode'];
    if (json['assignRecordList'] != null) {
      assignRecordList = <WorkOrderAssignRecord>[];
      json['assignRecordList'].forEach((v) {
        assignRecordList!.add(WorkOrderAssignRecord.fromJson(v));
      });
    }
    customerPhone = json['customerPhone'];
    voucherList = json['voucherList'].cast<String>();
    if (json['transactionList'] != null) {
      transactionList = <WorkOrderTransactionInfo>[];
      json['transactionList'].forEach((v) {
        transactionList!.add(WorkOrderTransactionInfo.fromJson(v));
      });
    }
    processRecord = json['processRecord'] != null
        ? WorkOrderProcessRecord.fromJson(json['processRecord'])
        : null;
    satisfactionRating = json['satisfactionRating'] != null
        ? WorkOrderRateBean.fromJson(json['satisfactionRating'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['phoneAreaCode'] = phoneAreaCode;
    if (assignRecordList != null) {
      data['assignRecordList'] =
          assignRecordList!.map((v) => v.toJson()).toList();
    }
    data['customerPhone'] = customerPhone;
    data['voucherList'] = voucherList;
    if (transactionList != null) {
      data['transactionList'] =
          transactionList!.map((v) => v.toJson()).toList();
    }
    if (processRecord != null) {
      data['processRecord'] = processRecord!.toJson();
    }
    if (satisfactionRating != null) {
      data['satisfactionRating'] = satisfactionRating!.toJson();
    }
    return data;
  }
}

class WorkOrderAssignRecord {
  String? designeeName;
  String? assignTime;
  String? designeeId;

  WorkOrderAssignRecord({this.designeeName, this.assignTime, this.designeeId});

  WorkOrderAssignRecord.fromJson(Map<String, dynamic> json) {
    designeeName = json['designeeName'];
    assignTime = json['assignTime'];
    designeeId = json['designeeId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['designeeName'] = designeeName;
    data['assignTime'] = assignTime;
    data['designeeId'] = designeeId;
    return data;
  }
}

class WorkOrderTransactionInfo {
  String? orderNo;
  String? transType;
  num? transAmt;
  String? orderStatus;
  String? currency;
  String? createdTime;

  WorkOrderTransactionInfo(
      {this.orderNo,
      this.transType,
      this.transAmt,
      this.orderStatus,
      this.currency,
      this.createdTime,
      });

  WorkOrderTransactionInfo.fromJson(Map<String, dynamic> json) {
    orderNo = json['orderNo'];
    transType = json['transType'];
    transAmt = json['transAmt'];
    orderStatus = json['orderStatus'];
    currency = json['currency'];
    createdTime = json['createdTime'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['orderNo'] = orderNo;
    data['transType'] = transType;
    data['transAmt'] = transAmt;
    data['orderStatus'] = orderStatus;
    data['currency'] = currency;
    data['createdTime'] = createdTime;
    return data;
  }
}

class WorkOrderProcessRecord {
  String? executorName;
  String? executorId;
  String? overTime;
  String? description;
  String? processType;

  WorkOrderProcessRecord(
      {this.executorName,
      this.executorId,
      this.overTime,
      this.description,
      this.processType});

  WorkOrderProcessRecord.fromJson(Map<String, dynamic> json) {
    executorName = json['executorName'];
    executorId = json['executorId'];
    overTime = json['overTime'];
    description = json['description'];
    processType = json['processType'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['executorName'] = executorName;
    data['executorId'] = executorId;
    data['overTime'] = overTime;
    data['description'] = description;
    data['processType'] = processType;
    return data;
  }
}

class WorkOrderRateBean {
  int? score;
  String? scoringTime;
  int? satisfaction;

  WorkOrderRateBean({this.score, this.scoringTime, this.satisfaction});

  WorkOrderRateBean.fromJson(Map<String, dynamic> json) {
    score = json['score'];
    scoringTime = json['scoringTime'];
    satisfaction = json['satisfaction'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['score'] = score;
    data['scoringTime'] = scoringTime;
    data['satisfaction'] = satisfaction;
    return data;
  }
}