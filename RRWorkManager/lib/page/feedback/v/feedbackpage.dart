import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:rrworkmanager/page/feedback/m/feedbackSubTypeListApiBean.dart';
import 'package:rrworkmanager/page/feedback/v/feedbackDysfunctionPage.dart';
import 'package:rrworkmanager/page/feedback/v/feedbackInfoInputPage.dart';
import 'package:rrworkmanager/page/records/v/feedbackRecordsPage.dart';
import 'package:rrworkmanager/rrworkmanager.dart';
import 'package:rrworkmanager/utils/Global.dart';
import 'package:rrworkmanager/utils/StringUtils.dart';
import 'package:rrworkmanager/common_widget/AppBarWidget.dart';
import 'package:rrworkmanager/page/feedback/vm/feedbackViewModel.dart';

class FeedbackSuggestionsPage extends StatefulWidget {
  const FeedbackSuggestionsPage({Key? key}) : super(key: key);
  @override
  _FeedbackSuggestionsPageState createState() =>
      _FeedbackSuggestionsPageState();
}

class _FeedbackSuggestionsPageState extends State<FeedbackSuggestionsPage> {
  final FeedbackViewModel _model = FeedbackViewModel();

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<FeedbackViewModel>(
      create: (_) => _model,
      child: Scaffold(
        appBar: AppBarUtils(title: trans('feedback_title_feedback_suggestions'))
            .build(context),
        body: _body(context),
        backgroundColor: Global.lightBackGroundColor,
      ),
    );
  }

  Widget _body(BuildContext context) {
    return Consumer<FeedbackViewModel>(builder: (context, model, child) {
      return Container(
        padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
        child: Column(
          children: [
            Column(
              children: List.generate(model.typeList.length, (index) {
                return GestureDetector(
                  onTap: () {
                    if (index == 0) {
                      model.requestWorkOrderSubTypeList(context, "dysfunction",
                          (result) {
                        FeedbackSubTypeListApiBean apiBean = result;
                        if (apiBean.data!.isNotEmpty) {
                          Navigator.push(
                              context,
                              MaterialPageRoute<void>(
                                builder: (BuildContext context) =>
                                    FeedbackDysfunctionPage(
                                  workOrderType: "dysfunction",
                                  subTypes: apiBean.data!,
                                ),
                              ));
                        } else {
                          Navigator.push(
                              context,
                              MaterialPageRoute<void>(
                                builder: (BuildContext context) =>
                                    const FeedbackInfoInputPage(
                                  type: "dysfunction",
                                ),
                              ));
                        }
                      }, ((error, code) {}));
                    } else {
                      Navigator.push(
                          context,
                          MaterialPageRoute<void>(
                            builder: (BuildContext context) =>
                                const FeedbackInfoInputPage(
                              type: "product",
                            ),
                          ));
                    }
                  },
                  child: _itemWidget(model.typeList[index]),
                );
              }),
            ),
            GestureDetector(
              onTap: () {
                Navigator.push(
                    context,
                    MaterialPageRoute<void>(
                      builder: (BuildContext context) =>
                          const FeedbackRecordsPage(),
                    ));
              },
              child: Container(
                margin: EdgeInsets.only(top: 24.w),
                child: Text(
                  trans("feedback_btn_view_feedback"),
                  style: TextStyle(
                      color: RRWorkManagerConfig.submitBtnColor,
                      fontSize: 28.sp),
                ),
              ),
            ),
            const Spacer(),
            Container(
              margin: EdgeInsets.only(bottom: 24.w),
              child: Text(
                trans("feedback_tips_service_telephone"),
                style: TextStyle(color: Global.tipsTextColor, fontSize: 24.sp),
              ),
            )
          ],
        ),
      );
    });
  }

  Widget _itemWidget(String title) {
    return Container(
      height: 88.w,
      margin: EdgeInsets.only(bottom: 24.w),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(8.r)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            margin: EdgeInsets.only(left: 24.w),
            child: Text(
              title,
              style: TextStyle(color: Global.mainTextColor, fontSize: 32.sp),
            ),
          ),
          const Spacer(),
          Container(
            width: 30.w,
            margin: EdgeInsets.only(right: 24.w),
            child: Image.asset(
              "resources/image/icon_right.png",
              package: Global.package,
            ),
          )
        ],
      ),
    );
  }
}
