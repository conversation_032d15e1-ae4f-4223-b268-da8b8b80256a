import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rrworkmanager/common_widget/AppBarWidget.dart';
import 'package:rrworkmanager/page/records/v/feedbackRecordsPage.dart';
import 'package:rrworkmanager/rrworkmanagerConfig.dart';
import 'package:rrworkmanager/rrworkmanagerplugin.dart';
import 'package:rrworkmanager/utils/Global.dart';
import 'package:rrworkmanager/utils/StringUtils.dart';

class FeedbackResultPage extends StatelessWidget {
  //主工单类型
  final String type;

  const FeedbackResultPage({Key? key, required this.type}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    String title = trans("feedback_title_feedback_suggestions");
    if (type == "transaction") {
      title = trans("transaction_title_transaction_complaints");
    }
    return Scaffold(
      appBar: AppBarUtils(title: title).build(context),
      body: _body(context),
    );
  }

  Widget _body(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      child: Column(
        children: [
          Container(
              margin: EdgeInsets.only(top: 193.w),
              width: 112.w,
              child: Image.asset(
                "resources/image/img_success.png",
                package: Global.package,
              )),
          Container(
            margin: EdgeInsets.only(top: 24.w),
            child: Text(
              trans("feedback_tips_submit_completed"),
              textAlign: TextAlign.center,
              style: TextStyle(color: Global.tipsTextColor, fontSize: 32.sp),
            ),
          ),
          GestureDetector(
            onTap: () {
              if (RRWorkManagerPlugin.platform == "Agent") {
                Navigator.popUntil(context,
                    ModalRoute.withName(RRWorkManagerPlugin.sourcesRouteName));
              } else {
                const platform = MethodChannel('nativeApp');
                platform.invokeMethod('backToNative');
              }
            },
            child: Container(
              height: 88.w,
              width: 1.sw - 60.w,
              alignment: Alignment.center,
              margin: EdgeInsets.only(top: 144.w),
              decoration: BoxDecoration(
                  color: RRWorkManagerConfig.submitBtnColor,
                  borderRadius: BorderRadius.circular(8.r)),
              child: Text(
                trans("common_btn_off"),
                style: TextStyle(color: Colors.white, fontSize: 36.sp),
              ),
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: () {
              Navigator.push(
                  context,
                  MaterialPageRoute<void>(
                    builder: (BuildContext context) =>
                        const FeedbackRecordsPage(),
                  ));
            },
            child: Container(
              margin: EdgeInsets.only(bottom: 24.w),
              child: Text(
                trans("feedback_btn_view_feedback"),
                style: TextStyle(
                    color: RRWorkManagerConfig.submitBtnColor, fontSize: 28.sp),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
