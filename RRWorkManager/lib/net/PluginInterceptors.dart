import 'dart:io';

import 'package:dio/dio.dart';
import 'DateEncryption.dart';
import '../rrworkmanagerplugin.dart';

///钱包层面的拦截器
class SmsInterceptors extends InterceptorsWrapper {
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    ///处理data
    String data = response.data as String;
    data = data.replaceFirst(RegExp(r'while\(1\);'), '');
    response.data = data;
    super.onResponse(response, handler);
  }

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    //拿到之前的请求数据
    Map<String, dynamic> data = options.data;

    ///添加相应的数据
    String uuid = RRWorkManagerPlugin.uuid;
    //时间戳
    String timestamp = DateTime.now().microsecondsSinceEpoch.toString();
    data["timeStamp"] = timestamp;
    //随机数
    String random = uuid + timestamp;
    data["random"] = random;
    //设备标识 Android -> 1 ,Ios -> 0
    data["deviceOS"] = Platform.isAndroid ? "1" : "0";
    //platform 平台 iOS Android
    data["platform"] = Platform.isAndroid ? "1" : "0";
    //版本信息
    data["appVersion"] = RRWorkManagerPlugin.version;
    //手机品牌
    data["brand"] = RRWorkManagerPlugin.brand;
    //手机系列
    data["series"] = RRWorkManagerPlugin.series;
    //手机系统版本
    data["systemVersion"] = RRWorkManagerPlugin.systemVersion;
    

    //唯一标识
    data["singleCode"] = uuid;

    //获取加密字符串
    String encryptionStr = await DateEncryption.encode(data);

    //设置上传的数据
    options.data = encryptionStr;
    //设置请求类型
    options.contentType = "application/json; charset=utf-8";

    //添加Header
    Map<String, dynamic> headers = options.headers;
    //当前语言
    String locale =
        RRWorkManagerPlugin.currentLocale.languageCode.toString();
    if (locale == "en") {
      headers["Accept-Language"] = "en-US";
    } else if (locale == "zh") {
      headers["Accept-Language"] = "zh-CN";
    }
    super.onRequest(options, handler);
  }
}
