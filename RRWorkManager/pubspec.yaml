name: rrworkmanager
description: RRWorkManager
version: 0.0.1
homepage:

environment:
  sdk: ">=2.16.2 <3.0.0"
  flutter: ">=2.5.0"

dependencies:
  flutter:
    sdk: flutter
  crypto: ^3.0.2
  encrypt: ^5.0.1
  # 安全存储
  # https://pub.dev/packages/flutter_secure_storage
  # Keychain is used for iOS
  # AES encryption is used for Android. AES secret key is encrypted with RSA and RSA key is stored in KeyStore
  flutter_secure_storage: 4.2.1
  # device_info 获取设备信息 https://pub.dev/packages/device_info/example
  device_info: ^2.0.3
  # 获取应用信息 https://pub.flutter-io.cn/packages/package_info
  package_info: ^2.0.2
  # dio 网络请求 https://pub.dev/packages/dio/example
  dio: ^4.0.1
  # provider  https://github.com/rrousselGit/provider/blob/master/resources/translations/zh-CN/README.md
  provider: ^6.0.1
  # 吐司提示 https://pub.dev/packages/fluttertoast
  fluttertoast: 8.0.8

  # 多语言 https://pub.dev/packages/easy_localization/versions/3.0.0-dev.1
  easy_localization: ^3.0.0

  # 屏幕适配 https://pub.dev/packages/flutter_screenutil
  flutter_screenutil: 5.0.0+2

  # 保存数据 https://pub.dev/packages/shared_preferences
  shared_preferences: ^2.0.8

  # 图片选择 https://pub.dev/packages/image_picker
  image_picker: ^0.8.5

  # 下拉刷新 https://pub.dev/packages/pull_to_refresh
  pull_to_refresh: ^2.0.0

  # 星级评分 https://pub.dev/packages/flutter_rating_bar
  flutter_rating_bar: ^4.0.1

  #通讯录列表
  #https://pub.dev/packages/azlistview
  azlistview: ^2.0.0
  # 汉字转拼音
  lpinyin: ^2.0.0

  # 加载html代码 https://pub.dev/packages/flutter_html
  html_unescape: ^2.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^1.0.0

dependency_overrides:
  win32: ^5.14.0
  # Fix for Flutter 3.32+ compatibility issues
  flutter_layout_grid: ^2.0.8
  path_drawing: ^1.0.1
  # Use a version compatible with current Flutter
  flutter_math_fork: ^0.5.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # This section identifies this Flutter project as a plugin project.
  # The 'pluginClass' and Android 'package' identifiers should not ordinarily
  # be modified. They are used by the tooling to maintain consistency when
  # adding or updating assets for this project.
  plugin:
    platforms:
      android:
        package: com.example.rrworkmanager
        pluginClass: RRWorkManagerPlugin
      ios:
        pluginClass: RRWorkManagerPlugin

  # To add assets to your plugin package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  assets:
    # 多语言的资源
    - resources/locale/
    # 图片资源
    - resources/image/
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your plugin package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
