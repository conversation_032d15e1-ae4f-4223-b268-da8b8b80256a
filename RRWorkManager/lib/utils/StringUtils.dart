// ignore: file_names
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

//翻译
String trans(String key) {

  return Localization.tr(key);
}

class Localization {

  static Locale? locale;

  static Map<String, dynamic>? languageInfo;

  static void initLocalization(Locale locale) async {
    Localization.locale = locale;
    String path = "packages/rrworkmanager/resources/locale/${locale.languageCode}-${locale.countryCode}.json";
    languageInfo = json.decode(await rootBundle.loadString(path));
  }

  static String tr(String key) {
    String value;
    if(languageInfo == null) {
      value = key;
    } else {
      try {
        value = languageInfo![key];
      } catch(e) {
        value = key;
      }
    }
    return value;
  }
}