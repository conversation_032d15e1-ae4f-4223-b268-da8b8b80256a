//
//  RRHistoryListDetailsVC.m
//  xWalletPro_iOS
//
//  Created by <PERSON> on 2/18/20.
//  Copyright © 2020 Shirley. All rights reserved.
//

#import "RRHistoryListDetailsVC.h"
#import "OrderedDictionary.h"
#import "RRWithdrawalStatusView.h"
#import "RRFlutterVC.h"

@interface RRHistoryListDetailsVC ()

@property (nonatomic,strong) UIButton *rightBtn;

@property (nonatomic,strong) UIScrollView *detailView;
@property (nonatomic,strong) UIView *detailConView;
//支付类型View
@property (nonatomic,strong) UIImageView *payTypeImg;
@property (nonatomic,strong) UILabel *payTypeLabel;
@property (nonatomic,strong) UIView *payTypeView;
@property (nonatomic,strong) UILabel *amountLabel;
@property (nonatomic,strong) UILabel *currencyLabel;
@property (nonatomic,strong) UILabel *statusLabel;
//详细信息View
@property (nonatomic,strong) UIImageView *lineImg;
@property (nonatomic,strong) UILabel *tipLabel;
@property (nonatomic,strong) UIView *tipView;
@property (nonatomic,strong) RRWithdrawalStatusView *initiate;
@property (nonatomic,strong) RRWithdrawalStatusView *process;
@property (nonatomic,strong) RRWithdrawalStatusView *success;
@property (nonatomic,strong) UIView *infoView;

//展示的条码视图
@property (nonatomic,strong) UILabel *merTipLabel;
@property (nonatomic,strong) UIImageView *barcodeView;
@property (nonatomic,strong) UILabel *codeTipLabel;
@property (nonatomic,strong) UIButton *barcodeBtn;
@property (nonatomic,strong) UIView *merBarView;

//点击放大后的二维码和条形码视图
@property (nonatomic,strong) UIView *enlargeView;
@property (nonatomic,strong) UIImageView *largeBarcodeView;
@property (nonatomic,strong) UILabel *largeCodeTipLabel;
@property (nonatomic,strong) UIImageView *largeqrcodeView;

@property (nonatomic,strong) UIButton *disCountBtn;
@property (nonatomic,strong) UIView *disCountView;
@property (nonatomic,assign) NSInteger disCountInt;
@property (nonatomic,strong) NSMutableArray *disCountLabArr;
@property (nonatomic,strong) NSMutableDictionary *disCountDic;

@end

@implementation RRHistoryListDetailsVC
{
    //账单详情
    RRHistoryDetailsModel *_detailModel;
    //已排序的账单详情
    OrderedDictionary *_orderedDetailDic;
    //infoView最后一个label
    UILabel *_lastLabel;
    
    //disView最后一个label
    UILabel *_disLastLabel;
    
    //flutter通道
    FlutterMethodChannel *_navigateChannel;
    //flutter通道
    FlutterMethodChannel *_nativeChannel;
}
#pragma mark- 控件懒加载
-(UIScrollView *)detailView
{
    if (!_detailView) {
        _detailView = [[UIScrollView alloc]init];
        _detailView.showsHorizontalScrollIndicator = NO;
        _detailView.showsVerticalScrollIndicator = NO;
        _detailView.pagingEnabled = NO;
        _detailView.clipsToBounds = YES;
        _detailView.bounces = NO;
    }
    return _detailView;
}
-(UIView *)detailConView
{
    if (!_detailConView) {
        _detailConView = [[UIView alloc]init];
    }
    return _detailConView;
}
-(UIImageView *)payTypeImg
{
    if (!_payTypeImg) {
        _payTypeImg = [[UIImageView alloc]init];
    }
    return _payTypeImg;
}
-(UILabel *)payTypeLabel
{
    if (!_payTypeLabel) {
        _payTypeLabel = [[UILabel alloc]init];
        _payTypeLabel.font = FONT(18*FONT_SIZE);
        _payTypeLabel.textColor = MAIN_TEXT_COLOR;
        _payTypeLabel.numberOfLines = 0;
    }
    return _payTypeLabel;
}
-(UIView *)payTypeView
{
    if (!_payTypeView) {
        _payTypeView = [[UIView alloc]init];
        
        [_payTypeView addSubview:self.payTypeImg];
        [self.payTypeImg mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(0);
            make.centerY.mas_equalTo(0);
            make.width.and.height.mas_equalTo(28*AUTO_SIZE);
        }];
        [_payTypeView addSubview:self.payTypeLabel];
        [self.payTypeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(self.payTypeImg.mas_right).offset(15*AUTO_SIZE);
            make.centerY.mas_equalTo(self.payTypeImg.mas_centerY);
            make.right.mas_equalTo(0);
            make.top.mas_equalTo(5*AUTO_SIZE);
            make.bottom.mas_equalTo(-5*AUTO_SIZE);
        }];
    }
    return _payTypeView;
}
-(UILabel *)amountLabel
{
    if (!_amountLabel) {
        _amountLabel = [[UILabel alloc]init];
        _amountLabel.font = MEDIUMFONT(21*FONT_SIZE);
        _amountLabel.textAlignment = NSTextAlignmentCenter;
        _amountLabel.textColor = MAIN_TEXT_COLOR;
    }
    return _amountLabel;
}
-(UILabel *)currencyLabel
{
    if (!_currencyLabel) {
        _currencyLabel = [[UILabel alloc]init];
        _currencyLabel.font = MEDIUMFONT(21*FONT_SIZE);
        _currencyLabel.textColor = MAIN_TEXT_COLOR;
    }
    return _currencyLabel;
}
-(UILabel *)statusLabel
{
    if (!_statusLabel) {
        _statusLabel = [[UILabel alloc]init];
        _statusLabel.font = FONT(18*FONT_SIZE);
        _statusLabel.textColor = INF_LABEL_COLOR;
        _statusLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _statusLabel;
}
-(UIImageView *)lineImg
{
    if (!_lineImg) {
        _lineImg = [[UIImageView alloc]init];
        _lineImg.image = [UIImage imageNamed:@"line_heng2"];
    }
    return _lineImg;
}
-(UILabel *)tipLabel
{
    if (!_tipLabel) {
        _tipLabel = [[UILabel alloc]init];
        _tipLabel.text = TRANSLATE(@"history_label_current_state");
        _tipLabel.font = FONT(11*FONT_SIZE);
        _tipLabel.textColor = INF_LABEL_COLOR;
    }
    return _tipLabel;
}
-(UIView *)tipView
{
    if (!_tipView) {
        _tipView = [[UIView alloc]init];
        _tipView.backgroundColor = UIColor.whiteColor;
        
        [_tipView addSubview:self.tipLabel];
        [self.tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.and.bottom.mas_equalTo(0);
            make.left.mas_equalTo(8*AUTO_SIZE);
            make.right.mas_equalTo(-8*AUTO_SIZE);
        }];
    }
    return _tipView;
}
-(RRWithdrawalStatusView *)initiate
{
    if (!_initiate) {
        _initiate = [[RRWithdrawalStatusView alloc]init];
    }
    return _initiate;
}
-(RRWithdrawalStatusView *)process
{
    if (!_process) {
        _process = [[RRWithdrawalStatusView alloc]init];
    }
    return _process;
}
-(RRWithdrawalStatusView *)success
{
    if (!_success) {
        _success = [[RRWithdrawalStatusView alloc]init];
    }
    return _success;
}
-(UIView *)disCountView{
    if (!_disCountView) {
        _disCountView = [[UIView alloc]init];
        _disCountView.backgroundColor = VC_BG_COLOR;
        _disCountView.layer.cornerRadius = 5.0;
        _disCountView.hidden = YES;
    }
    return _disCountView;
}
-(UIView *)infoView
{
    if (!_infoView) {
        _infoView = [[UIView alloc]init];
        _infoView.backgroundColor = VIEW_COLOR;
        _infoView.layer.cornerRadius = 5.0;
        
        [self infoViewSubview];
//        if ([@"20" isEqualToString:tradeType]) {
//            //当前为提现的详情
//            [_infoView addSubview:self.lineImg];
//            [_infoView addSubview:self.tipView];
//            [_infoView addSubview:self.initiate];
//            [_infoView addSubview:self.process];
//            [_infoView addSubview:self.success];
//            [self.lineImg mas_makeConstraints:^(MASConstraintMaker *make) {
//                make.left.mas_equalTo(10*AUTO_SIZE);
//                make.right.mas_equalTo(-10*AUTO_SIZE);
//                make.top.mas_equalTo(_lastLabel.mas_bottom).offset(27.5*AUTO_SIZE);
//                make.height.mas_equalTo(1*AUTO_SIZE);
//            }];
//            [self.tipView mas_makeConstraints:^(MASConstraintMaker *make) {
//                make.centerX.mas_equalTo(self.lineImg.mas_centerX);
//                make.centerY.mas_equalTo(self.lineImg.mas_centerY);
//            }];
//            [self.initiate mas_makeConstraints:^(MASConstraintMaker *make) {
//                make.left.mas_equalTo(0);
//                make.right.mas_equalTo(0);
//                make.top.mas_equalTo(self.lineImg.mas_bottom).offset(16.5*AUTO_SIZE);
//                make.height.mas_equalTo(75.5*AUTO_SIZE);
//            }];
//            [self.process mas_makeConstraints:^(MASConstraintMaker *make) {
//                make.left.mas_equalTo(self.initiate);
//                make.right.mas_equalTo(0);
//                make.top.mas_equalTo(self.initiate.mas_bottom).offset(-5.5*AUTO_SIZE);
//                make.height.mas_equalTo(70*AUTO_SIZE);
//            }];
//            [self.success mas_makeConstraints:^(MASConstraintMaker *make) {
//                make.left.mas_equalTo(self.initiate);
//                make.right.mas_equalTo(0);
//                make.top.mas_equalTo(self.process.mas_bottom).offset(-5.5*AUTO_SIZE);
//                make.height.mas_equalTo(48*AUTO_SIZE);
//                make.bottom.mas_equalTo(-20*AUTO_SIZE);
//            }];
//        }
    }
    return _infoView;
}
- (void)infoViewSubview {
    NSString *outOrderNo = [RRTools nvl:_detailModel.outOrderNo];
    NSString *terminalNo = [RRTools nvl:_detailModel.terminalNo];
    NSString *tradeType = [RRTools nvl:_detailModel.tradeType];
    
    NSArray *keyArr = [_orderedDetailDic allKeys];
    for (int i = 0; i < keyArr.count; i++) {
        UILabel *keyLabel = [[UILabel alloc]init];
        keyLabel.text = keyArr[i];
        keyLabel.font = FONT(11*FONT_SIZE);
        keyLabel.textColor = INF_LABEL_COLOR;
        [self.infoView addSubview:keyLabel];
        [self.disCountLabArr addObject:keyLabel];
        [keyLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(10);
            if([keyArr[i] isEqualToString:TRANSLATE(@"history_label_discount_amount")]){
                make.right.mas_equalTo(-30);
            } else {
                make.right.mas_equalTo(-10);
            }
            if (_lastLabel) {
                if (i == self.disCountInt + 1 && self.disCountView.hidden == NO) {
                    make.top.mas_equalTo(self.disCountView.mas_bottom).offset(12*AUTO_SIZE);
                } else {
                    make.top.mas_equalTo(_lastLabel.mas_bottom).offset(12*AUTO_SIZE);
                }
                
            }else{
                make.top.mas_equalTo(20*AUTO_SIZE);
            }
        }];
        
        UILabel *valueLabel = [[UILabel alloc]init];
        valueLabel.text = _orderedDetailDic[keyArr[i]];
        valueLabel.font = FONT(16*FONT_SIZE);
        valueLabel.textColor = MAIN_TEXT_COLOR;
        valueLabel.numberOfLines = 0;
        [self.infoView addSubview:valueLabel];
        [valueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(10);
            if([keyArr[i] isEqualToString:TRANSLATE(@"history_label_discount_amount")]){
                make.right.mas_equalTo(-30);
            } else {
                make.right.mas_equalTo(-10);
            }
            make.top.mas_equalTo(keyLabel.mas_bottom).offset(4*AUTO_SIZE);
            if (i == keyArr.count - 1 && terminalNo.length <= 0) {
                make.bottom.mas_equalTo(_infoView.mas_bottom).offset(-20*AUTO_SIZE);
            }
        }];
        _lastLabel = valueLabel;
        
        if([keyArr[i] isEqualToString:TRANSLATE(@"history_label_discount_amount")]){
            self.disCountInt = i;
           self.disCountBtn  = [[UIButton alloc]init];
            [self.disCountBtn setBackgroundColor:UIColor.whiteColor];
            [self.disCountBtn setBackgroundImage:[UIImage imageNamed:@"icon_sanjiao_orange"] forState:UIControlStateNormal];
            [self.disCountBtn addTarget:self action:@selector(openDiscountView) forControlEvents:UIControlEventTouchUpInside];
            [self.infoView addSubview:self.disCountBtn];
            [self.disCountBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.size.mas_equalTo(CGSizeMake(8*AUTO_SIZE, 8*AUTO_SIZE));
                make.right.mas_equalTo(-10);
                make.centerY.mas_equalTo(keyLabel.mas_centerY);
            }];
            [self.disCountBtn setEnlargeEdgeWithTop:10 right:10 bottom:10 left:10];

            if(self.disCountView.hidden == NO){
                [self.infoView addSubview: self.disCountView];
                NSArray *keyArrs = [self.disCountDic allKeys];
                for (int i = 0; i < keyArrs.count; i++) {
                    UILabel *disKeyLabel = [[UILabel alloc]init];
                    disKeyLabel.text = keyArrs[i];
                    disKeyLabel.font = FONT(13*FONT_SIZE);
                    disKeyLabel.textColor = INF_LABEL_COLOR;
                    [self.disCountView addSubview:disKeyLabel];
                    [disKeyLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                        make.left.mas_equalTo(10);
                        make.height.mas_equalTo(16 *AUTO_SIZE);
                        if (_disLastLabel) {
                            make.top.mas_equalTo(_disLastLabel.mas_bottom).offset(12*AUTO_SIZE);
                        }else{
                            make.top.mas_equalTo(12*AUTO_SIZE);
                        }
                    }];
                    UILabel *disValueLabel = [[UILabel alloc]init];
                    disValueLabel.text = self.disCountDic[keyArrs[i]];
                    disValueLabel.font = FONT(13*FONT_SIZE);
                    disValueLabel.textColor = MAIN_TEXT_COLOR;
                    disValueLabel.numberOfLines = 0;
                    disValueLabel.textAlignment = NSTextAlignmentRight;
                    [self.disCountView addSubview:disValueLabel];
                    [disValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                        make.left.mas_equalTo(disKeyLabel.mas_right);
                        make.right.mas_equalTo(-10);
                        make.height.mas_equalTo(16 *AUTO_SIZE);
                        make.top.mas_equalTo(disKeyLabel);
                    }];
                    _disLastLabel = disValueLabel;
                }
                [self.disCountView mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.left.mas_equalTo(10);
                    make.right.mas_equalTo(-10);
                    make.top.mas_equalTo(_lastLabel.mas_bottom).offset(12*AUTO_SIZE);
                    make.height.mas_equalTo((12 + 28 * keyArrs.count) *AUTO_SIZE );
                }];
            }
            
        }
    }
    
    if (outOrderNo.length > 0) {
        if (terminalNo.length > 0) {
            [_infoView addSubview:self.merBarView];
            [self.merBarView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.left.mas_equalTo(10*AUTO_SIZE);
                make.right.mas_equalTo(-10*AUTO_SIZE);
                make.top.mas_equalTo(_lastLabel.mas_bottom).offset(10*AUTO_SIZE);
                make.bottom.mas_equalTo(-10*AUTO_SIZE);
            }];
        }
    }
}
-(UILabel *)merTipLabel {
    if (!_merTipLabel) {
        _merTipLabel = [[UILabel alloc]init];
        _merTipLabel.text = TRANSLATE(@"history_label_mer_orderno");
        _merTipLabel.font = FONT(11*FONT_SIZE);
        _merTipLabel.textColor = INF_LABEL_COLOR;
    }
    return _merTipLabel;
}
-(UIImageView *)barcodeView {
    if (!_barcodeView) {
        _barcodeView = [[UIImageView alloc]init];
    }
    return _barcodeView;
}
-(UILabel *)codeTipLabel {
    if (!_codeTipLabel) {
        _codeTipLabel = [[UILabel alloc]init];
        _codeTipLabel.text = [NSString stringWithFormat:@"%@ %@",[RRTools merchantOrderNoMaskWithOrderNo:_detailModel.outOrderNo],TRANSLATE(@"history_label_click_tip")];
        _codeTipLabel.textColor = MAIN_TEXT_COLOR;
        _codeTipLabel.textAlignment = NSTextAlignmentCenter;
        _codeTipLabel.font = FONT(10*FONT_SIZE);
    }
    return _codeTipLabel;
}
-(UIButton *)barcodeBtn {
    if (!_barcodeBtn) {
        _barcodeBtn = [[UIButton alloc]init];
        [_barcodeBtn addTarget:self action:@selector(barcodeBtnClick) forControlEvents:UIControlEventTouchUpInside];
    }
    return _barcodeBtn;
}
-(UIView *)merBarView {
    if (!_merBarView) {
        _merBarView = [[UIView alloc]init];
        
        [_merBarView addSubview:self.merTipLabel];
        [_merBarView addSubview:self.barcodeView];
        [_merBarView addSubview:self.codeTipLabel];
        [_merBarView addSubview:self.barcodeBtn];
        [self.merTipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(0);
            make.top.mas_equalTo(0);
            make.height.mas_equalTo(15*AUTO_SIZE);
        }];
        [self.barcodeView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.merTipLabel.mas_bottom).offset(10*AUTO_SIZE);
            make.height.mas_equalTo(55.5*AUTO_SIZE);
            make.width.mas_equalTo(197.5*AUTO_SIZE);
            make.centerX.mas_equalTo(_merBarView.mas_centerX);
        }];
        [self.codeTipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.barcodeView.mas_bottom).offset(4*AUTO_SIZE);
            make.height.mas_equalTo(14*AUTO_SIZE);
            make.left.mas_greaterThanOrEqualTo(0);
            make.centerX.mas_equalTo(_merBarView.mas_centerX);
            make.bottom.mas_equalTo(0);
        }];
        [self.barcodeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.barcodeView.mas_top);
            make.bottom.mas_equalTo(self.codeTipLabel.mas_bottom);
            make.left.and.right.mas_equalTo(self.codeTipLabel);
        }];
        self.barcodeView.image = [RRTools getBarCodeImageWith:_detailModel.outOrderNo withSize:CGSizeMake(197.5*AUTO_SIZE, 55.5*AUTO_SIZE)];
    }
    return _merBarView;
}

-(UIView *)enlargeView {
    if (!_enlargeView) {
        _enlargeView = [[UIView alloc]init];
        _enlargeView.backgroundColor = VIEW_FLOAT_COLOR;
        _enlargeView.hidden = YES;
        
        UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(enlargeViewClick)];
        [_enlargeView addGestureRecognizer:tapGesture];
        
        [_enlargeView addSubview:self.largeBarcodeView];
        [_enlargeView addSubview:self.largeCodeTipLabel];
        [_enlargeView addSubview:self.largeqrcodeView];
        [self.largeBarcodeView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(47.5*AUTO_SIZE);
            make.top.mas_equalTo(175*AUTO_SIZE);
            make.height.mas_equalTo(85*AUTO_SIZE);
            make.width.mas_equalTo(305*AUTO_SIZE);
        }];
        [self.largeCodeTipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(self.largeBarcodeView.mas_centerY);
            make.height.mas_equalTo(14*AUTO_SIZE);
            make.width.mas_equalTo(305*AUTO_SIZE);
            make.right.mas_equalTo(self.largeBarcodeView.mas_right).offset(-62.5*AUTO_SIZE);
        }];
        [self.largeqrcodeView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(405*AUTO_SIZE);
            make.centerX.mas_equalTo(_enlargeView.mas_centerX);
            make.height.mas_equalTo(144*AUTO_SIZE);
            make.width.mas_equalTo(144*AUTO_SIZE);
        }];
        self.largeBarcodeView.image = [RRTools getBarCodeImageWith:_detailModel.outOrderNo withSize:CGSizeMake(305*AUTO_SIZE, 85*AUTO_SIZE)];
        self.largeCodeTipLabel.text = [RRTools nvl:_detailModel.outOrderNo];
        self.largeqrcodeView.image = [RRTools getQrImageWith:_detailModel.outOrderNo withSize:144*AUTO_SIZE];
        self.largeBarcodeView.transform = CGAffineTransformMakeRotation(M_PI_2);
        self.largeCodeTipLabel.transform = CGAffineTransformMakeRotation(M_PI_2);
        self.largeqrcodeView.transform = CGAffineTransformMakeRotation(M_PI_2);
    }
    return _enlargeView;
}
-(UIImageView *)largeBarcodeView {
    if (!_largeBarcodeView) {
        _largeBarcodeView = [[UIImageView alloc]init];
        _largeBarcodeView.backgroundColor = UIColor.redColor;
    }
    return _largeBarcodeView;
}
-(UILabel *)largeCodeTipLabel {
    if (!_largeCodeTipLabel) {
        _largeCodeTipLabel = [[UILabel alloc]init];
        _largeCodeTipLabel.text = @"757557475754478547854";
        _largeCodeTipLabel.textColor = MAIN_TEXT_COLOR;
        _largeCodeTipLabel.font = FONT(10*FONT_SIZE);
        _largeCodeTipLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _largeCodeTipLabel;
}
-(UIImageView *)largeqrcodeView {
    if (!_largeqrcodeView) {
        _largeqrcodeView = [[UIImageView alloc]init];
        _largeqrcodeView.backgroundColor = UIColor.greenColor;
    }
    return _largeqrcodeView;
}

-(UIButton *)rightBtn
{
    if (!_rightBtn) {
        _rightBtn = [[UIButton alloc]init];
        [_rightBtn setImage:[UIImage imageNamed:@"icon_feedback"] forState:UIControlStateNormal];
        [_rightBtn addTarget:self action:@selector(helpBtnClick) forControlEvents:UIControlEventTouchUpInside];
    }
    return _rightBtn;
}

#pragma mark- privateMethod

-(void)helpBtnClick{
    if(!_detailModel)return;;
    /*
    "tradeInfo":{
      "tradeType":"20",
      "tradeTypeDesc":"Withdrawal",
      "tradeAmt":"-9.00",
      "currency":"USD",
      "orderNo":"1624138515728646151",
      "tradeTime":"2023-09-21 11:23:23",
      "status":"Success"
    }
     */
    NSMutableDictionary *tradeInfo = [NSMutableDictionary dictionary];
    if ([_detailModel.tradeType isEqualToString:@"11"]) {
        if ([_detailModel.orderStatus isEqualToString:@"4"] || [_detailModel.orderStatus isEqualToString:@"5"]) {
            [tradeInfo setObject:@"12" forKey:@"tradeType"];
        } else {
            [tradeInfo setObject:@"11" forKey:@"tradeType"];
        }
        
    } else {
        [tradeInfo setObject:_detailModel.tradeType forKey:@"tradeType"];
    }
    [tradeInfo setObject:self.payTypeLabel.text forKey:@"tradeTypeDesc"];
    NSString *orderIncomeFlag = [RRTools nvl:_detailModel.orderIncomeFlag];
    NSString *tradeAmt = [NSString stringWithFormat:@"%@%@",orderIncomeFlag,[RRTools strmethodComma:[RRTools formatAmount:[RRTools nvl:_detailModel.tradeAmt]]]];
    [tradeInfo setObject:tradeAmt forKey:@"tradeAmt"];
    [tradeInfo setObject:[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY] forKey:@"currency"];
    [tradeInfo setObject:self.model.orderId forKey:@"orderNo"];
    [tradeInfo setObject:self.model.tradeTime forKey:@"tradeTime"];
    [tradeInfo setObject:self.statusLabel.text forKey:@"status"];
    
    RRUserInfo *userInf = [RRUserInfo objectForKey:@"customerDic"];
    NSString *userId = [RRTools nvl:userInf.userId];
    NSString *userName = [RRTools nvl:userInf.realName];
    NSString *token = [RRTools strForKey:@"Authorization"];
    NSMutableDictionary *dataDic = [NSMutableDictionary dictionary];
    [dataDic setObject:@"transaction" forKey:@"routeName"];
    [dataDic setObject:@"User" forKey:@"platform"];
    [dataDic setObject:orderSDKServer forKey:@"baseUrl"];
//    [dataDic setObject:@{@"submitBtnColor":@"0xF3881E"} forKey:@"uiStyle"];
    [dataDic setObject:[NSBundle currentLanguage] forKey:@"language"];
    [dataDic setObject:@{@"userId":userId, @"userName":userName, @"token":token, @"tradeInfo":tradeInfo} forKey:@"params"];
    NSData *data=[NSJSONSerialization dataWithJSONObject:dataDic options:NSJSONWritingPrettyPrinted error:nil];
    NSString *str=[[NSString alloc]initWithData:data encoding:NSUTF8StringEncoding];
    NSLog(@"JsonStr:%@",str);
    RRFlutterVC* flutterVC = [[RRFlutterVC alloc] initWithProject:nil initialRoute:str nibName:nil bundle:nil];
    flutterVC.view.backgroundColor = [UIColor whiteColor];
    //如果使用了插件
    [GeneratedPluginRegistrant registerWithRegistry:flutterVC];
    [self.navigationController pushViewController:flutterVC animated:YES];
    __weak typeof(self) weakSelf = self;
    _nativeChannel = [FlutterMethodChannel methodChannelWithName:@"nativeApp" binaryMessenger:flutterVC.binaryMessenger];
    [_nativeChannel setMethodCallHandler:^(FlutterMethodCall* call, FlutterResult result) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
       if ([call.method isEqualToString:@"backToNative"]) {
            //从flutter页面回到原生页面
            [RRTools getMainQueueDo:^{
                NSLog(@"%@-pop",[RRTools getCurrentViewController]);
                [[RRTools getCurrentViewController].navigationController popViewControllerAnimated:YES];
            }];
        } else {
            result(FlutterMethodNotImplemented);
        }
    }];
    ///此处bundle为旧名称 同步修改SDKname可替换 不要单独修改
    _navigateChannel = [FlutterMethodChannel methodChannelWithName:@"com.cpkj.xwalletPro" binaryMessenger:flutterVC.binaryMessenger];
    [_navigateChannel setMethodCallHandler:^(FlutterMethodCall* call, FlutterResult result) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        if ([call.method isEqualToString:@"backToNative"]) {
            //从flutter页面回到原生页面
            [RRTools getMainQueueDo:^{
                NSLog(@"%@-pop",[RRTools getCurrentViewController]);
                [[RRTools getCurrentViewController].navigationController popViewControllerAnimated:YES];
            }];
        } else if([@"getCurrentLanguage" isEqualToString:call.method]){
            //获取当前的语言为中文，英文，或其他语言
            NSString *currentLanguage = [NSBundle currentLanguage];
            if ([currentLanguage hasPrefix:@"zh"]) {
                currentLanguage = @"zh-Hans";
            }else if([currentLanguage hasPrefix:@"ru"]){
                currentLanguage = @"ru";
            }else if([currentLanguage hasPrefix:@"sn"]){
                currentLanguage = @"sn";
            }else if([currentLanguage hasPrefix:@"nr"]){
                currentLanguage = @"nr";
            }else {
                currentLanguage = @"en";
            }
            result(currentLanguage);
        } else if ([call.method isEqualToString:@"canPopFlutterPage"]) {
            UIViewController *vc = [RRTools getCurrentViewController];
            if ([vc isKindOfClass:[RRFlutterVC class]]) {
                ((RRFlutterVC *)vc).canPop = [call.arguments boolValue];
            }
        } else {
            result(FlutterMethodNotImplemented);
        }
    }];
}

//控制展示营销view
-(void)openDiscountView {
    self.disCountView.hidden = !self.disCountView.hidden;
    for (UIView *view in self.infoView.subviews) {
        [view removeFromSuperview];
    }
    for (UIView *view in self.disCountView.subviews) {
        [view removeFromSuperview];
    }
    _lastLabel = nil;
    _disLastLabel = nil;
    self.disCountInt = -2;
    [self infoViewSubview];
    [UIView animateWithDuration:0.2 animations:^{
        if (self.disCountView.hidden == NO) {
            self.disCountBtn.transform = CGAffineTransformMakeRotation(M_PI * 0.999);
        } else {
            self.disCountBtn.transform = CGAffineTransformIdentity;
        }
    }];
}
-(void)barcodeBtnClick {
    [RRTools getMainQueueDo:^{
        self.enlargeView.hidden = NO;
    }];
}
-(void)enlargeViewClick {
    [RRTools getMainQueueDo:^{
        self.enlargeView.hidden = YES;
    }];
}
-(void)requestOrderDetails
{
    _detailModel = [[RRHistoryDetailsModel alloc]init];
    RRHUDView *hud = [RRHUDView defaultHUDViewForWindow];
    [TOPWINDOW addSubview:hud];
    [hud show];
    NSMutableDictionary *jsonDic = [NSMutableDictionary dictionary];
    [jsonDic setObject:self.model.orderId forKey:@"orderId"];
    [jsonDic setObject:self.model.tradeType forKey:@"tradeType"];
    [RRHttpRequest postSend:ORDER_DETAIL urlString:nil jsonDic:jsonDic success:^(RRNetworkModel *model) {
        [hud hide];
        if ([RRTools isSuccess:model]) {
            self->_detailModel = [RRHistoryDetailsModel mj_objectWithKeyValues:model.data];
            [RRTools getMainQueueDo:^{
                [self processData];
            }];
        }
    } failure:^(NSError *error) {
        [hud hide];
    } time:60 encrypt:NO];
}
-(void)processData
{
    _orderedDetailDic = [OrderedDictionary new];
    //    00:TOP UP   10:Transfer   20:Withdrawal  30:Pay  40:AA 50:Refund
    NSString *tradeType = _detailModel.tradeType;
    if ([@"00" isEqualToString:tradeType]) {//充值
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.payMethod replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_payment_method")];
        NSString *account = [NSString stringWithFormat:@"%@%@", [RRTools nvl:_detailModel.orderCurrType], TRANSLATE(@"topup_label_account")];
        NSString *paymentAccount = [NSString stringWithFormat:@"%@(%@)", _detailModel.bankName, _detailModel.cardNo];
        [_orderedDetailDic setObject:account forKey:TRANSLATE(@"common_tab_account")];
        [_orderedDetailDic setObject: paymentAccount forKey:TRANSLATE(@"history_label_payment_account")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.tradeTime replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_date_time")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.orderId replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_no")];
        if ([@"2" isEqualToString:_detailModel.orderStatus]) {
            [_orderedDetailDic setObject:[RRTools nvl:_detailModel.resultRemark replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_failed_reason")];
        }
    }else if ([@"10" isEqualToString:tradeType]) {//转账
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.payMethod replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_payment_method")];
        if([@"-" isEqualToString:_detailModel.orderIncomeFlag]){//收款人
            [_orderedDetailDic setObject:[RRTools nvl:_detailModel.recMobile] forKey:TRANSLATE(@"common_label_payee_mobile_no")];
            [_orderedDetailDic setObject:[RRTools nvl:_detailModel.name replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"balance_label_Payee_name")];
        }else{//付款人
            if(![_detailModel.payCstType isEqualToString:@"0"]) {
                [_orderedDetailDic setObject:[RRTools nvl:_detailModel.payMobile] forKey:TRANSLATE(@"common_label_payer_mobile_no")];
                [_orderedDetailDic setObject:[RRTools nvl:_detailModel.name replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"balance_label_Payer_name")];
            }else {
                [_orderedDetailDic setObject:[RRTools nvl:_detailModel.name replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"balance_label_Payer_name")];
            }
        }
        if(![_detailModel.payCstType isEqualToString:@"0"]) {
            //添加手续费字段，根据用户是收款人和付款人来判断显示的手续费
            NSString *feeAmt;
            if([@"-" isEqualToString:_detailModel.orderIncomeFlag]){//付款人
                feeAmt = [RRTools nvl:_detailModel.feeAmt];
                if (feeAmt.doubleValue > 0) {
                    feeAmt = [NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:feeAmt]]];
                } else {
                    feeAmt = [NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],@"0.00"];
                }
                [_orderedDetailDic setObject:feeAmt forKey:TRANSLATE(@"common_label_fee")];
            }
            //添加手续费字段，根据用户是收款人和付款人来判断显示的手续费
            NSString *taxAmt;
            if([@"-" isEqualToString:_detailModel.orderIncomeFlag]){//付款人
                taxAmt = [RRTools nvl:_detailModel.taxAmt];
                if (taxAmt.doubleValue > 0) {
                    taxAmt = [NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:taxAmt]]];
                } else {
                    taxAmt = [NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],@"0.00"];
                }
                [_orderedDetailDic setObject:taxAmt forKey:TRANSLATE(@"common_label_tax")];
            }
        }
        
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.tradeTime] forKey:TRANSLATE(@"history_label_date_time")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.orderId replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_no")];
        NSString *remark = [RRTools nvl:_detailModel.remark];
        if (remark.length>0) {
            [_orderedDetailDic setObject:remark forKey:TRANSLATE(@"common_label_remark")];
        }
    }else if ([@"20" isEqualToString:tradeType]) {//提现
        NSString *account = [NSString stringWithFormat:@"%@%@", [RRTools nvl:_detailModel.orderCurrType], TRANSLATE(@"topup_label_account")];
        NSString *bankAccount = [NSString stringWithFormat:@"%@(%@)", _detailModel.bankName, _detailModel.cardNo];
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.payMethod replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_payment_method")];
        [_orderedDetailDic setObject:bankAccount forKey:TRANSLATE(@"withdrawal_label_bank_account")];
//        [_orderedDetailDic setObject: account forKey:TRANSLATE(@"history_label_payment_account")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.createTime replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_date_time")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.orderId replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_no")];
        if ([@"40" isEqualToString:_detailModel.orderStatus]) {
            [_orderedDetailDic setObject: [RRTools nvl:_detailModel.failureReason replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_failed_reason")];
        }
    }else if ([@"30" isEqualToString:tradeType]) {//支付
        if([_detailModel.paymentProduct isEqualToString:@"10"]){
            NSString *businessType = [RRTools nvl:_detailModel.businessType];
            if ([businessType isEqualToString:@"0"]) {
                [_orderedDetailDic setObject:TRANSLATE(@"history_label_loan_repayment") forKey:TRANSLATE(@"history_label_business_type")];
            } else if ([businessType isEqualToString:@"1"]) {
                [_orderedDetailDic setObject:TRANSLATE(@"history_label_international_remittance_out") forKey:TRANSLATE(@"history_label_business_type")];
            }
        }
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.name replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_merchant_name")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.remark replace:TRANSLATE(@"history_label_commodity")] forKey:TRANSLATE(@"history_label_commodity")];
        //营销优惠费
        NSString *discountAmt = [RRTools nvl:_detailModel.discountAmt];
        if (discountAmt.doubleValue > 0) {
            [_orderedDetailDic setObject:[NSString stringWithFormat:@"%@ -%@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:discountAmt]]] forKey:TRANSLATE(@"history_label_discount_amount")];
        }
        //支付手续费
        NSString *payFeeAmt = [RRTools nvl:_detailModel.payFeeAmt];
        if (payFeeAmt.doubleValue > 0) {
            [_orderedDetailDic setObject:[NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:payFeeAmt]]] forKey:TRANSLATE(@"common_label_fee")];
        }
        //添加手续费字段，根据用户是收款人和付款人来判断显示的手续费
        NSString *taxAmt = [RRTools nvl:_detailModel.taxAmt];
        if (taxAmt.doubleValue > 0) {
            taxAmt = [NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:taxAmt]]];
            [_orderedDetailDic setObject:taxAmt forKey:TRANSLATE(@"common_label_tax")];
        }
        NSString *walletAccount = [RRTools nvl:_detailModel.actualAmt];
        NSString *cashbackAmt = [RRTools nvl:_detailModel.cashbackAmt];
        if (walletAccount.doubleValue > 0 && (discountAmt.doubleValue > 0 || cashbackAmt.doubleValue > 0)) {
            walletAccount = [NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:walletAccount]]];
            if ([@"03" isEqualToString:_detailModel.paymentType]) {
                [_orderedDetailDic setObject:walletAccount forKey:TRANSLATE(@"history_label_bank_accoun")];
            } else {
                [_orderedDetailDic setObject:walletAccount forKey:TRANSLATE(@"history_label_wallet_accoun")];
            }
        }
        
        if (cashbackAmt.doubleValue > 0) {
            cashbackAmt = [NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:cashbackAmt]]];
            [_orderedDetailDic setObject:cashbackAmt forKey:TRANSLATE(@"history_label_cashBack_account")];
        }
        if([[RRTools nvl:_detailModel.discountAmt] doubleValue] > 0){
            self.disCountDic = [[NSMutableDictionary alloc] init];
            
            NSString *couponAmt = [RRTools nvl:_detailModel.couponAmt];
            if (couponAmt.doubleValue > 0) {
                couponAmt = [NSString stringWithFormat:@"%@ -%@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:couponAmt]]];
                [self.disCountDic setObject:couponAmt forKey:TRANSLATE(@"scan_label_coupon")];
            }
            NSString *membershipPointAmt = [RRTools nvl:_detailModel.membershipPointAmt];
            if (membershipPointAmt.doubleValue > 0) {
                membershipPointAmt = [NSString stringWithFormat:@"%@ -%@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:membershipPointAmt]]];
                [self.disCountDic setObject:membershipPointAmt forKey:TRANSLATE(@"scan_label_membership_points")];
            }
            NSString *membershipBonusAmt = [RRTools nvl:_detailModel.membershipBonusAmt];
            if (membershipBonusAmt.doubleValue > 0) {
                membershipBonusAmt = [NSString stringWithFormat:@"%@ -%@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:membershipBonusAmt]]];
                [self.disCountDic setObject:membershipBonusAmt forKey:TRANSLATE(@"scan_label_membership_bonus")];
            }
        }
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.payMethod replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_payment_method")];
        if(![RRTools isEmpty:_detailModel.transactionMode]){
            [_orderedDetailDic setObject:[RRTools nvl:_detailModel.transactionMode replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_mode")];
        }
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.tradeTime replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_date_time")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.orderId replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_no")];
        if([_detailModel.paymentProduct isEqualToString:@"06"] || [_detailModel.paymentProduct isEqualToString:@"04"]||[_detailModel.paymentProduct isEqualToString:@"00"] ||[_detailModel.paymentProduct isEqualToString:@"08"] ||[_detailModel.paymentProduct isEqualToString:@"09"] || [_detailModel.paymentProduct isEqualToString:@"10"] ){
            [_orderedDetailDic setObject:[RRTools nvl:_detailModel.outOrderNo replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_mer_orderno")];
        }
    }else if ([@"34" isEqualToString:tradeType]) {//代收代付
        NSString *businessType = [RRTools nvl:_detailModel.businessType];
        if ([businessType isEqualToString:@"0"]) {
            [_orderedDetailDic setObject:TRANSLATE(@"history_label_loan_disbursement") forKey:TRANSLATE(@"history_label_business_type")];
        } else if ([businessType isEqualToString:@"1"]) {
            [_orderedDetailDic setObject:TRANSLATE(@"history_label_international_remittance_in") forKey:TRANSLATE(@"history_label_business_type")];
        }
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.name replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_merchant_name")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.commodityRemark replace:TRANSLATE(@"history_label_commodity")] forKey:TRANSLATE(@"history_label_commodity")];
        if(![RRTools isEmpty:_detailModel.transactionMode]){
            [_orderedDetailDic setObject:[RRTools nvl:_detailModel.transactionMode replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_mode")];
        }
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.tradeTime replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_date_time")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.orderId replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_no")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.outOrderNo replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_mer_orderno")];
    }else if ([@"31" isEqualToString:tradeType]) {//缴费
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.billTypeDesc replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_biller_type")];
        if ([_detailModel.billType isEqualToString:@"01"]) {//市政
           [_orderedDetailDic setObject: [RRTools nvl:_detailModel.billAccount replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_city_harare")];
        } else if ([_detailModel.billType isEqualToString:@"02"]) {//电费
            [_orderedDetailDic setObject: [RRTools nvl:_detailModel.billAccount replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_meter_number")];
            [_orderedDetailDic setObject: [RRTools nvl:_detailModel.meterName replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"personal_label_user_name")];
            //营销优惠费
            NSString *discountAmt = [RRTools nvl:_detailModel.discountAmt];
            if (discountAmt.doubleValue > 0) {
                [_orderedDetailDic setObject:[NSString stringWithFormat:@"%@ -%@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:discountAmt]]] forKey:TRANSLATE(@"history_label_discount_amount")];
            }
            
            if([[RRTools nvl:_detailModel.discountAmt] doubleValue] > 0){
                self.disCountDic = [[NSMutableDictionary alloc] init];
                
                NSString *couponAmt = [RRTools nvl:_detailModel.couponAmt];
                if (couponAmt.doubleValue > 0) {
                    couponAmt = [NSString stringWithFormat:@"%@ -%@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:couponAmt]]];
                    [self.disCountDic setObject:couponAmt forKey:TRANSLATE(@"scan_label_coupon")];
                }
                NSString *membershipPointAmt = [RRTools nvl:_detailModel.membershipPointAmt];
                if (membershipPointAmt.doubleValue > 0) {
                    membershipPointAmt = [NSString stringWithFormat:@"%@ -%@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:membershipPointAmt]]];
                    [self.disCountDic setObject:membershipPointAmt forKey:TRANSLATE(@"scan_label_membership_points")];
                }
                NSString *membershipBonusAmt = [RRTools nvl:_detailModel.membershipBonusAmt];
                if (membershipBonusAmt.doubleValue > 0) {
                    membershipBonusAmt = [NSString stringWithFormat:@"%@ -%@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:membershipBonusAmt]]];
                    [self.disCountDic setObject:membershipBonusAmt forKey:TRANSLATE(@"scan_label_membership_bonus")];
                }
            }
        } else if ([_detailModel.billType isEqualToString:@"03"]) {//学费
            [_orderedDetailDic setObject: [RRTools nvl:_detailModel.billerCode replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_biller_code")];
            [_orderedDetailDic setObject: [RRTools nvl:_detailModel.billerName replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"bill_title_biller_school_name")];
            NSString *semester = @"";
            if ([_detailModel.semester isEqualToString:@"1"]) {
                semester = TRANSLATE(@"bill_title_biller_term_first");
            } else if ([_detailModel.semester isEqualToString:@"2"]) {
                semester = TRANSLATE(@"bill_title_biller_term_second");
            } else if ([_detailModel.semester isEqualToString:@"3"]) {
                semester = TRANSLATE(@"bill_title_biller_term_third");
            }
            [_orderedDetailDic setObject: [RRTools nvl:semester replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"bill_title_biller_term")];
            [_orderedDetailDic setObject: [RRTools nvl:_detailModel.classes replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"bill_title_biller_school")];
            [_orderedDetailDic setObject: [RRTools nvl:_detailModel.billAccount replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"bill_title_biller_student_name")];
        }else if ([_detailModel.billType isEqualToString:@"04"]) {//会费
            [_orderedDetailDic setObject: [RRTools nvl:_detailModel.billAccount replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_membership_number")];
        } else if ([_detailModel.billType isEqualToString:@"05"]) {//社区服务
            [_orderedDetailDic setObject: [RRTools nvl:_detailModel.billPaymentType replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_type_of_payment")];
            [_orderedDetailDic setObject: [RRTools nvl:_detailModel.userName replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"bill_title_biller_owner_name")];
            [_orderedDetailDic setObject: [RRTools nvl:[NSString stringWithFormat:@"%@ %@",_detailModel.address,_detailModel.city] replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"register_label_address")];
//            [_orderedDetailDic setObject: [RRTools nvl:_detailModel.city replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_city")];
        } else if ([_detailModel.billType isEqualToString:@"06"]) {//其他
            [_orderedDetailDic setObject: [RRTools nvl:_detailModel.billerCode replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_biller_code")];
            [_orderedDetailDic setObject: [RRTools nvl:_detailModel.billerName replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_biller_name")];
            [_orderedDetailDic setObject: [RRTools nvl:_detailModel.billAccount replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_account_number")];
        }
        //添加手续费字段，根据用户是收款人和付款人来判断显示的手续费
        NSString *feeAmt = [RRTools nvl:_detailModel.feeAmt];
        if (feeAmt.doubleValue > 0) {
            feeAmt = [NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:feeAmt]]];
        } else {
            feeAmt = [NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],@"0.00"];
        }
        [_orderedDetailDic setObject:feeAmt forKey:TRANSLATE(@"common_label_fee")];
        //添加手续费字段，根据用户是收款人和付款人来判断显示的手续费
        NSString *taxAmt = [RRTools nvl:_detailModel.taxAmt];
        if (taxAmt.doubleValue > 0) {
            taxAmt = [NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:taxAmt]]];
        } else {
            taxAmt = [NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],@"0.00"];
        }
        [_orderedDetailDic setObject:taxAmt forKey:TRANSLATE(@"common_label_tax")];
//        NSString *walletAmt = [RRTools nvl:_detailModel.walletRefundAmount];
//        NSString *walletAccount = [RRTools nvl:_detailModel.actualAmt];
//        if (walletAccount.doubleValue > 0) {
//            walletAccount = [NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:walletAccount]]];
//            if ([@"03" isEqualToString:_detailModel.paymentType]) {
//                [_orderedDetailDic setObject:walletAccount forKey:TRANSLATE(@"history_label_bank_accoun")];
//            } else {
//                [_orderedDetailDic setObject:walletAccount forKey:TRANSLATE(@"history_label_wallet_accoun")];
//            }
//        }
//        
//        NSString *cashbackAmt = [RRTools nvl:_detailModel.cashbackAmt];
//        if (cashbackAmt.doubleValue > 0) {
//            cashbackAmt = [NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:cashbackAmt]]];
//            [_orderedDetailDic setObject:cashbackAmt forKey:TRANSLATE(@"history_label_cashBack_account")];
//        }
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.paymentMethod replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_payment_method")];
        NSString *tradeTime = [RRTools nvl:_detailModel.tradeTime];
        [_orderedDetailDic setObject:tradeTime forKey:TRANSLATE(@"history_label_date_time")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.orderId replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_no")];
        NSString *remark = [RRTools nvl:_detailModel.remark];
        if (![RRTools isEmpty:remark]) {
            [_orderedDetailDic setObject:[RRTools nvl:_detailModel.remark replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"common_label_remark")];
        }
    } else if ([@"32" isEqualToString:tradeType]) {//缴费SDK
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.billAccount replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"personal_label_mobile")];
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.rechargeTradingType replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_type")];
        if(_detailModel.bundlePlan.length>0) {
            [_orderedDetailDic setObject: [RRTools nvl:_detailModel.bundleName replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_bundle_name")];
            [_orderedDetailDic setObject: [RRTools nvl:_detailModel.bundlePlan replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_bundle_plan")];
        } else {
            [_orderedDetailDic setObject:[NSString stringWithFormat:@"%@ %@",_detailModel.orderCurrType,[RRTools strmethodComma:[RRTools formatAmount:[RRTools nvl:_detailModel.tradeAmt]]]] forKey:TRANSLATE(@"history_label_airtime")];
        }
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.paymentMethod replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_payment_method")];
        //营销优惠费
        NSString *discountAmt = [RRTools nvl:_detailModel.discountAmt];
        if (discountAmt.doubleValue > 0) {
            [_orderedDetailDic setObject:[NSString stringWithFormat:@"%@ -%@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:discountAmt]]] forKey:TRANSLATE(@"history_label_discount_amount")];
        }
        NSString *walletAccount = [RRTools nvl:_detailModel.actualAmt];
        NSString *cashbackAmt = [RRTools nvl:_detailModel.cashbackAmt];
        if (walletAccount.doubleValue > 0 && (discountAmt.doubleValue > 0 || cashbackAmt.doubleValue > 0)) {
            walletAccount = [NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:walletAccount]]];
            if ([@"03" isEqualToString:_detailModel.paymentType]) {
                [_orderedDetailDic setObject:walletAccount forKey:TRANSLATE(@"history_label_bank_accoun")];
            } else {
                [_orderedDetailDic setObject:walletAccount forKey:TRANSLATE(@"history_label_wallet_accoun")];
            }
        }
        
        if (cashbackAmt.doubleValue > 0) {
            cashbackAmt = [NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:cashbackAmt]]];
            [_orderedDetailDic setObject:cashbackAmt forKey:TRANSLATE(@"history_label_cashBack_account")];
        }
        NSString *tradeTime = [RRTools nvl:_detailModel.tradeTime];
        [_orderedDetailDic setObject:tradeTime forKey:TRANSLATE(@"history_label_date_time")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.orderId replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_no")];
        
        if([[RRTools nvl:_detailModel.discountAmt] doubleValue] > 0){
            self.disCountDic = [[NSMutableDictionary alloc] init];
            
            NSString *couponAmt = [RRTools nvl:_detailModel.couponAmt];
            if (couponAmt.doubleValue > 0) {
                couponAmt = [NSString stringWithFormat:@"%@ -%@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:couponAmt]]];
                [self.disCountDic setObject:couponAmt forKey:TRANSLATE(@"scan_label_coupon")];
            }
            NSString *membershipPointAmt = [RRTools nvl:_detailModel.membershipPointAmt];
            if (membershipPointAmt.doubleValue > 0) {
                membershipPointAmt = [NSString stringWithFormat:@"%@ -%@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:membershipPointAmt]]];
                [self.disCountDic setObject:membershipPointAmt forKey:TRANSLATE(@"scan_label_membership_points")];
            }
            NSString *membershipBonusAmt = [RRTools nvl:_detailModel.membershipBonusAmt];
            if (membershipBonusAmt.doubleValue > 0) {
                membershipBonusAmt = [NSString stringWithFormat:@"%@ -%@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:membershipBonusAmt]]];
                [self.disCountDic setObject:membershipBonusAmt forKey:TRANSLATE(@"scan_label_membership_bonus")];
            }
        }
        NSString *channelTransNo = [RRTools nvl:_detailModel.channelTransNo];
        if (![RRTools isEmpty:channelTransNo]) {
            [_orderedDetailDic setObject:channelTransNo forKey:TRANSLATE(@"history_label_channel_transaction_no")];
        }
    }else if ([@"40" isEqualToString:tradeType]) {//AA
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.payMethod replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_payment_method")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.name replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_name")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.tradeTime replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_date_time")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.orderId replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_no")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.remark replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"common_label_remark")];
    }else if ([@"50" isEqualToString:tradeType]) {//退款
        NSString *feeAmt ;
        NSString *taxAmt ;
        if([_detailModel.originalTradeType isEqualToString:@"30"]){
            [_orderedDetailDic setObject:[RRTools nvl:_detailModel.merName replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_merchant_name")];
            [_orderedDetailDic setObject:[RRTools nvl:_detailModel.remark replace:TRANSLATE(@"history_label_default_commodity")] forKey:TRANSLATE(@"history_label_commodity")];
        } else {
            [_orderedDetailDic setObject:[RRTools nvl:_detailModel.merName replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_biller")];
            [_orderedDetailDic setObject:[RRTools nvl:_detailModel.remark replace:TRANSLATE(@"history_label_default_commodity")] forKey:TRANSLATE(@"history_label_biller_type")];
        }
        
        feeAmt = [RRTools nvl:_detailModel.refundPayFeeAmt];
        if (feeAmt.doubleValue > 0) {
            feeAmt = [NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:feeAmt]]];
            [_orderedDetailDic setObject:feeAmt forKey:TRANSLATE(@"history_label_refund_fee")];
        }
        //添加手续费字段，根据用户是收款人和付款人来判断显示的手续费
        taxAmt = [RRTools nvl:_detailModel.refundPayTaxAmt];
        if (taxAmt.doubleValue > 0) {
            taxAmt = [NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:taxAmt]]];
            [_orderedDetailDic setObject:taxAmt forKey:TRANSLATE(@"history_label_refund_tax")];
        }
        NSString *walletAmt = [RRTools nvl:_detailModel.walletRefundAmount];
        NSString *cashBackAmt = [RRTools nvl:_detailModel.cashbackRefundAmount];
        if (walletAmt.doubleValue > 0 && cashBackAmt.doubleValue > 0) {
            walletAmt = [NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:walletAmt]]];
            if ([@"03" isEqualToString:_detailModel.paymentType]) {
                [_orderedDetailDic setObject:walletAmt forKey:TRANSLATE(@"history_label_bank_accoun")];
            } else {
                [_orderedDetailDic setObject:walletAmt forKey:TRANSLATE(@"history_label_wallet_accoun")];
            }
        }
        if (cashBackAmt.doubleValue > 0) {
            cashBackAmt = [NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:cashBackAmt]]];
            [_orderedDetailDic setObject:cashBackAmt forKey:TRANSLATE(@"history_label_cashback_account_refund_amt")];
        }
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.refundAccount replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_refund_acc")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.tradeTime replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_date_time")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.orderId replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_no")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.originalOrderNo replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_related_payment_transaction_no")];
        NSString *outOrderNo = [RRTools nvl:_detailModel.outOrderNo];
        if(outOrderNo.length>0){
            [_orderedDetailDic setObject:outOrderNo forKey:TRANSLATE(@"history_label_mer_orderno")];
        }
    }else if ([@"55" isEqualToString:tradeType]) {//返现
        NSString *businessType = [RRTools nvl:_detailModel.businessType];
        if ([@"05" isEqualToString:businessType]) { //注册
            [_orderedDetailDic setObject:TRANSLATE(@"history_label_register") forKey:TRANSLATE(@"history_label_business_type")];
        }else if([@"00" isEqualToString:businessType]){
            [_orderedDetailDic setObject:TRANSLATE(@"history_label_topup") forKey:TRANSLATE(@"history_label_business_type")];
        }else{
            [_orderedDetailDic setObject: TRANSLATE(@"common_lable_n_a") forKey:TRANSLATE(@"history_label_business_type")];
        }
        NSString *participantType = [RRTools nvl:_detailModel.participantType];
        if ([@"0" isEqualToString:participantType]) { // 0：发起人
            [_orderedDetailDic setObject:TRANSLATE(@"history_label_initiator") forKey:TRANSLATE(@"history_label_paticipate_role")];
        }else if([@"1" isEqualToString:participantType]){ //1：推荐人
            [_orderedDetailDic setObject:TRANSLATE(@"history_label_recommender") forKey:TRANSLATE(@"history_label_paticipate_role")];
            [_orderedDetailDic setObject:[RRTools nvl:_detailModel.initiator replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_initiator")];
        }else{
            [_orderedDetailDic setObject: TRANSLATE(@"common_lable_n_a") forKey:TRANSLATE(@"history_label_paticipate_role")];
        }
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.tradeTime replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_date_time")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.orderId replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_no")];
    } else if ([@"01" isEqualToString:tradeType]) {//代客充值
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.payMethod replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_payment_method")];
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.agentName replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"common_label_branch_name")];
        NSString *feeAmt = [RRTools nvl:_detailModel.feeAmt];
        if (feeAmt.doubleValue > 0) {
            [_orderedDetailDic setObject:[NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:feeAmt]]] forKey:TRANSLATE(@"common_label_fee")];
        } else {
            [_orderedDetailDic setObject:[NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],@"0.00"] forKey:TRANSLATE(@"common_label_fee")];
        }
        NSString *taxAmt = [RRTools nvl:_detailModel.taxAmt];
        if (taxAmt.doubleValue > 0) {
            [_orderedDetailDic setObject:[NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:taxAmt]]] forKey:TRANSLATE(@"common_label_tax")];
        } else {
            [_orderedDetailDic setObject:[NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],@"0.00"] forKey:TRANSLATE(@"common_label_tax")];
        }
//        NSString *tradeTime = [RRTools formatDate:[RRTools nvl:_detailModel.tradeTime] withOldFormat:@"yyyy-MM-dd HH:mm:ss" newFormat:@"dd/MM/yyyy HH:mm:ss"];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.tradeTime] forKey:TRANSLATE(@"history_label_date_time")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.orderId replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_no")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.remark replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"common_label_remark")];
    } else if ([@"02" isEqualToString:tradeType]) {//代客话费充值
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.recMobileNo replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"personal_label_mobile")];
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.rechargeTradingType replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_type")];
        if(_detailModel.bundlePlan.length>0) {
            [_orderedDetailDic setObject: [RRTools nvl:_detailModel.bundleName replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_bundle_name")];
            [_orderedDetailDic setObject: [RRTools nvl:_detailModel.bundlePlan replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_bundle_plan")];
        } else {
            [_orderedDetailDic setObject:[NSString stringWithFormat:@"%@ %@",_detailModel.orderCurrType,[RRTools strmethodComma:[RRTools formatAmount:[RRTools nvl:_detailModel.tradeAmt]]]] forKey:TRANSLATE(@"history_label_airtime")];
        }
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.payMethod replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_payment_method")];
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.agentName replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_agent")];
//        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.agentOperName replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_operator")];
        NSString *tradeTime = [RRTools nvl:_detailModel.tradeTime];
        [_orderedDetailDic setObject:tradeTime forKey:TRANSLATE(@"history_label_date_time")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.orderId replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_no")];
    } else if ([@"11" isEqualToString:tradeType]) {//代客转账
        if([_detailModel.orderIncomeFlag isEqualToString:@"+"]) {
            [_orderedDetailDic setObject: [RRTools nvl:_detailModel.counterpartyMobileNo replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"balance_label_Payer_mobile")];
            [_orderedDetailDic setObject: [RRTools nvl:_detailModel.counterpartyName replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"balance_label_Payer_name")];
        } else {
            [_orderedDetailDic setObject: [RRTools nvl:_detailModel.counterpartyMobileNo replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"balance_label_Payee_mobile")];
            [_orderedDetailDic setObject: [RRTools nvl:_detailModel.counterpartyName replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"balance_label_Payee_name")];
        }
        NSString *feeAmt = [RRTools nvl:_detailModel.feeAmt];
        if (feeAmt.doubleValue > 0) {
            [_orderedDetailDic setObject:[NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:feeAmt]]] forKey:TRANSLATE(@"common_label_fee")];
        }
        NSString *taxAmt = [RRTools nvl:_detailModel.taxAmt];
        if (taxAmt.doubleValue > 0) {
            [_orderedDetailDic setObject:[NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:taxAmt]]] forKey:TRANSLATE(@"common_label_tax")];
        }
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.payMethod replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_payment_method")];
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.agentName replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"common_label_branch_name")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.tradeTime replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_date_time")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.orderId replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_no")];
        NSString *remark = [RRTools nvl:_detailModel.remark];
        if(remark.length>0){
            [_orderedDetailDic setObject:remark forKey:TRANSLATE(@"common_label_remark")];
        }
    } else if ([@"13" isEqualToString:tradeType]) {//退回
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.name replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"balance_label_Payer_name")];
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.payMethod replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_payment_method")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.tradeTime replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_date_time")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.orderId replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_no")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.originalOrderId replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_original_transaction_no")];
    }else if ([@"22" isEqualToString:tradeType]) {//代客提现
//        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.agentName replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"common_label_branch_name")];
        
        NSString *bankAccount = [NSString stringWithFormat:@"%@(%@)", _detailModel.agentName, _detailModel.agentShortCode];
        [_orderedDetailDic setObject:bankAccount forKey:TRANSLATE(@"common_label_branch_name")];
        NSString *feeAmt = [RRTools nvl:_detailModel.feeAmt];
        if (feeAmt.doubleValue > 0) {
            [_orderedDetailDic setObject:[NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:feeAmt]]] forKey:TRANSLATE(@"common_label_fee")];
        } else {
            [_orderedDetailDic setObject:[NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],@"0.00"] forKey:TRANSLATE(@"common_label_fee")];
        }
        NSString *taxAmt = [RRTools nvl:_detailModel.taxAmt];
        if (taxAmt.doubleValue > 0) {
            [_orderedDetailDic setObject:[NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:taxAmt]]] forKey:TRANSLATE(@"common_label_tax")];
        } else {
            [_orderedDetailDic setObject:[NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],@"0.00"] forKey:TRANSLATE(@"common_label_tax")];
        }
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.payMethod replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_payment_method")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.tradeTime] forKey:TRANSLATE(@"history_label_date_time")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.orderId replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_no")];
//        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.remark replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"common_label_remark")];
    }else if ([@"66" isEqualToString:tradeType] || [@"65" isEqualToString:tradeType]) {//交易业务
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.payMethod replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_payment_method")];
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.enquiryAccount replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"common_tab_account")];
        NSString *feeAmt = [RRTools nvl:_detailModel.feeAmt];
        if (feeAmt.doubleValue > 0) {
            [_orderedDetailDic setObject:[NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:feeAmt]]] forKey:TRANSLATE(@"common_label_fee")];
        }
        NSString *taxAmt = [RRTools nvl:_detailModel.taxAmt];
        if (taxAmt.doubleValue > 0) {
            [_orderedDetailDic setObject:[NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:taxAmt]]] forKey:TRANSLATE(@"common_label_tax")];
        }
        NSString *tradeTime =_detailModel.tradeTime;
        [_orderedDetailDic setObject:tradeTime forKey:TRANSLATE(@"history_label_date_time")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.orderId replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_no")];
    }else if ([@"33" isEqualToString:tradeType]) {//代客电费
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.meterNo replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_meter_number")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.meterName replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"personal_label_user_name")];
        //支付手续费
        NSString *payFeeAmt = [RRTools nvl:_detailModel.feeAmt];
        if (payFeeAmt.doubleValue > 0) {
            [_orderedDetailDic setObject:[NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:payFeeAmt]]] forKey:TRANSLATE(@"common_label_fee")];
        }
        //添加手续费字段，根据用户是收款人和付款人来判断显示的手续费
        NSString *taxAmt = [RRTools nvl:_detailModel.taxAmt];
        if (taxAmt.doubleValue > 0) {
            taxAmt = [NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:taxAmt]]];
            [_orderedDetailDic setObject:taxAmt forKey:TRANSLATE(@"common_label_tax")];
        }
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.payMethod replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_payment_method")];
//        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.agentName replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"common_label_branch_name")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.tradeTime replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_date_time")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.orderId replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_no")];
        NSString *remark = [RRTools nvl:_detailModel.remark];
        if(remark.length>0){
            [_orderedDetailDic setObject:remark forKey:TRANSLATE(@"common_label_remark")];
        }
    }else if ([@"75" isEqualToString:tradeType]) {//交易业务
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.payMethod replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_payment_method")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.name replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"balance_label_Payer_name")];
        NSString *tradeTime =_detailModel.tradeTime;
        [_orderedDetailDic setObject:tradeTime forKey:TRANSLATE(@"history_label_date_time")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.orderId replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_no")];
        NSString *remark = [RRTools nvl:_detailModel.remark];
        if(remark.length>0){
            [_orderedDetailDic setObject:remark forKey:TRANSLATE(@"common_label_remark")];
        }
    } else if ([@"26" isEqualToString:tradeType] || [@"27" isEqualToString:tradeType]) {//zipit
        NSString *bankAccount = [NSString stringWithFormat:@"%@(%@)", _detailModel.bankName, _detailModel.cardNo];
        [_orderedDetailDic setObject:bankAccount forKey:TRANSLATE(@"withdrawal_label_bank_account")];
        [_orderedDetailDic setObject: [RRTools nvl:_detailModel.payMethod replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_payment_method")];
        if ([@"-" isEqualToString:_detailModel.orderIncomeFlag]) {
            NSString *feeAmt = [RRTools nvl:_detailModel.feeAmt];
            if (feeAmt.doubleValue > 0) {
                [_orderedDetailDic setObject:[NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:feeAmt]]] forKey:TRANSLATE(@"common_label_fee")];
            }
            NSString *taxAmt = [RRTools nvl:_detailModel.taxAmt];
            if (taxAmt.doubleValue > 0) {
                [_orderedDetailDic setObject:[NSString stringWithFormat:@"%@ %@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY],[RRTools strmethodComma:[RRTools formatAmount:taxAmt]]] forKey:TRANSLATE(@"common_label_tax")];
            }
        }
        
        NSString *tradeTime =_detailModel.createTime;
        [_orderedDetailDic setObject:tradeTime forKey:TRANSLATE(@"history_label_date_time")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.orderId replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_no")];
        NSString *remark = [RRTools nvl:_detailModel.remark];
        if (remark.length>0) {
            [_orderedDetailDic setObject:remark forKey:TRANSLATE(@"common_label_remark")];
        }
    } else if ([@"28" isEqualToString:tradeType]) {//ZIPIT Reversal
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.bankName replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"bankcard_label_bankname")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.cardNo replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"withdrawal_label_bank_account")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.payMethod replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_payment_method")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.createTime replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_date_time")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.orderId replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_transaction_no")];
        [_orderedDetailDic setObject:[RRTools nvl:_detailModel.originalOrderNo replace:TRANSLATE(@"common_lable_n_a")] forKey:TRANSLATE(@"history_label_original_transaction_no")];
    }
    [self setUp];
    [self addViewConstraints];
    [self loadViews];
}
-(void)loadViews
{
    //    交易类型tradeType  （00 -Top UP  10:Fund Transfer  40:Split Bill）
    //    充值支付方式 当tradeType为00时  recordTitle  （00：Credit/Debit Card）
    //    充值详情里里面payMethod （00：Credit/Debit Card）
    //    充值-00，转账-10，提现-20，支付-30，AA-40，退款-50  余额业务-65 交易业务-66
    NSString *tradeType = [RRTools nvl:_detailModel.tradeType];
    NSString *payType = [RRTools nvl:self.model.tradeTypeName];
    if([@"00" isEqualToString:tradeType]){
        self.payTypeImg.image = [UIImage imageNamed:@"top up"];
    }else if([@"10" isEqualToString:tradeType]){
        self.payTypeImg.image = [UIImage imageNamed:@"Fund Transfer"];
    }else if([@"20" isEqualToString:tradeType]){
        self.payTypeImg.image = [UIImage imageNamed:@"icon_Withdrawal"];
    }else if([@"30" isEqualToString:tradeType]){
        self.payTypeImg.image = [UIImage imageNamed:@"Payment"];
    }else if([@"34" isEqualToString:tradeType]){
        self.payTypeImg.image = [UIImage imageNamed:@"icon_thirty_party_pay"];
    }else if([@"31" isEqualToString:tradeType]){//缴费
        self.payTypeImg.image = [UIImage imageNamed:@"icon_bill"];
    }else if([@"32" isEqualToString:tradeType]){//缴费
        self.payTypeImg.image = [UIImage imageNamed:@"icon_agent_moblie"];
    }else if([@"40" isEqualToString:tradeType]){
        self.payTypeImg.image = [UIImage imageNamed:@"icon_Split Bill"];
    }else if([@"50" isEqualToString:tradeType]){
        self.payTypeImg.image = [UIImage imageNamed:@"icon_refund"];
    }else if([@"55" isEqualToString:tradeType]){
        self.payTypeImg.image = [UIImage imageNamed:@"icon_Cashback"];
    }else if([@"01" isEqualToString:tradeType]){
        self.payTypeImg.image = [UIImage imageNamed:@"icon_agent_topup"];
    }else if([@"02" isEqualToString:tradeType]){
        self.payTypeImg.image = [UIImage imageNamed:@"icon_agent_moblie"];
    }else if([@"11" isEqualToString:tradeType]){
        self.payTypeImg.image = [UIImage imageNamed:@"icon_agent_transfer"];
    }else if([@"13" isEqualToString:tradeType]){
        self.payTypeImg.image = [UIImage imageNamed:@"icon_reversal_transfer"];
    }else if([@"22" isEqualToString:tradeType]){
        self.payTypeImg.image = [UIImage imageNamed:@"icon_agent_withdrawal"];
    }else if([@"65" isEqualToString:tradeType]){
        self.payTypeImg.image = [UIImage imageNamed:@"icon_balance_enquiry"];
    }else if([@"66" isEqualToString:tradeType]){
        self.payTypeImg.image = [UIImage imageNamed:@"icon_statement_enquiry"];
    }else if([@"75" isEqualToString:tradeType]){
        self.payTypeImg.image = [UIImage imageNamed:@"Fund Transfer"];
    }else if([@"33" isEqualToString:tradeType]){//缴费
        self.payTypeImg.image = [UIImage imageNamed:@"icon_agent_zesa"];
    }else if([@"26" isEqualToString:tradeType] || [@"27" isEqualToString:tradeType]){
        self.payTypeImg.image = [UIImage imageNamed:@"icon_zipit"];
    }else if([@"28" isEqualToString:tradeType]){
        self.payTypeImg.image = [UIImage imageNamed:@"icon_zipit_reversal"];
    }
    self.payTypeLabel.text = payType;
    NSString *orderIncomeFlag = [RRTools nvl:_detailModel.orderIncomeFlag];
    self.amountLabel.text = [NSString stringWithFormat:@"%@ %@%@",[NSString stringWithFormat:@"%@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY]],orderIncomeFlag,[RRTools strmethodComma:[RRTools formatAmount:[RRTools nvl:_detailModel.tradeAmt]]]];
//    self.currencyLabel.text = [NSString stringWithFormat:@"%@",[RRTools nvl:_detailModel.orderCurrType replace:CURRENCY]];
//    除提现以外的交易使用 0-处理中 1-成功 2-失败 3-关闭 4-全额退款
//    提现订单由于有初始化状态 直接使用原始订单状态 10:初始化 20 :处理中 30 :成功 40:失败
    NSString *orderStatus = [RRTools nvl:_detailModel.orderStatus];
    if([@"20" isEqualToString:tradeType] || [@"26" isEqualToString:tradeType] || [@"27" isEqualToString:tradeType]|| [@"28" isEqualToString:tradeType]){
        if ([@"10" isEqualToString:orderStatus]) {
            self.statusLabel.text = TRANSLATE(@"history_label_pending");
        }else if ([@"20" isEqualToString:orderStatus]) {
            self.statusLabel.text = TRANSLATE(@"history_label_pending");
        }else if ([@"30" isEqualToString:orderStatus]) {
            self.statusLabel.text = TRANSLATE(@"history_label_successful");
        }else if ([@"40" isEqualToString:orderStatus]) {
            self.statusLabel.text = TRANSLATE(@"history_label_failed");
        }else{
            self.statusLabel.text = TRANSLATE(@"history_label_closed");
        }
    }else{
        if ([@"0" isEqualToString:orderStatus]) {
            self.statusLabel.text = TRANSLATE(@"history_label_pending");
        }else if ([@"1" isEqualToString:orderStatus]) {
            self.statusLabel.text = TRANSLATE(@"history_label_successful");
        }else if ([@"2" isEqualToString:orderStatus]) {
            self.statusLabel.text = TRANSLATE(@"history_label_failed");
        }else if ([@"3" isEqualToString:orderStatus]) {
            self.statusLabel.text = TRANSLATE(@"history_label_closed");
        }else if ([@"4" isEqualToString:orderStatus]) {
            self.statusLabel.text = TRANSLATE(@"history_label_full_refund");
        }else if ([@"5" isEqualToString:orderStatus]) {
            self.statusLabel.text = TRANSLATE(@"history_label_partial_refund");
        }else{
            self.statusLabel.text = TRANSLATE(@"history_label_closed");
            if([@"66" isEqualToString:tradeType] || [@"65" isEqualToString:tradeType]){
                self.statusLabel.text = TRANSLATE(@"history_label_successful");
            }
        }
    }
}
#pragma mark- 重写方法
-(void)backToPuper
{
    [self.navigationController popViewControllerAnimated:YES];
}
//继承的方法实现，语言更改后，页面需要刷新的内容
-(void)languageChange:(NSNotification*)notification
{
   //将observer收到通知后的方法，放在子线程中执行，这种方式接收到通知还是同步，但是接收到通知后的处理改成异步
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        
    });
}

#pragma mark- 页面生命周期/布局约束方法
- (void)viewDidLoad {
    self.disCountInt = -2;
    self.hasLeftBtn = YES;
    [super viewDidLoad];
    self.titleString = TRANSLATE(@"history_title_transaction_detail");
    [self setNav];
    [self requestOrderDetails];
}
-(void)setNav
{
    [self.headerView addSubview:self.rightBtn];
    [self.rightBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(-15);
        make.bottom.mas_equalTo(-10);
        make.height.and.width.mas_equalTo(24*AUTO_SIZE);
    }];
}
-(void)setUp
{
    [self.view addSubview:self.detailView];
    [self.detailView addSubview:self.detailConView];
    [self.detailView addSubview:self.payTypeView];
    [self.detailView addSubview:self.amountLabel];
//    [self.detailView addSubview:self.currencyLabel];
    [self.detailView addSubview:self.statusLabel];
    [self.detailView addSubview:self.infoView];
    [self.view addSubview:self.enlargeView];
}
-(void)addViewConstraints
{
    [self.detailView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.headerView.mas_bottom);
        make.left.and.right.mas_equalTo(0);
        make.bottom.mas_equalTo(0);
    }];
    [self.detailConView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self.detailView);
        make.width.mas_equalTo(self.detailView);
    }];
    [self.payTypeView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(30*AUTO_SIZE);
        make.left.greaterThanOrEqualTo(self.detailView).offset(15*AUTO_SIZE);
        make.right.lessThanOrEqualTo(self.detailView).offset(-15*AUTO_SIZE);
        make.centerX.mas_equalTo(self.detailView.mas_centerX);
    }];
    [self.amountLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self.detailView.mas_centerX);
        make.top.mas_equalTo(self.payTypeView.mas_bottom).offset(20*AUTO_SIZE);
    }];
//    [self.currencyLabel mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.right.mas_equalTo(self.amountLabel.mas_left).offset(-5*AUTO_SIZE);
//        make.bottom.mas_equalTo(self.amountLabel.mas_bottom);
//    }];
    [self.statusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.and.right.mas_equalTo(0);
        make.top.mas_equalTo(self.amountLabel.mas_bottom).offset(4*AUTO_SIZE);
        make.height.mas_equalTo(25*AUTO_SIZE);
    }];
    [self.infoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(15);
        make.right.mas_equalTo(-15);
        make.top.mas_equalTo(self.statusLabel.mas_bottom).offset(20*AUTO_SIZE);
        make.bottom.mas_equalTo(self.detailView.mas_bottom).offset(-40*AUTO_SIZE);
    }];
    [self.enlargeView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.and.right.mas_equalTo(0);
        make.top.mas_equalTo(self.headerView.mas_bottom);
        make.bottom.mas_equalTo(0);
    }];
}

@end
