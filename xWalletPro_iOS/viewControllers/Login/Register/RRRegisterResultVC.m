//
//  RRRegisterResultVC.m
//  xWalletPro_iOS
//
//  Created by <PERSON> on 2/7/20.
//  Copyright © 2020 Shirley. All rights reserved.
//

#import "RRRegisterResultVC.h"
#import "RRXNButton.h"
#import "RRTabbarVC.h"
#import "RRSetLoginPwdVC.h"
#import "RRTempDeviceLoginVC.h"
#import "RRLoginVC.h"
#import "RRRegisterProcessView.h"
#import "RRJuniorAccountVC.h"

@interface RRRegisterResultVC ()

@property (nonatomic,strong) RRRegisterProcessView *processView;
@property(nonatomic,strong) UIImageView *statusImg;
@property(nonatomic,strong) UILabel *statusLabel;
@property(nonatomic,strong) UILabel *tipLabel;
@property(nonatomic,strong) RRXNButton *doneBtn;

@end

@implementation RRRegisterResultVC

#pragma mark- 控件懒加载

//进度
-(RRRegisterProcessView *)processView{
    if(!_processView){
        _processView = [[RRRegisterProcessView alloc] init];
        if(![self.dataDic[@"userStatus"] isEqualToString:@"0"]){
            _processView.title = TRANSLATE(@"register_label_authentication");
        } else {
            _processView.title = TRANSLATE(@"register_label_completed");
        }
        if([@"junior" isEqualToString:self.from]){
            _processView.currentIndex = @"5";
            _processView.total = @"5";
        } else {
            _processView.currentIndex = @"6";
            _processView.total = @"6";
        }
        [_processView loadView];
    }
    return _processView;
}

-(UIImageView *)statusImg
{
    if (!_statusImg) {
        _statusImg = [[UIImageView alloc]init];
        _statusImg.image = [UIImage imageNamed:@"img_success"];
    }
    return _statusImg;
}
-(UILabel *)statusLabel
{
    if (!_statusLabel) {
        _statusLabel = [[UILabel alloc]init];
        _statusLabel.text = TRANSLATE(@"register_tips_receive_register");
        _statusLabel.font = FONT(16*FONT_SIZE);
        _statusLabel.textColor = MAIN_TEXT_COLOR;
        _statusLabel.textAlignment = NSTextAlignmentLeft;
        _statusLabel.numberOfLines = 0;
    }
    return _statusLabel;
}
-(UILabel *)tipLabel
{
    if (!_tipLabel) {
        _tipLabel = [[UILabel alloc]init];
       
        if([@"junior" isEqualToString:self.from]){
            _tipLabel.text = TRANSLATE(@"register_label_junior_received2");
        } else {
            _tipLabel.text = TRANSLATE(@"register_tips_authentication");
        }

        _tipLabel.font = FONT(14*FONT_SIZE);
        _tipLabel.textColor = SUB_TEXT_COLOR;
        _tipLabel.textAlignment = NSTextAlignmentLeft;
        _tipLabel.numberOfLines = 0;
    }
    return _tipLabel;
}
-(RRXNButton *)doneBtn
{
    if (!_doneBtn) {
        _doneBtn = [[RRXNButton alloc]init];
        if([@"junior" isEqualToString:self.from]) {
            [_doneBtn setTitle:TRANSLATE(@"common_btn_done") forState:UIControlStateNormal];
        } else {
            if(![self.dataDic[@"userStatus"] isEqualToString:@"0"]){
                [_doneBtn setTitle:TRANSLATE(@"common_btn_done") forState:UIControlStateNormal];
            } else {
                [_doneBtn setTitle:TRANSLATE(@"login_btn_login") forState:UIControlStateNormal];
            }
        }
        [_doneBtn addTarget:self action:@selector(doneBtnClick) forControlEvents:UIControlEventTouchUpInside];
        [RRTools btnEnable:_doneBtn withHighColor:BTN_HIGH_COLOR];
    }
    return _doneBtn;
}


#pragma mark- privateMethod
-(void)doneBtnClick
{
    for(UIViewController*vc in self.navigationController.viewControllers) {
        if([self.from isEqualToString:@"junior"]) {
            if([vc isKindOfClass:[RRJuniorAccountVC class]]) {
                [self.navigationController popToViewController:vc animated:YES];
            }
        } else {
            if([vc isKindOfClass:[RRLoginVC class]]) {
                [self.navigationController popToViewController:vc animated:YES];
            }
        }
    }
    /*
    //登录
    RRHUDView *hud = [RRHUDView defaultHUDViewForWindow];
    [TOPWINDOW addSubview:hud];
    [hud show];
    NSMutableDictionary *jsonDic = [NSMutableDictionary dictionary];
    [jsonDic setObject:[RRTools nvl:self.dataDic[@"userName"]] forKey:@
     "userName"];
    [jsonDic setObject:[RRTools nvl:self.dataDic[@"password"]] forKey:@"password"];
    [jsonDic setObject:@"1" forKey:@"passwordType"];
    [jsonDic setObject:[RRTools nvl:[RRTools strForKey:@"fcmRegistrationID"]] forKey:@"deviceToken"];
    [RRHttpRequest postSend:USER_LOGIN urlString:nil jsonDic:jsonDic success:^(RRNetworkModel *model) {
        NSString * status = [NSString stringWithFormat:@"%@",model.status];
        if ([status isEqualToString:@"0"]){
            //存储个人信息
            RRUserInfo *userInf = [RRUserInfo mj_objectWithKeyValues:model.data];
            //当前是否是未激活用户登录，如果是，则跳转设置登录密码页面，如果不是，则存储信息跳转首页
            if([@"3" isEqualToString:userInf.userStatus]){
                [RRTools getMainQueueDo:^{
                    [hud hide];
                    NSMutableDictionary *dictionary = [NSMutableDictionary dictionary];
                    [dictionary setObject:userInf.userName forKey:@"userName"];
                    RRSetLoginPwdVC *setLoginPwdVC = [[RRSetLoginPwdVC alloc]init];
                    setLoginPwdVC.from = @"Login";
                    setLoginPwdVC.dataDic = dictionary;
                    [self.navigationController pushViewController:setLoginPwdVC animated:YES];
                }];
            }else{
                //存储个人信息
                RRUserInfo *userInf = [RRUserInfo mj_objectWithKeyValues:model.data];
                //指纹信息处理
                //1.清除之前登录账号的指纹信息
                [RRTools setBool:NO key:[RRTools getBiometricsToken]];
                //2.存储新的客户数据
                [RRUserInfo setObject:userInf forKey:@"customerDic"];
                //存储币种
                [RRTools setStr:userInf.currency key:@"CURRENCY"];
                //3.指纹登录的状态同步，只同步服务器为关的状态
                NSString *bioStatus = [RRTools nvl:userInf.bioStatus];
                NSString *gestureStatus = [RRTools nvl:userInf.gestureStatus];
                if ([@"1" isEqualToString:bioStatus] || [@"1" isEqualToString:gestureStatus]) {
                    [RRTools setBool:YES key:[RRTools getBiometricsToken]];
                } else {
                    [RRTools setBool:NO key:[RRTools getBiometricsToken]];
                }
                [RRTools getMainQueueDo:^{
                    //跳转主页面
                    [hud hide];
                    RRTabbarVC *tabBarController = [[RRTabbarVC alloc]init];
                    tabBarController.delegate = APPDELEGATE;
                    RRNavigationController *navVC = [[RRNavigationController alloc]initWithRootViewController:tabBarController];
                    [UIApplication sharedApplication].keyWindow.rootViewController = navVC;
                    GETTHEAPP
                    theApp.hasLogin = YES;
                    [theApp appPay];
                }];
            }
        }else{
            //报错
            NSString *msg = model.message;
            if ([status isEqualToString:@"RRB-01001047"]){
                //当前是临时设备登录
                [RRTools getMainQueueDo:^{
                    [hud hide];
                    NSMutableDictionary *dictionary = [NSMutableDictionary dictionary];
                    [dictionary setObject:[RRTools nvl:self.dataDic[@"userName"]] forKey:@
                     "userName"];
                    [dictionary setObject:[RRTools nvl:self.dataDic[@"password"]] forKey:@"password"];
                    RRTempDeviceLoginVC *tempLoginVC = [[RRTempDeviceLoginVC alloc]init];
                    tempLoginVC.dataDic = dictionary;
                    [self.navigationController pushViewController:tempLoginVC animated:YES];
                }];
            }else{
                [RRTools getMainQueueDo:^{//登录报错
                    [hud hide];
                    [RRTools showTips:msg];
                }];
            }
        }
    } failure:^(NSError *error) {
        [hud hide];
    } time:60 encrypt:YES];
     */
}

#pragma mark- 重写方法
-(void)backToPuper
{
    [self doneBtnClick];
}
//继承的方法实现，语言更改后，页面需要刷新的内容
-(void)languageChange:(NSNotification*)notification
{
    //将observer收到通知后的方法，放在子线程中执行，这种方式接收到通知还是同步，但是接收到通知后的处理改成异步
    //    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
    //
    //    });
}

#pragma mark- 页面生命周期/布局约束方法
- (void)viewDidLoad {
    self.hasLeftBtn = YES;
    self.cannotSlideBack = YES;
    [super viewDidLoad];
    if([@"junior" isEqualToString:self.from]){//亲子注册流程-Registration;
        self.titleString = TRANSLATE(@"mrechant_btn_register_junior_account");
    } else {
        self.titleString = TRANSLATE(@"register_title_user_registration");
    }
    [self setUp];
    [self addViewConstraints];
}
-(void)setUp
{
    [self.view addSubview:self.processView];
    [self.view addSubview:self.statusLabel];
    if(![self.dataDic[@"userStatus"] isEqualToString:@"0"]){
        [self.view addSubview:self.tipLabel];
        self.statusLabel.textAlignment = NSTextAlignmentLeft;
    } else {
        self.statusLabel.textAlignment = NSTextAlignmentCenter;
        self.statusLabel.text = TRANSLATE(@"register_label_register_success");
    }
    [self.view addSubview:self.doneBtn];
}

-(void)addViewConstraints
{
    [self.processView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.headerView.mas_bottom);
        make.centerX.mas_equalTo(self.view.mas_centerX);
        make.width.mas_equalTo(VIEW_WIDTH);
        make.height.mas_equalTo(44*AUTO_SIZE);
    }];
    [self.statusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.processView.mas_bottom).offset(16*AUTO_SIZE);
        make.left.mas_equalTo(15*AUTO_SIZE);
        make.right.mas_equalTo(-15*AUTO_SIZE);
    }];
    if(![self.dataDic[@"userStatus"] isEqualToString:@"0"]){
        [self.tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(15*AUTO_SIZE);
            make.right.mas_equalTo(-15*AUTO_SIZE);
            make.top.mas_equalTo(self.statusLabel.mas_bottom).offset(10*AUTO_SIZE);
        }];
    }
    [self.doneBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(15);
        make.right.mas_equalTo(-15);
        make.top.mas_equalTo(self.processView.mas_bottom).offset(427*AUTO_SIZE);
        make.height.mas_equalTo(40*AUTO_SIZE);
    }];
}


@end
