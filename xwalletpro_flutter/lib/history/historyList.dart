import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xwalletpro_flutter/history/historyListDetails.dart';
// import 'package:xwalletpro_flutter/rrnetwork.dart';

class historyList extends StatefulWidget {
  historyList({Key? key}) : super(key: key);

  _historyListState createState() => _historyListState();
}

class _historyListState extends State<historyList> {
  @override
  //当前是否是空白的
  bool _isBlank = true;
  //表尾标记
  static const loadingTag = "##loading##";
  // var _data = <Map>[
  //   {"receiver": loadingTag}
  // ];
  var _data = <Map>[
    {
      "date": "02-12",
      "receiver": "Paid For Order at YKKO",
      "payType": "Top up",
      "time": "Dec 20, 2018",
      "amount": "+212,000.00",
      "status": "Completed"
    },
    {
      "date": "02-12",
      "receiver": "Paid For Order at YKKO",
      "payType": "Top up",
      "time": "Dec 20, 2018",
      "amount": "+212,000.00",
      "status": "Completed"
    },
    {
      "date": "02-13",
      "receiver": "Paid For Order at YKKO",
      "payType": "Top up",
      "time": "Dec 20, 2018",
      "amount": "+212,000.00",
      "status": "Completed"
    },
    {
      "date": "02-14",
      "receiver": "Paid For Order at YKKO",
      "payType": "Top up",
      "time": "Dec 20, 2018",
      "amount": "+212,000.00",
      "status": "Completed"
    },
    {
      "date": "02-15",
      "receiver": "Paid For Order at YKKO",
      "payType": "Top up",
      "time": "Dec 20, 2018",
      "amount": "+212,000.00",
      "status": "Completed"
    },
  ];
  ScrollController _scrollController = new ScrollController();
  var load = false;
  String datestring = "";
  String date = "";
  bool end = false;
  int psize = 10;
  bool init = true;
  List pickerdata = [];
  List<int> v = [0, 0];
  List _datelist(DateTime start) {
    List end = [];
    DateTime now = DateTime.now();
    var month = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"];
    var newmonth = [
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "10",
      "11",
      "12"
    ];
    var startmonth = [
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "10",
      "11",
      "12"
    ];

    startmonth.removeRange(0, start.month - 1);
    end.add({"${start.year}": startmonth});
    for (int s = start.year + 1; s < now.year; s++) {
      end.add({"${s}": month});
    }
    newmonth.removeRange(now.month, 12);
    end.add({"${now.year}": newmonth});
    return end;
  }

  @override
  initState() {
    super.initState();

    var now = DateTime.now();
    datestring = "${now.year}-${now.month < 10 ? 0 : ''}${now.month}-";
    date = "${now.year}-${now.month < 10 ? 0 : ''}${now.month}";
    pickerdata = _datelist(DateTime(now.year - 1));
    var d = pickerdata[pickerdata.length - 1]["${now.year}"].length - 1;
    var w = pickerdata.length - 1;
    v = [w, d];

    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        if (!load) loading(); // 当滑到最底部时调用
      }
    });
    // widget.notifierData.addListener(_handleValueChanged);

    // Api.getAccList(
    //   {},
    //   context,
    // ).then((d) {
    //   if (d == null) if (mounted)
    //     setState(() {
    //       end = true;
    //     });
    //   if (d != null) {
    //     cardlist = d;
    //     List<DropdownMenuItem> s = [];
    //     if (value == "") {
    //       value = cardlist[0]["currency"];
    //     }
    //     for (int x = 0; x < cardlist.length; x++) {
    //       String acc = cardlist[x]["acc"];
    //       s.add(DropdownMenuItem(
    //         value: cardlist[x]["currency"],
    //         child: Text(
    //             cardlist[x]["currencyShow"] + "(${cardlist[x]["accDesc"]})"),
    //       ));
    //     }
    //     if (this.mounted) if (mounted)
    //       setState(() {
    //         items = s;
    //       });
    //     refresh();
    //   }
    // });
    // itemTextstyle = TextStyle(
    //     fontSize: ScreenUtil.getInstance().setWidth(28), color: Colors.black);
  }

  Future<void> refresh() async {
    if (mounted)
      setState(() {
        _data = [];
        end = false;
        load = true;
      });
    // return Api.getAccDeatil(
    //   {
    //     "currency": value,
    //     "startDate": datestring + "01",
    //     "endDate": datestring + "31",
    //     "pageNum": 1,
    //     "pageSize": psize
    //   },
    //   context,
    // ).then((d) {
    //   if (mounted)
    //     setState(() {
    //       init = false;
    //       load = false;
    //     });
    //   if (d != null) {
    //     if (d[0]["accountTxHistorys"].length < psize) end = true;
    //     if (mounted)
    //       setState(() {
    //         data = d[0]["accountTxHistorys"];
    //       });
    //   }
    // });
  }

  Future<void> loading() async {
    if (end) return;
    if (mounted)
      setState(() {
        load = true;
      });
    var p =
        (_data.length / psize).toInt() + ((_data.length % psize) > 0 ? 2 : 1);
    // return Api.getAccDeatil(
    //   {
    //     "currency": value,
    //     "startDate": datestring + "01",
    //     "endDate": datestring + "31",
    //     "pageNum": p,
    //     "pageSize": psize
    //   },
    //   context,
    // ).then((d) {
    //   if (mounted)
    //     setState(() {
    //       load = false;
    //     });
    //   if (d != null) {
    //     if (d[0]["accountTxHistorys"].length < psize) end = true;
    //     if (mounted)
    //       setState(() {
    //         data.addAll(d[0]["accountTxHistorys"]);
    //       });
    //   }
    // });
  }

  Widget _buildLoading() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        Container(
          padding: EdgeInsets.only(
              right: ScreenUtil().setWidth(20), top: ScreenUtil().setWidth(8)),
          child: CircularProgressIndicator(),
        ),
        Text("加载中...")
      ],
    );
  }

  Widget _buildEnd() {
    return Container();
  }

  bool add = false;

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(

        ///屏幕适配
        designSize: const Size(750, 1334),
        builder: () {
          return Scaffold(
            appBar: AppBar(
              elevation: 0.0,
              title: Text(
                "Transaction History",
                style: TextStyle(
                  fontSize: ScreenUtil().setWidth(36),
                  color: Color(0xFF0F0F0F),
                  fontWeight: FontWeight.w500,
                ),
              ),
              leading: IconButton(
                icon: Icon(Icons.menu),
                onPressed: () {
                  print('menu');
                },
              ),
              backgroundColor: Colors.white,
            ),
            body: Column(
              children: <Widget>[
                // _blank(),
                Container(
                  margin: EdgeInsets.only(top: 0.0),
                  // constraints: BoxConstraints.tightFor( height: ScreenUtil.getInstance().setWidth(89)),
                  child: _filter(),
                ),
                Container(
                  width: ScreenUtil().setWidth(750),
                  height: ScreenUtil().setWidth(1334 - 218),
                  child: ListView.builder(
                      padding: EdgeInsets.only(top: 0.0),
                      itemCount: _data.length,
                      itemBuilder: (BuildContext context, int index) {
                        if (index == 0 ||
                            _data[index]["date"] != _data[index - 1]["date"]) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Container(
                                padding: EdgeInsets.only(
                                    top: ScreenUtil().setWidth(25),
                                    bottom: ScreenUtil().setWidth(25),
                                    left: ScreenUtil().setWidth(30)),
                                color: Color(0xFFF5F7F8),
                                child: Text(
                                  _data[index]["date"],
                                  style: TextStyle(
                                      fontSize: ScreenUtil().setSp(24),
                                      color: Color(0xFF666666)),
                                ),
                              ),
                              _cell(context, index, true, _data),
                            ],
                          );
                        } else {
                          return _cell(context, index, true, _data);
                        }
                        //如果到了表尾
                        // if (_data[index]["receiver"] == loadingTag) {
                        //   //不足100条，继续获取数据
                        //   _retrieveData();
                        //   if (_data.length - 1 < 100) {
                        //     //获取数据
                        //     _retrieveData();
                        //     if(index == 3){
                        //       return new Container(
                        //         height: 40.0,
                        //         color:Colors.green,
                        //         child: new Center(
                        //           child: new Text("类型1"),
                        //         ),
                        //       );
                        //     }else{
                        //       //加载时显示loading
                        //       return Container(
                        //         padding: const EdgeInsets.all(10.0),
                        //         alignment: Alignment.center,
                        //         child: SizedBox(
                        //             width: 24.0,
                        //             height: 24.0,
                        //             child:
                        //                 CircularProgressIndicator(strokeWidth: 2.0)),
                        //       );
                        //     }

                        //   } else {
                        //     //已经加载了100条数据，不再获取数据。
                        //     return Container(
                        //         alignment: Alignment.center,
                        //         padding: EdgeInsets.all(16.0),
                        //         child: Text(
                        //           "没有更多了",
                        //           style: TextStyle(color: Colors.grey),
                        //         ));
                        //   }
                        // }
                        //显示列表项
                        // return _cell(index, true, _data);
                      }),
                ),
              ],
            ),
          );
        });
  }

  void _retrieveData() {
    Future.delayed(Duration(seconds: 2)).then((e) {
      var element = {
        "receiver": "Paid For Order at YKKO",
        "payType": "Top up",
        "time": "Dec 20, 2018",
        "amount": "+212,000.00",
        "status": "Completed"
      };
      for (int i = 0; i < 3; i++) {
        _data.insert(_data.length - 1, element);
      }
      setState(() {
        //重新构建列表
      });
    });
  }
}

Widget _filter() {
  return Row(
    mainAxisAlignment: MainAxisAlignment.start,
    children: <Widget>[
      GestureDetector(
        onTap: () {
          print("456");
          // rrnetwork.register(appId:"1234444");
        },
        child: Container(
          decoration: BoxDecoration(
              //背景装饰
              color: Colors.white,
              border: new Border.all(width: 0.5, color: Color(0xFFE5E5E5))),
          constraints: BoxConstraints.tightFor(
              width: ScreenUtil().setWidth(375),
              height: ScreenUtil().setWidth(89)),
          child: Row(
            children: <Widget>[
              Padding(
                padding: EdgeInsets.only(left: 15),
                child: Image(
                  width: ScreenUtil().setWidth(48),
                  height: ScreenUtil().setHeight(48),
                  image: AssetImage("assets/images/icon_filter_0.png"),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 10),
                child: Text(
                  "Filter",
                  style: TextStyle(
                    color: Color(0xFF9C9C9C),
                    fontSize: ScreenUtil().setSp(28),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      GestureDetector(
        onTap: () {
          // print("123");
          //调用原生插件
          // Rxnetwork.register(appId:"wxd930ea5d5a258f4f",doOnIOS:true);
        },
        child: Container(
          decoration: BoxDecoration(
              //背景装饰
              color: Colors.white,
              border: new Border.all(width: 0.5, color: Color(0xFFE5E5E5))),
          constraints: BoxConstraints.tightFor(
              width: ScreenUtil().setWidth(375),
              height: ScreenUtil().setWidth(89)),
          child: Row(
            children: <Widget>[
              Padding(
                padding: EdgeInsets.only(left: 15),
                child: Text(
                  "MM/YY",
                  style: TextStyle(
                    color: Color(0xFF9C9C9C),
                    fontSize: 14.0,
                  ),
                ),
              ),
            ],
          ),
        ),
      )
    ],
  );
}

Widget _blank() {
  return Container(
    width: ScreenUtil().setWidth(375),
    height: ScreenUtil().setWidth(1334 - 129),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: <Widget>[
        Container(
          margin: EdgeInsets.only(top: 184),
          child: Image(
            width: ScreenUtil().setWidth(345),
            height: ScreenUtil().setHeight(232),
            image: AssetImage("assets/images/img_nohistory.png"),
          ),
        ),
        Container(
          margin: EdgeInsets.only(top: 10),
          height: ScreenUtil().setHeight(22.5),
          child: Text(
            'No record！',
            style: TextStyle(
              color: Color(0xFF212121),
              fontSize: 16.0,
            ),
          ),
        ),
        Container(
          margin: EdgeInsets.only(top: 10),
          child: Text(
            'You can use other functions',
            style: TextStyle(
              color: Color(0xFF9C9C9C),
              fontSize: 14.0,
            ),
          ),
        ),
      ],
    ),
  );
}

Widget _cell(
    BuildContext context, int index, bool isShowBottomLine, List data) {
  // print(data);
  return GestureDetector(
    onTap: () {
      print("$index");
      Navigator.push(context, MaterialPageRoute(builder: (context) {
        return historyListDetails(data[index]);
      }));
    },
    child: new Container(
      color: Colors.white,
      child: new Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          new Container(
            margin: new EdgeInsets.all(0.0),
            height: (isShowBottomLine ? 80.5 : 81.5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Row(
                  children: <Widget>[
                    Padding(
                      padding: EdgeInsets.only(left: 15.0),
                      child: Image(
                        width: 28,
                        height: 28,
                        image:
                            AssetImage("assets/images/icon_Fund Transfer.png"),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 5.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Padding(
                            padding: EdgeInsets.only(top: 10.0),
                            child: Text(
                              data[index]["receiver"],
                              style: TextStyle(
                                color: Color(0xFF212121),
                                fontSize: 14.0,
                              ),
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 5.0),
                            child: Text(
                              data[index]["payType"],
                              style: TextStyle(
                                color: Color(0xFF212121),
                                fontSize: 14.0,
                              ),
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 5.0),
                            child: Text(
                              data[index]["time"],
                              style: TextStyle(
                                color: Color(0xFF9C9C9C),
                                fontSize: 12.0,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                Align(
                  alignment: Alignment.centerRight,
                  child: Padding(
                    padding: EdgeInsets.only(right: 15.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: <Widget>[
                        Padding(
                          padding: EdgeInsets.only(top: 16.5),
                          child: Text(
                            data[index]["amount"],
                            style: TextStyle(
                              color: Color(0xFF188F86),
                              fontSize: 14.0,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(top: 5.0),
                          child: Text(
                            data[index]["status"],
                            style: TextStyle(
                              color: Color(0xFF9C9C9C),
                              fontSize: 12.0,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          _bottomLine(isShowBottomLine),
        ],
      ),
    ),
  );
}

Widget _bottomLine(bool isShowBottomLine) {
  if (isShowBottomLine) {
    return new Container(
      margin: new EdgeInsets.only(left: 15.0, right: 15.0),
      child: new Divider(height: 0.5, color: Color(0xFFE5E5E5)),
    );
  }
  return Container();
}
