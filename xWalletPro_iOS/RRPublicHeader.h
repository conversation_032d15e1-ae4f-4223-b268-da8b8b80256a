//
//  RRPublicHeader.h
//  xmobile4.0
//
//  Created by <PERSON> on 12/16/19.
//  Copyright © 2019 Shirley. All rights reserved.
//

#ifndef RRPublicHeader_h
#define RRPublicHeader_h
#import "AppDelegate.h"
#import "UIDevice+VGAddition.h"
#pragma mark - 颜色值
#define UIColorFromRGB(rgbValue) [UIColor colorWithRed:((float)((rgbValue & 0xFF0000) >> 16))/255.0 green:((float)((rgbValue & 0xFF00) >> 8))/255.0 blue:((float)(rgbValue & 0xFF))/255.0 alpha:1.0]
#define COLOR(r,g,b) [UIColor colorWithRed:r/255.0 green:g/255.0 blue:b/255.0 alpha:1.0]
#define COLORA(r,g,b,a) [UIColor colorWithRed:r/255.0 green:g/255.0 blue:b/255.0 alpha:a/1.0]
#define MAIN_BLUE COLOR(243,136,30)
#define MAIN_BLUE_BG COLOR(253, 243, 232)
#define VC_BG_COLOR COLOR(247,248,250)
#define VIEW_FLOAT_COLOR COLOR(242,243,245)
#define VIEW_DEEP_COLOR COLOR(229,230,235)
#define VIEW_UNABLE_COLOR COLOR(245,247,258)
#define VIEW_COLOR COLOR(255,255,255)
#define VIEW_SPECIAL_COLOR COLOR(201,205,212)
#define Line_COLOR COLOR(229,230,235)
#define TIP_LABEL_COLOR COLOR(201, 205, 212)
#define INF_LABEL_COLOR COLOR(134, 144, 156)
#define MAIN_TEXT_COLOR COLOR(29, 33, 41)
#define SUB_TEXT_COLOR COLOR(78, 89, 105)
#define BTN_HIGH_COLOR COLOR(243,136,30)
#define BTN_DARK_COLOR COLORA(243,136,30,0.25)
#define BTN_TITLE_COLOR COLOR(255,255,255)
#define GREEN_COLOR COLOR(0, 165, 62)
#define RED_COLOR COLOR(242, 61, 29)

// View 圆角
#define ViewRadius(View, Radius)\
\
[View.layer setCornerRadius:(Radius)];\
[View.layer setMasksToBounds:YES]
//View 圆角和加边框
#define ViewBorderRadius(View, Radius, Width, Color)\
\
[View.layer setCornerRadius:(Radius)];\
[View.layer setMasksToBounds:YES];\
[View.layer setBorderWidth:(Width)];\
[View.layer setBorderColor:[Color CGColor]]
#define kLineOnePxHeight   (1.0/[[UIScreen mainScreen] scale])
#define JWCellDefaultFrame CGRectMake(0, 0, VIEW_WIDTH, 50)

#pragma mark - 设备信息
/** 获取bundle Id信息*/
#define kGetBundleId        [[NSBundle mainBundle] bundleIdentifier]
/** 获取 App 名称*/
#define kGetAppDisplayName  [[[NSBundle mainBundle] bundleIdentifier] objectForKey:@"CFBundleDisplayName"]
/** APP版本号 Version*/
#define kAppVersion         [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"]
/** APPbuild版本号 BundleVersion*/
#define kAppBundle          [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleVersion"]
/** 获取设备名称：手机别名(即：用户定义的名称)*/
#define kGetDeviceName      [[UIDevice currentDevice] name]
/** 获取设备类型*/
#define kGetDeviceModel     [[UIDevice currentDevice] model]
/** 获取设备 UUID*/
#define kGetDeviceUUID      [[UIDevice currentDevice].identifierForVendor UUIDString]
/** 获取系统名称*/
#define kSystemName         [[UIDevice currentDevice] systemName]
/** 系统版本号*/
#define kSystemVersion      [[UIDevice currentDevice] systemVersion]
/** 获取地方型号(即：国际化区域名称)*/
#define kLocalPhoneModel    [[UIDevice currentDevice] localizedModel]
/** 获取当前语言*/
#define kCurrentLanguage    ([[NSLocale preferredLanguages] objectAtIndex:0])
/** 获取沙盒 Document 路径*/
#define kDocumentPath       [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject]
/** 获取沙盒 Library 路径*/
#define kLibraryPath        [NSSearchPathForDirectoriesInDomains(NSLibraryDirectory, NSUserDomainMask, YES) lastObject]
/** 获取沙盒 temp 路径(注:iPhone 重启会清空)*/
#define kTempPath           NSTemporaryDirectory()
/** 获取沙盒 Cache 路径*/
#define kCachePath          [NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES) lastObject]
/** 获取程序包中程序路径*/
#define kResource(f, t)     [[NSBundle mainBundle] pathForResource:(f) ofType:(t)];
/** 获取系统时间戳*/
#define getCurrentTime      [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]]
/** 屏幕分辨率*/
#define SCREEN_RESOLUTION   (SCREEN_WIDTH * SCREEN_HEIGHT * ([UIScreen mainScreen].scale))

//密码相关
//RSA密钥
#define FIRST @"446879154c5d0547092e76315292740dd3e291e9a5e69a6882b54f8424ad966b15878963ecd73ae955b693a6e6281018c1383d8044f2b0d2d109a18fa89ce6d7f71ca48b51b5072be7098703411ebac8c13a9a276052e4efc4f573f9fb3fab9d"
#define SECOND @"257b5e8349350dd1fc67125d8f499f5e7269e9a9527b4330840a5c19fb89004541d1d2fb4dfe3e8e46a0500a0edb94e0763230d27c79724e62f361b879b4f789f1304ccf455c2d09090c6a52fb7bd515b083e7274c71b4b329cb8a087c9f247f86191e0f727e587775414c6fa2bd96ea106fe058c98207ac670b9ce6d3e6a56dcfa67d919a9ff686277bead2b538376e"
#define KEYSTR @"************"

#pragma mark - 窗口变量
#define TOPWINDOW [[[UIApplication sharedApplication] delegate] window]

#define APPDELEGATE [[UIApplication sharedApplication] delegate]

#define GETTHEAPP AppDelegate * theApp = (AppDelegate* )[[UIApplication sharedApplication] delegate];

#pragma mark - 适配相关
//设备相关
#define VIEW_WIDTH [UIScreen mainScreen].bounds.size.width
#define VIEW_HEIGHT [UIScreen mainScreen].bounds.size.height
#define VERSION [UIDevice currentDevice].systemVersion.doubleValue
#define AUTO_SIZE [RRTools autoSize]
#define FONT_SIZE [RRTools fontSize]
//默认字体字号
#define font_Size (14*FONT_SIZE)
#define TRANSLATE(string) NSLocalizedString(string, nil)
#define FONT(size) [UIFont systemFontOfSize:size]
#define MEDIUMFONT(size) [UIFont systemFontOfSize:size weight:UIFontWeightMedium]
#define SEMIBOLDFONT(size) [UIFont systemFontOfSize:size weight:UIFontWeightSemibold]
#define BOLDFONT(size) [UIFont systemFontOfSize:size weight:UIFontWeightBold]
// 判断是否是ipad
#define isPad ([[UIDevice currentDevice] userInterfaceIdiom] == UIUserInterfaceIdiomPad)
#define IS_X ({ \
    BOOL isIPhoneXOrLater = NO; \
    if (@available(iOS 11.0, *)) { \
        UIWindow *window = UIApplication.sharedApplication.windows.firstObject; \
        if (window) { \
            UIEdgeInsets safeAreaInsets = window.safeAreaInsets; \
            isIPhoneXOrLater = safeAreaInsets.top > 20.0; \
        } \
    } \
    isIPhoneXOrLater; \
})
#define Height_StatusBar [UIDevice vg_statusBarHeight]

#define Height_NavBar [UIDevice vg_navigationFullHeight]

#define Height_TabBar [UIDevice vg_tabBarFullHeight]

#define bottomHeight [UIDevice vg_safeDistanceBottom]

#define statusHeight [UIDevice vg_safeDistanceTop]

#define statusNavYHeight [UIDevice navY]
// 安全区域底部高度
#define kSafeAreaBottomHeight \
({\
    CGFloat height = 0.0;\
    if (@available(iOS 11.0, *)) {\
        UIEdgeInsets insets = [UIApplication sharedApplication].delegate.window.safeAreaInsets;\
        height = insets.bottom;\
    }\
    height;\
})

#pragma mark - 定义通知名
//语言更改通知
#define AppLanguageDidChangeNotification @"AppLanguageDidChangeNotification"
//收银台刷新支付方式列表
#define RefreshPayMethodListNotification @"RefreshPayMethodListNotification"
//找回密码成功通知
#define RetrievePayPwdSuccess @"RetrievePayPwdSuccess"
//订单二维码支付通知
#define OrderQrcodePay @"OrderQrcodePay"
//处理推送消息通知
#define HandleRemoteNotification @"HandleRemoteNotification"

#pragma mark - 定义存储健名
//定义存储key值的宏定义
//#define SDKRSAPUBLICKEY @"SDKRSAPUBLICKEY"

//标识是是否是第三方app跳转过来的
#define ISAPPPAY @"isAppPay"
//标识是否是启动后直接进入的首页,及是否强制验证指纹
#define NEEDVERIFY @"needVerify"
//手机区号存储key
#define PHONECODE_EN @"phoneCodeArr_en"
#define PHONECODE_ZH @"phoneCodeArr_zh"
#define PHONECODE_RU @"phoneCodeArr_ru"
#define PHONECODE_SN @"phoneCodeArr_sn"
#define PHONECODE_NR @"phoneCodeArr_nr"
//存储应用版本号
#define LOCAL_VERSION @"localVersion"

//存储默认支付方式
#define DEFAULT_PAY_METHOD @"defaultPayMehodModel"

#pragma mark - 定义宏常量
//定义存储key值的宏定义
#define SDKRSAPUBLICKEY @"SDKRSAPUBLICKEY"
#define BUNDLEIDENTIFIER @"zw.co.onemoney"
//默认手机区号
#define DEFAULT_MOBILEHEX @"263"
//金额限制位数
#define AMTLENGTH 10
//应用名称展示
#define parentAppName @"OneMoney Mobile"
//金额币种
#define CURRENCY [RRTools strForKey:@"CURRENCY"] && [[RRTools strForKey:@"CURRENCY"] length] > 0 ? [RRTools strForKey:@"CURRENCY"]:@"ZWG"
#define CURRENCYARRAY [RRTools arrayForKey:@"currencyList"] ? [RRTools arrayForKey:@"currencyList"]:[NSMutableArray arrayWithObjects:@"ZWG", @"USD",@"ZAR", @"GBP",@"EUR", nil]
//前端过期时间，用于弹出指纹验证，单位为秒
#define OVERTIME 5.0
//安装appstore的地址
#define APPStoreStr @"https://apps.apple.com/cn/app/sarawak-pay/id1250845042"

#define SERVICE_TIME @"10:00 - 18:00"
#define PHONE_NUM @"121"
#define TERMCONDITIONS @"https://onemoney.co.zw/privacypolicy.html"
#define PRIVACY @"https://onemoney.co.zw/privacypolicy.html"

#define CASH_OBJECT_NAME @"inviteFriends"

//环境配置文件
//开发环境
//#import "configDev.h"
//测试环境
//#import "configTest.h"
//验收环境
//#import "configFat.h"
//演示环境                                                                   
//#import "configUat.h"
//生产环境
#import "configPro.h"

#import "NSBundle+XNCategory.h"
#import "UIButton+Category.h"
#import <MJRefresh.h>
//Model类
#import "RRModels.h"
#import "RRHUDView.h"
#import "RRTools.h"
#import "RRRequestCode.h" 
#import "RRHttpRequest.h"
#import "RRNavigationController.h"
//View设置单边圆角
#import "UIView+CornerRadius.h"
#import <Flutter/Flutter.h>
//如果用到flutter插件时,导入下面头文件
#import <FlutterPluginRegistrant/GeneratedPluginRegistrant.h>

#endif /* RRPublicHeader_h */
