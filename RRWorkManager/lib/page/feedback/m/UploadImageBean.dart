class UpLoadImageBean {
  String? fileBatchId;
  String? fileUrl;
  String? fileName;

  UpLoadImageBean({
    this.fileBatchId,
    this.fileUrl,
    this.fileName
  });

  UpLoadImageBean.fromJson(Map<String, dynamic> json) {
    fileBatchId = json['fileBatchId'];
    fileUrl = json['fileUrl'];
    fileName = json['fileName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['fileBatchId'] = fileBatchId;
    data['fileUrl'] = fileUrl;
    data['fileName'] = fileName;
    return data;
  }
}