// ignore_for_file: avoid_print, null_argument_to_non_null_type

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:rrworkmanager/common_widget/LoadingDialog.dart';
import 'package:rrworkmanager/model/EntityFactory.dart';
import 'package:rrworkmanager/net/HttpUtils.dart';
import 'package:rrworkmanager/net/file_interceptors.dart';
import 'package:rrworkmanager/rrworkmanagerplugin.dart';
import 'package:rrworkmanager/utils/StringUtils.dart';

import 'LoginInterceptors.dart';

///网络请求工具类
class HttpFileUtils {
  static Dio? _dio;
  static final HttpFileUtils _http = HttpFileUtils();

  static HttpFileUtils getInstance() {
    return _http;
  }

  HttpFileUtils() {
    _dio ??= createDio();
  }

  ///创建Dio实例
  Dio createDio() {
    var dio = Dio(BaseOptions(
      ///链接超时
      connectTimeout: 60000,

      ///读取超时
      receiveTimeout: 60000,

      ///服务端地址
      baseUrl: RRWorkManagerPlugin.baseUrl,

      ///发送超时
      sendTimeout: 60000,
    ));

    //添加拦截器
    dio.interceptors.addAll([LogInterceptors(), FileInterceptors()]);
    return dio;
  }

/*
   * 上传文件
   * 注：file是服务端接受的字段字段，如果接受字段不是这个需要修改
   */
  Future<Response> uploadFile<T>(BuildContext context, String url,
      Map<String, dynamic> requestMap, Success<T> success, Fail fail,
      [bool isJson = true, bool isShowLoading = false]) async {

    print("请求的参数==isShowLoading=======$requestMap==$isShowLoading==");
    if (isShowLoading) {
      //显示dialog
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            // return LoadingDialog(text: "加载中...");
            return const LoadingDialog();
          });
    }

    try {
      String? filePath;
      String? fileName;
      if (requestMap.isNotEmpty &&
          requestMap.containsKey("filePath") &&
          requestMap.containsKey("fileName")) {
        filePath = requestMap["filePath"];
        fileName = requestMap["fileName"];
      }

      var postData = FormData.fromMap({
        "file": await MultipartFile.fromFile(filePath!, filename: fileName)
      }); //file是服务端接受的字段字段，如果接受字段不是这个需要修改
      var option = Options(
          method: "POST",
          contentType: "multipart/form-data"); //上传文件的content-type 表单
    } on DioError catch (e) {
      if (isShowLoading) {
        Navigator.pop(context);
      }
      fail(tr("common_network_error"), e.toString());
      return Future.value();
    }

    // print("响应结果$response");
    print("isShowLoading $isShowLoading  context  $context ");
    if (isShowLoading) {
      Navigator.pop(context);
    }
    return Future.value();
  }

  /*
   * 下载文件
   */
  Future<void> downloadFile(BuildContext context, String url,
      Map<String, dynamic> requestMap, Function(String) success, Fail fail,
      [bool isJson = true, bool isShowLoading = true]) async {
    print("请求的参数==isShowLoading=======$requestMap==$isShowLoading==");
    if (isShowLoading) {
      //显示dialog
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return const LoadingDialog();
          });
    }
    try {
      String? fileSavePath;
      if (requestMap.isNotEmpty && requestMap.containsKey("fileSavePath")) {
        fileSavePath = requestMap["fileSavePath"];
      }

      print("=====fileSavePath=======$fileSavePath==");

      _dio!.download(url, fileSavePath, data: requestMap,
          onReceiveProgress: (int loaded, int total) {
        print("下载进度：" + (loaded / total * 100).toString() + "%");
      }).then((response) async {
        print("=======then=======then==============");
      }).whenComplete(() {
        print("=======whenComplete=======下载完成==============");
        print("isShowLoading $isShowLoading  context  $context ");
        if (isShowLoading) {
          Navigator.pop(context);
        }
        success(fileSavePath!);
              return Future.value();
      });
    } on DioError catch (e) {
      print("-------错误信息--------------------e-----------");
      if (isShowLoading) {
        Navigator.pop(context);
      }
      fail(trans("common_network_error"), e.toString());
      return Future.value();
    }
  }

  /*
   * 上传图片
   * 注：file是服务端接受的字段字段，如果接受字段不是这个需要修改
   */
  Future<Response?>? uploadImage<T>(BuildContext context, String url,
      Map<String, dynamic> requestMap, Success<T>? success, Fail? fail,
      [bool isJson = true, bool isShowLoading = true]) async {
    Response response;

    print("请求的参数==isShowLoading=======$requestMap==$isShowLoading==");
    if (isShowLoading) {
      //显示dialog
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            // return LoadingDialog(text: "加载中...");
            return const LoadingDialog();
          });
    }

    try {
      String? filePath;
      String? fileName;
      String? suffix;
      if (requestMap.isNotEmpty && requestMap.containsKey("file")) {
        filePath = requestMap["file"];
        fileName =
            filePath!.substring(filePath.lastIndexOf("/") + 1, filePath.length);
        suffix =
            fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length);
      }

      var postData = FormData.fromMap({
        "file": await MultipartFile.fromFile(filePath!, filename: fileName)
      }); //file是服务端接受的字段字段，如果接受字段不是这个需要修改
      var option = Options(
        method: "POST",
        contentType: _dio!.options.contentType = "image/$suffix",
      ); //上传文件的content-type 表单
      response = await _dio!.post(
        url,
        data: postData,
        options: option,
        // onSendProgress: (int sent, int total) {
        //   print("上传进度：" + (sent / total * 100).toString() + "%"); //取精度，如：56.45%
        // },
      );
    } on DioError catch (e) {
      if (isShowLoading) {
        Navigator.pop(context);
      }
      fail!(trans("common_network_error"), e.toString());
      return Future.value();
    }
    // print("响应结果$response");
    print("isShowLoading $isShowLoading  context  $context ");
    if (isShowLoading) {
      Navigator.pop(context);
    }

    if (response.statusCode == 200) {
      Map<String, dynamic> data = response.data;
      String status = data["status"];
      if (status == "0") {
        //成功
        if (success != null) {
          T t = EntityFactory.generateOBJ<T>(data["data"])!;
          success(t);
        }
      } else {
        fail!(data["message"], data["status"]);
      }
    } else {
      //失败
      if (fail != null) {
        fail(response.statusMessage, response.statusCode.toString());
      }
    }
    return Future.value();
  }
}
