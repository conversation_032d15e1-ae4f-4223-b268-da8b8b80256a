// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		6820F13B98DEA44D6699052C /* libPods-xWalletPro_iOS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = CCB4995D92C61B68DB390649 /* libPods-xWalletPro_iOS.a */; };
		690A07962CDFF3B6005C1EE4 /* CustomMJRefreshNormalHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 690A07952CDFF3B6005C1EE4 /* CustomMJRefreshNormalHeader.m */; };
		690A079A2CE00AA8005C1EE4 /* YJCouponCenterSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 690A07982CE00AA8005C1EE4 /* YJCouponCenterSDK.framework */; };
		690A079E2CE36DF9005C1EE4 /* resource.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 690A079D2CE36DF9005C1EE4 /* resource.bundle */; };
		690A07A12CE4B624005C1EE4 /* RRCashOutVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 690A07A02CE4B624005C1EE4 /* RRCashOutVC.m */; };
		690A07A42CE7414A005C1EE4 /* RRRegisterMobileVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 690A07A32CE7414A005C1EE4 /* RRRegisterMobileVC.m */; };
		690A07A72CEB3A10005C1EE4 /* RCashOutResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 690A07A62CEB3A10005C1EE4 /* RCashOutResultVC.m */; };
		6928D2952C25586D001D0802 /* UIDevice+VGAddition.m in Sources */ = {isa = PBXBuildFile; fileRef = 6928D2942C25586D001D0802 /* UIDevice+VGAddition.m */; };
		69366F272B70D43C0065BC73 /* RRCheckJuniorAccountVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 69366F262B70D43C0065BC73 /* RRCheckJuniorAccountVC.m */; };
		69366F2A2B70E3C20065BC73 /* RRCheckJuniorAccountCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 69366F292B70E3C20065BC73 /* RRCheckJuniorAccountCell.m */; };
		6936784D2C0482E900817B5A /* RRMerchantCodeView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6936784C2C0482E900817B5A /* RRMerchantCodeView.m */; };
		694BB1FE2CD50D84008989BB /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 694BB1FD2CD50D84008989BB /* GoogleService-Info.plist */; };
		695A24782C05684B00109FF5 /* RRCommonInputView.m in Sources */ = {isa = PBXBuildFile; fileRef = 695A24772C05684B00109FF5 /* RRCommonInputView.m */; };
		695A247E2C056DDD00109FF5 /* RRBillerCodeInputVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 695A247D2C056DDD00109FF5 /* RRBillerCodeInputVC.m */; };
		695A24812C0575B000109FF5 /* RRCommonAmountView.m in Sources */ = {isa = PBXBuildFile; fileRef = 695A24802C0575B000109FF5 /* RRCommonAmountView.m */; };
		695A24842C058CA000109FF5 /* RRCommonSelectView.m in Sources */ = {isa = PBXBuildFile; fileRef = 695A24832C058CA000109FF5 /* RRCommonSelectView.m */; };
		695AED472C22B7B2005A4D31 /* RRResetPINVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 695AED462C22B7B2005A4D31 /* RRResetPINVC.m */; };
		697047BF2B81EC8F009543EC /* RRCheckJuniorAccountDetailsVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 697047BE2B81EC8F009543EC /* RRCheckJuniorAccountDetailsVC.m */; };
		697047C62B82430C009543EC /* RRPersonalInfoAvactorCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 697047C52B82430C009543EC /* RRPersonalInfoAvactorCell.m */; };
		697047C92B82E73B009543EC /* RRJuniorBalanceView.m in Sources */ = {isa = PBXBuildFile; fileRef = 697047C82B82E73B009543EC /* RRJuniorBalanceView.m */; };
		6977AC6D2B39135600879110 /* PaymentOrderVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6977AC6B2B39135600879110 /* PaymentOrderVC.m */; };
		6977AC712B392F1C00879110 /* RRAutomaticDebitVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6977AC702B392F1C00879110 /* RRAutomaticDebitVC.m */; };
		6977AC742B39631100879110 /* RRAutomaticDebitCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6977AC732B39631100879110 /* RRAutomaticDebitCell.m */; };
		698CCF822B67AEA300FFD9CA /* RRJuniorAccountUpgradeVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 698CCF812B67AEA300FFD9CA /* RRJuniorAccountUpgradeVC.m */; };
		698CCF852B67E88C00FFD9CA /* RRJuniorUpgradeFResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 698CCF842B67E88C00FFD9CA /* RRJuniorUpgradeFResultVC.m */; };
		698DCBDD2B679520005D0205 /* RRJuniorAccountDetailVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 698DCBDC2B679520005D0205 /* RRJuniorAccountDetailVC.m */; };
		698DCBE02B679E50005D0205 /* RRJuniorAccountChangeVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 698DCBDF2B679E50005D0205 /* RRJuniorAccountChangeVC.m */; };
		6990B0822B2FDBDF007D917B /* RRPaymentSettingsVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6990B0812B2FDBDF007D917B /* RRPaymentSettingsVC.m */; };
		6990B0852B2FEB4B007D917B /* RRNoPINPaymentVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6990B0842B2FEB4B007D917B /* RRNoPINPaymentVC.m */; };
		6990B0882B302DE6007D917B /* RRNoPINPaymentMangeVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6990B0872B302DE6007D917B /* RRNoPINPaymentMangeVC.m */; };
		6997F4872BA91F70006E4E03 /* RRZipitVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6997F4862BA91F70006E4E03 /* RRZipitVC.m */; };
		6997F48A2BA9CE0D006E4E03 /* RRPaymentAccountSequenceVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6997F4892BA9CE0D006E4E03 /* RRPaymentAccountSequenceVC.m */; };
		6997F48D2BA9D677006E4E03 /* RRAccountSequenceCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6997F48C2BA9D677006E4E03 /* RRAccountSequenceCell.m */; };
		69CDA09B2C1831ED0050440E /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 69CDA0992C1831EC0050440E /* LaunchScreen.storyboard */; };
		69D75DBF2B31A11000C76AE6 /* RRNoPINPaymentResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 69D75DBE2B31A10F00C76AE6 /* RRNoPINPaymentResultVC.m */; };
		69DDF33B2B63A4C2006DAE64 /* RRJuniorAccountVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 69DDF33A2B63A4C2006DAE64 /* RRJuniorAccountVC.m */; };
		69DDF3402B63A5D8006DAE64 /* RRJuniorAccountCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 69DDF33F2B63A5D8006DAE64 /* RRJuniorAccountCell.m */; };
		69E2C77D2B396F2000BE6EB2 /* RRAutomaticDebitDetailVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 69E2C77C2B396F2000BE6EB2 /* RRAutomaticDebitDetailVC.m */; };
		69E2C7802B39753300BE6EB2 /* BuyAirtimeBundleVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 69E2C77F2B39753300BE6EB2 /* BuyAirtimeBundleVC.m */; };
		69E2C7832B39826400BE6EB2 /* BuyAirtimeBundleResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 69E2C7822B39826400BE6EB2 /* BuyAirtimeBundleResultVC.m */; };
		69EF34262B3132DA00ADE484 /* RRPINValidateView.m in Sources */ = {isa = PBXBuildFile; fileRef = 69EF34252B3132DA00ADE484 /* RRPINValidateView.m */; };
		69F138902BDAB4D500E056F5 /* CustomAlertAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 69F1388F2BDAB4D500E056F5 /* CustomAlertAction.m */; };
		69F7DB402B14323200D908D8 /* RRStatementEnquiryVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 69F7DB3F2B14323200D908D8 /* RRStatementEnquiryVC.m */; };
		69F7DB432B143C8200D908D8 /* RRStatementEnquiryCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 69F7DB422B143C8200D908D8 /* RRStatementEnquiryCell.m */; };
		69F7F72B2B70869B003BB98E /* RRSecretWordsVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 69F7F72A2B70869B003BB98E /* RRSecretWordsVC.m */; };
		69FA3E232C18455C005C9650 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 8D02289823E7D394008EB3F4 /* Assets.xcassets */; };
		8D02289123E7D392008EB3F4 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D02289023E7D392008EB3F4 /* AppDelegate.m */; };
		8D02289423E7D392008EB3F4 /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D02289323E7D392008EB3F4 /* ViewController.m */; };
		8D02289723E7D392008EB3F4 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 8D02289523E7D392008EB3F4 /* Main.storyboard */; };
		8D02289F23E7D394008EB3F4 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D02289E23E7D394008EB3F4 /* main.m */; };
		8D0228A923E7D394008EB3F4 /* xWalletPro_iOSTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0228A823E7D394008EB3F4 /* xWalletPro_iOSTests.m */; };
		8D0228B423E7D394008EB3F4 /* xWalletPro_iOSUITests.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0228B323E7D394008EB3F4 /* xWalletPro_iOSUITests.m */; };
		8D0228C723E7DDDD008EB3F4 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 8D0228C923E7DDDD008EB3F4 /* Localizable.strings */; };
		8D0228CD23E7E040008EB3F4 /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 8D0228CF23E7E040008EB3F4 /* InfoPlist.strings */; };
		8D0228DA23E9178D008EB3F4 /* RRHttpRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0228D723E9178D008EB3F4 /* RRHttpRequest.m */; };
		8D0228DD23E918B5008EB3F4 /* SecurityUtility.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0228DC23E918B5008EB3F4 /* SecurityUtility.m */; };
		8D0228E123E91901008EB3F4 /* RRTools.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0228DF23E91901008EB3F4 /* RRTools.m */; };
		8D0228E623E91CF7008EB3F4 /* JSONKit.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0228E423E91CF7008EB3F4 /* JSONKit.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		8D0228EA23E97004008EB3F4 /* RRLoginVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0228E923E97004008EB3F4 /* RRLoginVC.m */; };
		8D0228EE23E97152008EB3F4 /* RRTabbarVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0228ED23E97152008EB3F4 /* RRTabbarVC.m */; };
		8D0228F223E9B400008EB3F4 /* RRHomeVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0228F123E9B400008EB3F4 /* RRHomeVC.m */; };
		8D0228F523E9B492008EB3F4 /* RRNationalBaseVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0228F423E9B492008EB3F4 /* RRNationalBaseVC.m */; };
		8D0228FE23E9B893008EB3F4 /* XNLanguageConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0228FC23E9B893008EB3F4 /* XNLanguageConfig.m */; };
		8D0228FF23E9B893008EB3F4 /* NSBundle+XNCategory.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0228FD23E9B893008EB3F4 /* NSBundle+XNCategory.m */; };
		8D02290223E9B8E8008EB3F4 /* RRLeftVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D02290123E9B8E8008EB3F4 /* RRLeftVC.m */; };
		8D0F93BA23FE52F300B04EAC /* RRSetLoginPwdVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0F93B923FE52F300B04EAC /* RRSetLoginPwdVC.m */; };
		8D0F93C223FE725E00B04EAC /* RRSplitBillResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0F93C023FE725E00B04EAC /* RRSplitBillResultVC.m */; };
		8D0F93C323FE725E00B04EAC /* RRSplitBillVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0F93C123FE725E00B04EAC /* RRSplitBillVC.m */; };
		8D0F93C623FE74F400B04EAC /* RRBillContactListVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0F93C523FE74F400B04EAC /* RRBillContactListVC.m */; };
		8D0F93CB23FEC40000B04EAC /* RRBillPersonAmtView.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0F93CA23FEC40000B04EAC /* RRBillPersonAmtView.m */; };
		8D0F93CE23FF6D2200B04EAC /* RRSplitIndividVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0F93CD23FF6D2200B04EAC /* RRSplitIndividVC.m */; };
		8D0F93D2240010D500B04EAC /* RRPendingListVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0F93D1240010D500B04EAC /* RRPendingListVC.m */; };
		8D0F93D6240011CE00B04EAC /* RRPendHeadView.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0F93D5240011CE00B04EAC /* RRPendHeadView.m */; };
		8D0F93DA24002B7A00B04EAC /* RRLssuedCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0F93D924002B7A00B04EAC /* RRLssuedCell.m */; };
		8D0F93DD24002B9400B04EAC /* RRReceiveCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0F93DC24002B9400B04EAC /* RRReceiveCell.m */; };
		8D0F93E02400D9C000B04EAC /* RRLssuedDetailsVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0F93DF2400D9C000B04EAC /* RRLssuedDetailsVC.m */; };
		8D0F93E32400D9DC00B04EAC /* RRReceivedDetailsVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D0F93E22400D9DC00B04EAC /* RRReceivedDetailsVC.m */; };
		8D10715E253ED10B0075A927 /* RRDeviceModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D10715D253ED10B0075A927 /* RRDeviceModel.m */; };
		8D1071FB254067230075A927 /* AppDelegate+JPushService.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D1071FA254067230075A927 /* AppDelegate+JPushService.m */; };
		8D109D19240C2DBF001261C8 /* RRPaymentQrCodeVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D109D18240C2DBF001261C8 /* RRPaymentQrCodeVC.m */; };
		8D109D20240CE774001261C8 /* RRPaymentVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D109D1F240CE774001261C8 /* RRPaymentVC.m */; };
		8D109D23240CE789001261C8 /* RRPaymentResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D109D22240CE789001261C8 /* RRPaymentResultVC.m */; };
		8D1329972412688700CA6E11 /* RRBankCardListVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D1329962412688700CA6E11 /* RRBankCardListVC.m */; };
		8D13299B24126AB800CA6E11 /* RRBankCardCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D13299A24126AB800CA6E11 /* RRBankCardCell.m */; };
		8D13299E2412815600CA6E11 /* RRAddBankCardVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D13299D2412815600CA6E11 /* RRAddBankCardVC.m */; };
		8D1329A22412880500CA6E11 /* RRBankNameVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D1329A12412880500CA6E11 /* RRBankNameVC.m */; };
		8D1329A5241289B000CA6E11 /* RRBankNameCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D1329A4241289B000CA6E11 /* RRBankNameCell.m */; };
		8D244C322447333D0031595E /* RRFlutterVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D244C312447333D0031595E /* RRFlutterVC.m */; };
		8D25D4CC2466B321009F25BA /* RRUserInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D25D4CB2466B321009F25BA /* RRUserInfo.m */; };
		8D278C5E244F163900DB8DD9 /* RRNetworkModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D278C5D244F163900DB8DD9 /* RRNetworkModel.m */; };
		8D37C9A523EFCFC000E71E5D /* RRXNTextField.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D37C9A423EFCFC000E71E5D /* RRXNTextField.m */; };
		8D37C9AB23EFF44000E71E5D /* RRCommonPickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D37C9A923EFF44000E71E5D /* RRCommonPickerView.m */; };
		8D37C9AF23F0229C00E71E5D /* RRKeyBoardView.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D37C9AD23F0229C00E71E5D /* RRKeyBoardView.m */; };
		8D3F342323EABA6D009063F8 /* RRXNTextView.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D3F342223EABA6D009063F8 /* RRXNTextView.m */; };
		8D3F342723EBC12F009063F8 /* RRRegisterVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D3F342623EBC12F009063F8 /* RRRegisterVC.m */; };
		8D3F342A23EBEDF5009063F8 /* RRXNTipLabel.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D3F342923EBEDF5009063F8 /* RRXNTipLabel.m */; };
		8D3F343123EC3357009063F8 /* RRXNButton.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D3F343023EC3356009063F8 /* RRXNButton.m */; };
		8D3F343423EC6EFC009063F8 /* RRVerifyCodeVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D3F343323EC6EFC009063F8 /* RRVerifyCodeVC.m */; };
		8D3F343723EC797F009063F8 /* RRRegisterResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D3F343623EC797F009063F8 /* RRRegisterResultVC.m */; };
		8D3F343A23ED0287009063F8 /* RRShadowButton.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D3F343923ED0287009063F8 /* RRShadowButton.m */; };
		8D3F343E23ED7493009063F8 /* RRAccountVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D3F343D23ED7493009063F8 /* RRAccountVC.m */; };
		8D3F344423ED9CCF009063F8 /* RRXNListBtn.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D3F344323ED9CCF009063F8 /* RRXNListBtn.m */; };
		8D3F344A23EDC350009063F8 /* RRSecurityCenterVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D3F344923EDC350009063F8 /* RRSecurityCenterVC.m */; };
		8D3F344D23EE8EA9009063F8 /* RRPwdManageVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D3F344C23EE8EA9009063F8 /* RRPwdManageVC.m */; };
		8D3F345023EE94D6009063F8 /* RREditLoginPwdVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D3F344F23EE94D6009063F8 /* RREditLoginPwdVC.m */; };
		8D3F345323EE94F1009063F8 /* RREditPayPwdVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D3F345223EE94F1009063F8 /* RREditPayPwdVC.m */; };
		8D3F345623EE9529009063F8 /* RRRetrievePayPwdVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D3F345523EE9529009063F8 /* RRRetrievePayPwdVC.m */; };
		8D3F345923EEBC70009063F8 /* RREditLoginPwdResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D3F345823EEBC70009063F8 /* RREditLoginPwdResultVC.m */; };
		8D3F345C23EEC703009063F8 /* RRMobileView.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D3F345A23EEC702009063F8 /* RRMobileView.m */; };
		8D3F345F23EECA51009063F8 /* RRAuthPayPwdVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D3F345E23EECA51009063F8 /* RRAuthPayPwdVC.m */; };
		8D3F346223EEE5A3009063F8 /* RREditPayPwdAgainVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D3F346123EEE5A3009063F8 /* RREditPayPwdAgainVC.m */; };
		8D3F346523EEE791009063F8 /* RREditPayPwdResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D3F346423EEE791009063F8 /* RREditPayPwdResultVC.m */; };
		8D56D5462484A91D0083608E /* RRHistoryDetailsModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D56D5452484A91D0083608E /* RRHistoryDetailsModel.m */; };
		8D5E74F32486303F00DC1C9E /* phoneCode_ru.plist in Resources */ = {isa = PBXBuildFile; fileRef = 8D5E74F22486303F00DC1C9E /* phoneCode_ru.plist */; };
		8D64EF522534043F00D2E08E /* RRTempDeviceLoginVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D64EF512534043F00D2E08E /* RRTempDeviceLoginVC.m */; };
		8D64EF572534307C00D2E08E /* RRAuthCodeUseListVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D64EF562534307C00D2E08E /* RRAuthCodeUseListVC.m */; };
		8D64EF5D253430CC00D2E08E /* RRAuthCodeUseListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D64EF5C253430CC00D2E08E /* RRAuthCodeUseListCell.m */; };
		8D64EF642534310300D2E08E /* RRAuthCodeUseListModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D64EF632534310300D2E08E /* RRAuthCodeUseListModel.m */; };
		8D64EF7E25358FA200D2E08E /* RRChangeMasterDeviceResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D64EF7D25358FA200D2E08E /* RRChangeMasterDeviceResultVC.m */; };
		8D6685DB241C8E8E00AEC5DC /* XNExceptionHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D6685DA241C8E8E00AEC5DC /* XNExceptionHandler.m */; };
		8D6685E3241CB97400AEC5DC /* RRRetrieveUsernameVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D6685DD241CB97400AEC5DC /* RRRetrieveUsernameVC.m */; };
		8D6685E4241CB97400AEC5DC /* RRRetrieveResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D6685DE241CB97400AEC5DC /* RRRetrieveResultVC.m */; };
		8D6685E5241CB97400AEC5DC /* RRRetrievePwdVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D6685E2241CB97400AEC5DC /* RRRetrievePwdVC.m */; };
		8D6685ED241CEF3400AEC5DC /* RRAboutVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D6685EC241CEF3400AEC5DC /* RRAboutVC.m */; };
		8D66E0FA258394BA004853DD /* loading.json in Resources */ = {isa = PBXBuildFile; fileRef = 8D66E0F9258394BA004853DD /* loading.json */; };
		8D6CA51124B0287B00BCF643 /* RRTopUpVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D6CA51024B0287B00BCF643 /* RRTopUpVC.m */; };
		8D6CA51424B047DF00BCF643 /* RRTopUpResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D6CA51324B047DF00BCF643 /* RRTopUpResultVC.m */; };
		8D73D82024F3E3FF0022ABBD /* UIView+CornerRadius.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D73D81F24F3E3FF0022ABBD /* UIView+CornerRadius.m */; };
		8D7C04022403698900FD57A3 /* RRFixedTransferVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D7C04012403698900FD57A3 /* RRFixedTransferVC.m */; };
		8D7C04062403AD4E00FD57A3 /* RRXNScanVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D7C04052403AD4E00FD57A3 /* RRXNScanVC.m */; };
		8D7C040B2403D47A00FD57A3 /* RRXNStyleDIY.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D7C04072403D47900FD57A3 /* RRXNStyleDIY.m */; };
		8D7D2FC4241D370700775E59 /* RRNavigationController.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D7D2FC3241D370700775E59 /* RRNavigationController.m */; };
		8D7D2FC7241D470400775E59 /* RRDatePicker.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D7D2FC6241D470400775E59 /* RRDatePicker.m */; };
		8D94697F2422849500F41711 /* RRPortraitSetVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D94697D2422849500F41711 /* RRPortraitSetVC.m */; };
		8D946988242284D600F41711 /* RRCheckoutView.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D946984242284D600F41711 /* RRCheckoutView.m */; };
		8D946989242284D600F41711 /* RRNativeCheckoutVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D946986242284D600F41711 /* RRNativeCheckoutVC.m */; };
		8D94698A242284D600F41711 /* RRNativeCheckoutResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D946987242284D600F41711 /* RRNativeCheckoutResultVC.m */; };
		8D996C4124109F480020E6EF /* UIButton+Category.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D996C4024109F480020E6EF /* UIButton+Category.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		8D9F058123F1AC97004D0817 /* RRXNLoginInputView.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D9F058023F1AC97004D0817 /* RRXNLoginInputView.m */; };
		8DA0BE7924B7178F00EF4D4D /* RRVersionUpdateView.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DA0BE7824B7178F00EF4D4D /* RRVersionUpdateView.m */; };
		8DA3AAC92578D30100B7354C /* RRHomeFunCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DA3AAC82578D30100B7354C /* RRHomeFunCell.m */; };
		8DA88D2023FBB9710057A82F /* RRHistoryListVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DA88D1F23FBB9710057A82F /* RRHistoryListVC.m */; };
		8DA88D2423FBE78A0057A82F /* RRHistoryListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DA88D2323FBE78A0057A82F /* RRHistoryListCell.m */; };
		8DA88D2723FC028E0057A82F /* RRHistoryListDetailsVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DA88D2623FC028E0057A82F /* RRHistoryListDetailsVC.m */; };
		8DA88D2A23FC15610057A82F /* OrderedDictionary.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DA88D2923FC15610057A82F /* OrderedDictionary.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		8DB4A4D623F6EED90095A49F /* RRTransferResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DB4A4D523F6EED90095A49F /* RRTransferResultVC.m */; };
		8DB88950250B2DE5006A5AC3 /* RRInviteFriendsWebVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DB8894F250B2DE5006A5AC3 /* RRInviteFriendsWebVC.m */; };
		8DB88955250C78FF006A5AC3 /* RRDeviceManageVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DB88954250C78FF006A5AC3 /* RRDeviceManageVC.m */; };
		8DB88958250CA923006A5AC3 /* RRFirstSetLoginPwdResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DB88957250CA923006A5AC3 /* RRFirstSetLoginPwdResultVC.m */; };
		8DB8895B250CDB52006A5AC3 /* RRAuthCodeDisplayVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DB8895A250CDB52006A5AC3 /* RRAuthCodeDisplayVC.m */; };
		8DBB5B8223FB89ED00CFEF1A /* RRHUDView.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DBB5B8123FB89ED00CFEF1A /* RRHUDView.m */; };
		8DBB5BFD23FB8CE000CFEF1A /* mobileImg.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 8DBB5BFC23FB8CE000CFEF1A /* mobileImg.bundle */; };
		8DC42C2124358506003FF9BF /* phoneCode_en.plist in Resources */ = {isa = PBXBuildFile; fileRef = 8DC42C2024358506003FF9BF /* phoneCode_en.plist */; };
		8DC42C23243596DC003FF9BF /* phoneCode_zh.plist in Resources */ = {isa = PBXBuildFile; fileRef = 8DC42C22243596DC003FF9BF /* phoneCode_zh.plist */; };
		8DC829A824791A60007EBA45 /* RRPaymentMethodModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DC829A724791A60007EBA45 /* RRPaymentMethodModel.m */; };
		8DCCDCAA2546B36E008E1793 /* RRAVPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DCCDCA82546B36E008E1793 /* RRAVPlayer.m */; };
		8DCCDCB02546B869008E1793 /* RRBroadcastVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DCCDCAF2546B869008E1793 /* RRBroadcastVC.m */; };
		8DCFC7E025594218000EF0AB /* NotificationService.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DCFC7DF25594218000EF0AB /* NotificationService.m */; };
		8DCFC7E425594218000EF0AB /* pushServiceExtension.appex in Embed App Extensions */ = {isa = PBXBuildFile; fileRef = 8DCFC7DC25594218000EF0AB /* pushServiceExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		8DCFC7F925595268000EF0AB /* 123.m4a in Resources */ = {isa = PBXBuildFile; fileRef = 8DCFC7F825595268000EF0AB /* 123.m4a */; };
		8DCFC7FA25595268000EF0AB /* 123.m4a in Resources */ = {isa = PBXBuildFile; fileRef = 8DCFC7F825595268000EF0AB /* 123.m4a */; };
		8DCFC7FF255953ED000EF0AB /* 456.m4a in Resources */ = {isa = PBXBuildFile; fileRef = 8DCFC7FE255953ED000EF0AB /* 456.m4a */; };
		8DCFC800255953ED000EF0AB /* 456.m4a in Resources */ = {isa = PBXBuildFile; fileRef = 8DCFC7FE255953ED000EF0AB /* 456.m4a */; };
		8DD4F31A2412AF6200FB55EF /* RRBindBankCardResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DD4F3192412AF6200FB55EF /* RRBindBankCardResultVC.m */; };
		8DD4F31E241377A600FB55EF /* RRWithdrawalVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DD4F31D241377A600FB55EF /* RRWithdrawalVC.m */; };
		8DD4F3212413A55E00FB55EF /* RRWithdrawalResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DD4F3202413A55E00FB55EF /* RRWithdrawalResultVC.m */; };
		8DD4F3252413C39D00FB55EF /* RRWithdrawalStatusView.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DD4F3242413C39D00FB55EF /* RRWithdrawalStatusView.m */; };
		8DD4F3292413F57B00FB55EF /* RRPersonalInfoVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DD4F3282413F57B00FB55EF /* RRPersonalInfoVC.m */; };
		8DD4F32D241411B500FB55EF /* RRLoginLockVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DD4F32C241411B500FB55EF /* RRLoginLockVC.m */; };
		8DD4F3302414148800FB55EF /* RRModifyNicknameVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DD4F32F2414148800FB55EF /* RRModifyNicknameVC.m */; };
		8DD4F333241414A900FB55EF /* RRModifyEmailVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DD4F332241414A900FB55EF /* RRModifyEmailVC.m */; };
		8DD4F33724141D0100FB55EF /* RRModifyMobileVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DD4F33624141D0100FB55EF /* RRModifyMobileVC.m */; };
		8DD4F33B2415608500FB55EF /* RRLoginUnlockVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DD4F33A2415608500FB55EF /* RRLoginUnlockVC.m */; };
		8DD4F33E2415672C00FB55EF /* RRLoginPwdUnlockVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DD4F33D2415672C00FB55EF /* RRLoginPwdUnlockVC.m */; };
		8DD4F3452416572700FB55EF /* RRContactTransferRecordVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DD4F3442416572700FB55EF /* RRContactTransferRecordVC.m */; };
		8DD4F3482416788D00FB55EF /* RRModifyMobileResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DD4F3472416788D00FB55EF /* RRModifyMobileResultVC.m */; };
		8DE1DF3823F79148002D04CA /* UITextField+Category.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DE1DF3723F79148002D04CA /* UITextField+Category.m */; };
		8DE5C5952536A432000712E0 /* RRPromotionImgModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DE5C5942536A432000712E0 /* RRPromotionImgModel.m */; };
		8DE5C5A02536D573000712E0 /* RRFloatPictureView.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DE5C59F2536D573000712E0 /* RRFloatPictureView.m */; };
		8DE674A22468FEAF00E52F70 /* RRContact.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DE674A12468FEAF00E52F70 /* RRContact.m */; };
		8DE674A5246A8CDB00E52F70 /* RRHistoryModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DE674A4246A8CDB00E52F70 /* RRHistoryModel.m */; };
		8DE674A8246AA14400E52F70 /* RRLssuedModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DE674A7246AA14400E52F70 /* RRLssuedModel.m */; };
		8DE674AB246AA18900E52F70 /* RRReceiveModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DE674AA246AA18900E52F70 /* RRReceiveModel.m */; };
		8DE674B1246AA38000E52F70 /* RRCardModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DE674B0246AA38000E52F70 /* RRCardModel.m */; };
		8DE674B5246B9C3400E52F70 /* RRBillPersonAmtModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DE674B4246B9C3400E52F70 /* RRBillPersonAmtModel.m */; };
		8DEE998823F546B900C47CB3 /* RRReceiveQrCodeVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DEE998723F546B900C47CB3 /* RRReceiveQrCodeVC.m */; };
		8DEE998B23F5642800C47CB3 /* RRReceiveQrCodeSetPriceVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DEE998A23F5642800C47CB3 /* RRReceiveQrCodeSetPriceVC.m */; };
		8DEE998F23F56E2800C47CB3 /* RRTransferListVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DEE998E23F56E2800C47CB3 /* RRTransferListVC.m */; };
		8DEE999223F58E6300C47CB3 /* RRTransferAccountVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DEE999123F58E6300C47CB3 /* RRTransferAccountVC.m */; };
		8DEE999523F5925700C47CB3 /* RRTransferVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DEE999423F5925700C47CB3 /* RRTransferVC.m */; };
		8DEE999923F5A4B300C47CB3 /* RRContactListVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DEE999823F5A4B300C47CB3 /* RRContactListVC.m */; };
		8DEE999C23F5AA9900C47CB3 /* RRContactAddVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DEE999B23F5AA9900C47CB3 /* RRContactAddVC.m */; };
		8DEE999F23F5ABFE00C47CB3 /* RRContactInfVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DEE999E23F5ABFE00C47CB3 /* RRContactInfVC.m */; };
		8DF96231249883600005DB6E /* RRContactUsVC.m in Sources */ = {isa = PBXBuildFile; fileRef = 8DF96230249883600005DB6E /* RRContactUsVC.m */; };
		B4016DAE2B4CFAB90072D554 /* RRConfirmPaymentVC.m in Sources */ = {isa = PBXBuildFile; fileRef = B4016DAD2B4CFAB90072D554 /* RRConfirmPaymentVC.m */; };
		B4079FA62AE229970076460D /* CouponSDK.m in Sources */ = {isa = PBXBuildFile; fileRef = B4079FA52AE229970076460D /* CouponSDK.m */; };
		B40FEDB2272BE2ED00640151 /* RRBlurView.m in Sources */ = {isa = PBXBuildFile; fileRef = B40FEDB1272BE2ED00640151 /* RRBlurView.m */; };
		B40FEDB5272C020A00640151 /* RRWarningView.m in Sources */ = {isa = PBXBuildFile; fileRef = B40FEDB4272C020A00640151 /* RRWarningView.m */; };
		B421BE7A2B3588DF00488ACF /* RRAirtimeBundleResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = B421BE792B3588DF00488ACF /* RRAirtimeBundleResultVC.m */; };
		B44A90152B441C7200E8B1A3 /* RRMoreFunctionVC.m in Sources */ = {isa = PBXBuildFile; fileRef = B44A90142B441C7200E8B1A3 /* RRMoreFunctionVC.m */; };
		B44A90182B441D5700E8B1A3 /* RRMoreFuctionCell.m in Sources */ = {isa = PBXBuildFile; fileRef = B44A90172B441D5700E8B1A3 /* RRMoreFuctionCell.m */; };
		B44C86FE2A09E3BE00FA25DB /* RRJWMerchantDetailsModel.m in Sources */ = {isa = PBXBuildFile; fileRef = B44C86FD2A09E3BE00FA25DB /* RRJWMerchantDetailsModel.m */; };
		B453B0C22B3292B7008D787C /* RRMobileTopView.m in Sources */ = {isa = PBXBuildFile; fileRef = B453B0C12B3292B7008D787C /* RRMobileTopView.m */; };
		B453B0C52B3292D1008D787C /* RRBundleTopView.m in Sources */ = {isa = PBXBuildFile; fileRef = B453B0C42B3292D1008D787C /* RRBundleTopView.m */; };
		B46813442AB94C8F002C3438 /* RRLoginLockView.m in Sources */ = {isa = PBXBuildFile; fileRef = B46813432AB94C8F002C3438 /* RRLoginLockView.m */; };
		B46813472AB98963002C3438 /* RRLoginPwdVerifyVC.m in Sources */ = {isa = PBXBuildFile; fileRef = B46813462AB98962002C3438 /* RRLoginPwdVerifyVC.m */; };
		B46813592AB99A22002C3438 /* AliPayItem.m in Sources */ = {isa = PBXBuildFile; fileRef = B468134F2AB99A22002C3438 /* AliPayItem.m */; };
		B468135E2AB99A34002C3438 /* RRGestureViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B468135D2AB99A34002C3438 /* RRGestureViewController.m */; };
		B46813612AB9BF48002C3438 /* RRGestureLockView.m in Sources */ = {isa = PBXBuildFile; fileRef = B46813602AB9BF48002C3438 /* RRGestureLockView.m */; };
		B46C2C922A088C9B0075C196 /* UIView+JWExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = B46C2C7A2A088C9B0075C196 /* UIView+JWExtension.m */; };
		B46C2C932A088C9B0075C196 /* RRJWMerchantInfomationVC.m in Sources */ = {isa = PBXBuildFile; fileRef = B46C2C7C2A088C9B0075C196 /* RRJWMerchantInfomationVC.m */; };
		B46C2C942A088C9B0075C196 /* RRJWMerchantListVC.m in Sources */ = {isa = PBXBuildFile; fileRef = B46C2C7D2A088C9B0075C196 /* RRJWMerchantListVC.m */; };
		B46C2C952A088C9B0075C196 /* RRJWMerchantModel.m in Sources */ = {isa = PBXBuildFile; fileRef = B46C2C812A088C9B0075C196 /* RRJWMerchantModel.m */; };
		B46C2C962A088C9B0075C196 /* RRJWMerchantInfomationAddressCell.m in Sources */ = {isa = PBXBuildFile; fileRef = B46C2C852A088C9B0075C196 /* RRJWMerchantInfomationAddressCell.m */; };
		B46C2C972A088C9B0075C196 /* RRJWMerchantInfomationHeaderView.m in Sources */ = {isa = PBXBuildFile; fileRef = B46C2C862A088C9B0075C196 /* RRJWMerchantInfomationHeaderView.m */; };
		B46C2C982A088C9B0075C196 /* RRJWMerchantListFiltrateView.m in Sources */ = {isa = PBXBuildFile; fileRef = B46C2C872A088C9B0075C196 /* RRJWMerchantListFiltrateView.m */; };
		B46C2C992A088C9B0075C196 /* RRJWBaseView.m in Sources */ = {isa = PBXBuildFile; fileRef = B46C2C882A088C9B0075C196 /* RRJWBaseView.m */; };
		B46C2C9A2A088C9B0075C196 /* RRJWMerchantInfomationCell.m in Sources */ = {isa = PBXBuildFile; fileRef = B46C2C892A088C9B0075C196 /* RRJWMerchantInfomationCell.m */; };
		B46C2C9B2A088C9B0075C196 /* RRJWMerchantListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = B46C2C8A2A088C9B0075C196 /* RRJWMerchantListCell.m */; };
		B46C2C9C2A088C9C0075C196 /* RRJWShopInfoView.m in Sources */ = {isa = PBXBuildFile; fileRef = B46C2C8C2A088C9B0075C196 /* RRJWShopInfoView.m */; };
		B470C7942B24957C000C47F1 /* RRBillManagerVC.m in Sources */ = {isa = PBXBuildFile; fileRef = B470C7932B24957C000C47F1 /* RRBillManagerVC.m */; };
		B470C7982B2496E2000C47F1 /* RRBIillTypeCell.m in Sources */ = {isa = PBXBuildFile; fileRef = B470C7972B2496E2000C47F1 /* RRBIillTypeCell.m */; };
		B470C79B2B249A29000C47F1 /* RRBillInfoInputVC.m in Sources */ = {isa = PBXBuildFile; fileRef = B470C79A2B249A29000C47F1 /* RRBillInfoInputVC.m */; };
		B470C79E2B24A05B000C47F1 /* RRBillAmtInputVC.m in Sources */ = {isa = PBXBuildFile; fileRef = B470C79D2B24A05B000C47F1 /* RRBillAmtInputVC.m */; };
		B470C7A12B24AC80000C47F1 /* RRBillPaymentResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = B470C7A02B24AC80000C47F1 /* RRBillPaymentResultVC.m */; };
		B470C7A42B24ADD1000C47F1 /* RRBIllPaymentRecordsVC.m in Sources */ = {isa = PBXBuildFile; fileRef = B470C7A32B24ADD1000C47F1 /* RRBIllPaymentRecordsVC.m */; };
		B470C7A72B24ADFB000C47F1 /* RRBillPaymentRecordCell.m in Sources */ = {isa = PBXBuildFile; fileRef = B470C7A62B24ADFB000C47F1 /* RRBillPaymentRecordCell.m */; };
		B49C987E273382F8007F2C9E /* md5.json in Resources */ = {isa = PBXBuildFile; fileRef = B49C987D273382F8007F2C9E /* md5.json */; };
		B4A9AF2E2AA8500D000CF6A1 /* RRCurrencyAccountView.m in Sources */ = {isa = PBXBuildFile; fileRef = B4A9AF2D2AA8500D000CF6A1 /* RRCurrencyAccountView.m */; };
		B4BBD29E2B5E3C0C00EAAD97 /* RRXNInputField.m in Sources */ = {isa = PBXBuildFile; fileRef = B4BBD29C2B5E3C0C00EAAD97 /* RRXNInputField.m */; };
		B4C1604E29A704DF002D5740 /* RRBalanceRecordModel.m in Sources */ = {isa = PBXBuildFile; fileRef = B4C1604D29A704DF002D5740 /* RRBalanceRecordModel.m */; };
		B4C1605429A70BBB002D5740 /* RRAccountRecordDetailsVC.m in Sources */ = {isa = PBXBuildFile; fileRef = B4C1605329A70BBB002D5740 /* RRAccountRecordDetailsVC.m */; };
		B4C3ED822B10966000FC3D53 /* RRWithdrawalAccountPickView.m in Sources */ = {isa = PBXBuildFile; fileRef = B4C3ED812B10966000FC3D53 /* RRWithdrawalAccountPickView.m */; };
		B4D17A1A2B2E91700090D568 /* RRAirTimeBundleTopVC.m in Sources */ = {isa = PBXBuildFile; fileRef = B4D17A192B2E91700090D568 /* RRAirTimeBundleTopVC.m */; };
		B4D17A1D2B2EF71C0090D568 /* RRBillerInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = B4D17A1C2B2EF71C0090D568 /* RRBillerInfoModel.m */; };
		B4DB370A29A5F2B3009B7529 /* RRAccountHistoryVC.m in Sources */ = {isa = PBXBuildFile; fileRef = B4DB370929A5F2B3009B7529 /* RRAccountHistoryVC.m */; };
		B4DB370D29A5F5B2009B7529 /* RRAccountHistoryListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = B4DB370C29A5F5B2009B7529 /* RRAccountHistoryListCell.m */; };
		B4DB371029A5FD25009B7529 /* RRBalanceView.m in Sources */ = {isa = PBXBuildFile; fileRef = B4DB370F29A5FD25009B7529 /* RRBalanceView.m */; };
		B4DC2B3729BF220C00FF8162 /* RRCommonWebVC.m in Sources */ = {isa = PBXBuildFile; fileRef = B4DC2B3629BF220C00FF8162 /* RRCommonWebVC.m */; };
		B4DC2B3A29BF221700FF8162 /* RRTermProtocolVC.m in Sources */ = {isa = PBXBuildFile; fileRef = B4DC2B3929BF221700FF8162 /* RRTermProtocolVC.m */; };
		B4DFE1352AA99A98007BA3AC /* HMSegmentedControl.m in Sources */ = {isa = PBXBuildFile; fileRef = B4DFE1342AA99A98007BA3AC /* HMSegmentedControl.m */; };
		B4DFE1382AA9A101007BA3AC /* RRActiveAccountVC.m in Sources */ = {isa = PBXBuildFile; fileRef = B4DFE1372AA9A101007BA3AC /* RRActiveAccountVC.m */; };
		B4E018352AEA3C5500531BD0 /* RRSelectLanguageVC.m in Sources */ = {isa = PBXBuildFile; fileRef = B4E018342AEA3C5500531BD0 /* RRSelectLanguageVC.m */; };
		B4E018392AEA4ADE00531BD0 /* RRRegisterProcessView.m in Sources */ = {isa = PBXBuildFile; fileRef = B4E018382AEA4ADE00531BD0 /* RRRegisterProcessView.m */; };
		B4E018432AEE0DC500531BD0 /* CXDatePickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = B4E0183B2AEE0DC500531BD0 /* CXDatePickerView.m */; };
		B4E018442AEE0DC500531BD0 /* NSDate+CXCategory.m in Sources */ = {isa = PBXBuildFile; fileRef = B4E018412AEE0DC500531BD0 /* NSDate+CXCategory.m */; };
		B4E018452AEE0DC500531BD0 /* CXDatePickerViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = B4E018422AEE0DC500531BD0 /* CXDatePickerViewManager.m */; };
		B4E02F7A2B392564000D9856 /* RRAirtimeBundleModel.m in Sources */ = {isa = PBXBuildFile; fileRef = B4E02F792B392564000D9856 /* RRAirtimeBundleModel.m */; };
		B4ED8CC52B26DC7A00D4A3F7 /* RRUserQRCodeVC.m in Sources */ = {isa = PBXBuildFile; fileRef = B4ED8CC42B26DC7A00D4A3F7 /* RRUserQRCodeVC.m */; };
		B4ED8CCA2B26ED0400D4A3F7 /* RRSaveQrCodeView.m in Sources */ = {isa = PBXBuildFile; fileRef = B4ED8CC92B26ED0400D4A3F7 /* RRSaveQrCodeView.m */; };
		B4F12ABD2B34138900896314 /* RRAmtItemView.m in Sources */ = {isa = PBXBuildFile; fileRef = B4F12ABC2B34138900896314 /* RRAmtItemView.m */; };
		B4F12AC02B341DF600896314 /* RRBundleItemView.m in Sources */ = {isa = PBXBuildFile; fileRef = B4F12ABF2B341DF600896314 /* RRBundleItemView.m */; };
		B4F12AC32B3422BE00896314 /* RRBundlePlanVC.m in Sources */ = {isa = PBXBuildFile; fileRef = B4F12AC22B3422BE00896314 /* RRBundlePlanVC.m */; };
		B4F12AC62B34230900896314 /* RRBundleListView.m in Sources */ = {isa = PBXBuildFile; fileRef = B4F12AC52B34230900896314 /* RRBundleListView.m */; };
		B4F12AC92B34262900896314 /* RRBundleItemCell.m in Sources */ = {isa = PBXBuildFile; fileRef = B4F12AC82B34262900896314 /* RRBundleItemCell.m */; };
		B4F12ACC2B34350300896314 /* RRBundleAppCell.m in Sources */ = {isa = PBXBuildFile; fileRef = B4F12ACB2B34350300896314 /* RRBundleAppCell.m */; };
		B4F534002A08D55C002E1985 /* RRJWMerchantCategoryModel.m in Sources */ = {isa = PBXBuildFile; fileRef = B4F533FF2A08D55C002E1985 /* RRJWMerchantCategoryModel.m */; };
		B4F534032A08EBC0002E1985 /* RRJWMerchantInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = B4F534022A08EBC0002E1985 /* RRJWMerchantInfoModel.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		8D0228A523E7D394008EB3F4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8D02288423E7D392008EB3F4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8D02288B23E7D392008EB3F4;
			remoteInfo = xWalletPro_iOS;
		};
		8D0228B023E7D394008EB3F4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8D02288423E7D392008EB3F4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8D02288B23E7D392008EB3F4;
			remoteInfo = xWalletPro_iOS;
		};
		8DCFC7E225594218000EF0AB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8D02288423E7D392008EB3F4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8DCFC7DB25594218000EF0AB;
			remoteInfo = pushServiceExtension;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		8DCCDCC52546DC16008E1793 /* Embed App Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				8DCFC7E425594218000EF0AB /* pushServiceExtension.appex in Embed App Extensions */,
			);
			name = "Embed App Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		57466F3357655E842D2ABDF8 /* Pods-xWalletPro_iOS.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-xWalletPro_iOS.release.xcconfig"; path = "Target Support Files/Pods-xWalletPro_iOS/Pods-xWalletPro_iOS.release.xcconfig"; sourceTree = "<group>"; };
		690A07942CDFF3B6005C1EE4 /* CustomMJRefreshNormalHeader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CustomMJRefreshNormalHeader.h; sourceTree = "<group>"; };
		690A07952CDFF3B6005C1EE4 /* CustomMJRefreshNormalHeader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CustomMJRefreshNormalHeader.m; sourceTree = "<group>"; };
		690A07982CE00AA8005C1EE4 /* YJCouponCenterSDK.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = YJCouponCenterSDK.framework; sourceTree = "<group>"; };
		690A079D2CE36DF9005C1EE4 /* resource.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = resource.bundle; sourceTree = "<group>"; };
		690A079F2CE4B624005C1EE4 /* RRCashOutVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRCashOutVC.h; sourceTree = "<group>"; };
		690A07A02CE4B624005C1EE4 /* RRCashOutVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRCashOutVC.m; sourceTree = "<group>"; };
		690A07A22CE7414A005C1EE4 /* RRRegisterMobileVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRRegisterMobileVC.h; sourceTree = "<group>"; };
		690A07A32CE7414A005C1EE4 /* RRRegisterMobileVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRRegisterMobileVC.m; sourceTree = "<group>"; };
		690A07A52CEB3A10005C1EE4 /* RCashOutResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RCashOutResultVC.h; sourceTree = "<group>"; };
		690A07A62CEB3A10005C1EE4 /* RCashOutResultVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RCashOutResultVC.m; sourceTree = "<group>"; };
		6928D2932C25586D001D0802 /* UIDevice+VGAddition.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIDevice+VGAddition.h"; sourceTree = "<group>"; };
		6928D2942C25586D001D0802 /* UIDevice+VGAddition.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIDevice+VGAddition.m"; sourceTree = "<group>"; };
		69366F252B70D43C0065BC73 /* RRCheckJuniorAccountVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRCheckJuniorAccountVC.h; sourceTree = "<group>"; };
		69366F262B70D43C0065BC73 /* RRCheckJuniorAccountVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRCheckJuniorAccountVC.m; sourceTree = "<group>"; };
		69366F282B70E3C20065BC73 /* RRCheckJuniorAccountCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRCheckJuniorAccountCell.h; sourceTree = "<group>"; };
		69366F292B70E3C20065BC73 /* RRCheckJuniorAccountCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRCheckJuniorAccountCell.m; sourceTree = "<group>"; };
		6936784B2C0482E900817B5A /* RRMerchantCodeView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRMerchantCodeView.h; sourceTree = "<group>"; };
		6936784C2C0482E900817B5A /* RRMerchantCodeView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRMerchantCodeView.m; sourceTree = "<group>"; };
		694BB1FD2CD50D84008989BB /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		695A24762C05684B00109FF5 /* RRCommonInputView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRCommonInputView.h; sourceTree = "<group>"; };
		695A24772C05684B00109FF5 /* RRCommonInputView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRCommonInputView.m; sourceTree = "<group>"; };
		695A247C2C056DDD00109FF5 /* RRBillerCodeInputVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBillerCodeInputVC.h; sourceTree = "<group>"; };
		695A247D2C056DDD00109FF5 /* RRBillerCodeInputVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBillerCodeInputVC.m; sourceTree = "<group>"; };
		695A247F2C0575B000109FF5 /* RRCommonAmountView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRCommonAmountView.h; sourceTree = "<group>"; };
		695A24802C0575B000109FF5 /* RRCommonAmountView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRCommonAmountView.m; sourceTree = "<group>"; };
		695A24822C058CA000109FF5 /* RRCommonSelectView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRCommonSelectView.h; sourceTree = "<group>"; };
		695A24832C058CA000109FF5 /* RRCommonSelectView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRCommonSelectView.m; sourceTree = "<group>"; };
		695AED452C22B7B2005A4D31 /* RRResetPINVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRResetPINVC.h; sourceTree = "<group>"; };
		695AED462C22B7B2005A4D31 /* RRResetPINVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRResetPINVC.m; sourceTree = "<group>"; };
		697047BD2B81EC8F009543EC /* RRCheckJuniorAccountDetailsVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRCheckJuniorAccountDetailsVC.h; sourceTree = "<group>"; };
		697047BE2B81EC8F009543EC /* RRCheckJuniorAccountDetailsVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRCheckJuniorAccountDetailsVC.m; sourceTree = "<group>"; };
		697047C42B82430C009543EC /* RRPersonalInfoAvactorCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRPersonalInfoAvactorCell.h; sourceTree = "<group>"; };
		697047C52B82430C009543EC /* RRPersonalInfoAvactorCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRPersonalInfoAvactorCell.m; sourceTree = "<group>"; };
		697047C72B82E73B009543EC /* RRJuniorBalanceView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRJuniorBalanceView.h; sourceTree = "<group>"; };
		697047C82B82E73B009543EC /* RRJuniorBalanceView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRJuniorBalanceView.m; sourceTree = "<group>"; };
		6977AC6B2B39135600879110 /* PaymentOrderVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PaymentOrderVC.m; sourceTree = "<group>"; };
		6977AC6C2B39135600879110 /* PaymentOrderVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PaymentOrderVC.h; sourceTree = "<group>"; };
		6977AC6F2B392F1C00879110 /* RRAutomaticDebitVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRAutomaticDebitVC.h; sourceTree = "<group>"; };
		6977AC702B392F1C00879110 /* RRAutomaticDebitVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRAutomaticDebitVC.m; sourceTree = "<group>"; };
		6977AC722B39631100879110 /* RRAutomaticDebitCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRAutomaticDebitCell.h; sourceTree = "<group>"; };
		6977AC732B39631100879110 /* RRAutomaticDebitCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRAutomaticDebitCell.m; sourceTree = "<group>"; };
		698CCF802B67AEA300FFD9CA /* RRJuniorAccountUpgradeVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRJuniorAccountUpgradeVC.h; sourceTree = "<group>"; };
		698CCF812B67AEA300FFD9CA /* RRJuniorAccountUpgradeVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRJuniorAccountUpgradeVC.m; sourceTree = "<group>"; };
		698CCF832B67E88C00FFD9CA /* RRJuniorUpgradeFResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRJuniorUpgradeFResultVC.h; sourceTree = "<group>"; };
		698CCF842B67E88C00FFD9CA /* RRJuniorUpgradeFResultVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRJuniorUpgradeFResultVC.m; sourceTree = "<group>"; };
		698DCBDB2B679520005D0205 /* RRJuniorAccountDetailVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRJuniorAccountDetailVC.h; sourceTree = "<group>"; };
		698DCBDC2B679520005D0205 /* RRJuniorAccountDetailVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRJuniorAccountDetailVC.m; sourceTree = "<group>"; };
		698DCBDE2B679E50005D0205 /* RRJuniorAccountChangeVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRJuniorAccountChangeVC.h; sourceTree = "<group>"; };
		698DCBDF2B679E50005D0205 /* RRJuniorAccountChangeVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRJuniorAccountChangeVC.m; sourceTree = "<group>"; };
		6990B0802B2FDBDF007D917B /* RRPaymentSettingsVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRPaymentSettingsVC.h; sourceTree = "<group>"; };
		6990B0812B2FDBDF007D917B /* RRPaymentSettingsVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRPaymentSettingsVC.m; sourceTree = "<group>"; };
		6990B0832B2FEB4B007D917B /* RRNoPINPaymentVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRNoPINPaymentVC.h; sourceTree = "<group>"; };
		6990B0842B2FEB4B007D917B /* RRNoPINPaymentVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRNoPINPaymentVC.m; sourceTree = "<group>"; };
		6990B0862B302DE6007D917B /* RRNoPINPaymentMangeVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRNoPINPaymentMangeVC.h; sourceTree = "<group>"; };
		6990B0872B302DE6007D917B /* RRNoPINPaymentMangeVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRNoPINPaymentMangeVC.m; sourceTree = "<group>"; };
		6997F4852BA91F70006E4E03 /* RRZipitVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRZipitVC.h; sourceTree = "<group>"; };
		6997F4862BA91F70006E4E03 /* RRZipitVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRZipitVC.m; sourceTree = "<group>"; };
		6997F4882BA9CE0D006E4E03 /* RRPaymentAccountSequenceVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRPaymentAccountSequenceVC.h; sourceTree = "<group>"; };
		6997F4892BA9CE0D006E4E03 /* RRPaymentAccountSequenceVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRPaymentAccountSequenceVC.m; sourceTree = "<group>"; };
		6997F48B2BA9D677006E4E03 /* RRAccountSequenceCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRAccountSequenceCell.h; sourceTree = "<group>"; };
		6997F48C2BA9D677006E4E03 /* RRAccountSequenceCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRAccountSequenceCell.m; sourceTree = "<group>"; };
		69CDA09A2C1831EC0050440E /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		69D75DBD2B31A10F00C76AE6 /* RRNoPINPaymentResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRNoPINPaymentResultVC.h; sourceTree = "<group>"; };
		69D75DBE2B31A10F00C76AE6 /* RRNoPINPaymentResultVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRNoPINPaymentResultVC.m; sourceTree = "<group>"; };
		69DDF3392B63A4C2006DAE64 /* RRJuniorAccountVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRJuniorAccountVC.h; sourceTree = "<group>"; };
		69DDF33A2B63A4C2006DAE64 /* RRJuniorAccountVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRJuniorAccountVC.m; sourceTree = "<group>"; };
		69DDF33E2B63A5D8006DAE64 /* RRJuniorAccountCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRJuniorAccountCell.h; sourceTree = "<group>"; };
		69DDF33F2B63A5D8006DAE64 /* RRJuniorAccountCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRJuniorAccountCell.m; sourceTree = "<group>"; };
		69E2C77B2B396F2000BE6EB2 /* RRAutomaticDebitDetailVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRAutomaticDebitDetailVC.h; sourceTree = "<group>"; };
		69E2C77C2B396F2000BE6EB2 /* RRAutomaticDebitDetailVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRAutomaticDebitDetailVC.m; sourceTree = "<group>"; };
		69E2C77E2B39753300BE6EB2 /* BuyAirtimeBundleVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BuyAirtimeBundleVC.h; sourceTree = "<group>"; };
		69E2C77F2B39753300BE6EB2 /* BuyAirtimeBundleVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BuyAirtimeBundleVC.m; sourceTree = "<group>"; };
		69E2C7812B39826400BE6EB2 /* BuyAirtimeBundleResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BuyAirtimeBundleResultVC.h; sourceTree = "<group>"; };
		69E2C7822B39826400BE6EB2 /* BuyAirtimeBundleResultVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BuyAirtimeBundleResultVC.m; sourceTree = "<group>"; };
		69EF34242B3132DA00ADE484 /* RRPINValidateView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRPINValidateView.h; sourceTree = "<group>"; };
		69EF34252B3132DA00ADE484 /* RRPINValidateView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRPINValidateView.m; sourceTree = "<group>"; };
		69F1388E2BDAB4D500E056F5 /* CustomAlertAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CustomAlertAction.h; sourceTree = "<group>"; };
		69F1388F2BDAB4D500E056F5 /* CustomAlertAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CustomAlertAction.m; sourceTree = "<group>"; };
		69F7DB3E2B14323200D908D8 /* RRStatementEnquiryVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRStatementEnquiryVC.h; sourceTree = "<group>"; };
		69F7DB3F2B14323200D908D8 /* RRStatementEnquiryVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRStatementEnquiryVC.m; sourceTree = "<group>"; };
		69F7DB412B143C8200D908D8 /* RRStatementEnquiryCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRStatementEnquiryCell.h; sourceTree = "<group>"; };
		69F7DB422B143C8200D908D8 /* RRStatementEnquiryCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRStatementEnquiryCell.m; sourceTree = "<group>"; };
		69F7F7292B70869B003BB98E /* RRSecretWordsVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRSecretWordsVC.h; sourceTree = "<group>"; };
		69F7F72A2B70869B003BB98E /* RRSecretWordsVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRSecretWordsVC.m; sourceTree = "<group>"; };
		69FA3E1E2C18442F005C9650 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		69FF451A2CDB7FF400CF6194 /* sn */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = sn; path = sn.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		72D3AD5752D61CC3355A87F8 /* Pods-xWalletPro_iOS.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-xWalletPro_iOS.debug.xcconfig"; path = "Target Support Files/Pods-xWalletPro_iOS/Pods-xWalletPro_iOS.debug.xcconfig"; sourceTree = "<group>"; };
		8D02288C23E7D392008EB3F4 /* xWalletPro_iOS.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = xWalletPro_iOS.app; sourceTree = BUILT_PRODUCTS_DIR; };
		8D02288F23E7D392008EB3F4 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		8D02289023E7D392008EB3F4 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		8D02289223E7D392008EB3F4 /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		8D02289323E7D392008EB3F4 /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		8D02289623E7D392008EB3F4 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		8D02289823E7D394008EB3F4 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		8D02289D23E7D394008EB3F4 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		8D02289E23E7D394008EB3F4 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		8D0228A423E7D394008EB3F4 /* xWalletPro_iOSTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = xWalletPro_iOSTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		8D0228A823E7D394008EB3F4 /* xWalletPro_iOSTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = xWalletPro_iOSTests.m; sourceTree = "<group>"; };
		8D0228AA23E7D394008EB3F4 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		8D0228AF23E7D394008EB3F4 /* xWalletPro_iOSUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = xWalletPro_iOSUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		8D0228B323E7D394008EB3F4 /* xWalletPro_iOSUITests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = xWalletPro_iOSUITests.m; sourceTree = "<group>"; };
		8D0228B523E7D394008EB3F4 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		8D0228C823E7DDDD008EB3F4 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		8D0228CE23E7E040008EB3F4 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		8D0228D723E9178D008EB3F4 /* RRHttpRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRHttpRequest.m; sourceTree = "<group>"; };
		8D0228D823E9178D008EB3F4 /* RRHttpRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRHttpRequest.h; sourceTree = "<group>"; };
		8D0228D923E9178D008EB3F4 /* RRRequestCode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRRequestCode.h; sourceTree = "<group>"; };
		8D0228DB23E918B5008EB3F4 /* SecurityUtility.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SecurityUtility.h; sourceTree = "<group>"; };
		8D0228DC23E918B5008EB3F4 /* SecurityUtility.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SecurityUtility.m; sourceTree = "<group>"; };
		8D0228DF23E91901008EB3F4 /* RRTools.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRTools.m; sourceTree = "<group>"; };
		8D0228E023E91901008EB3F4 /* RRTools.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRTools.h; sourceTree = "<group>"; };
		8D0228E223E91AD5008EB3F4 /* RRPublicHeader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRPublicHeader.h; sourceTree = "<group>"; };
		8D0228E323E91AF1008EB3F4 /* PrefixHeader.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PrefixHeader.pch; sourceTree = "<group>"; };
		8D0228E423E91CF7008EB3F4 /* JSONKit.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JSONKit.m; sourceTree = "<group>"; };
		8D0228E523E91CF7008EB3F4 /* JSONKit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JSONKit.h; sourceTree = "<group>"; };
		8D0228E823E97004008EB3F4 /* RRLoginVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRLoginVC.h; sourceTree = "<group>"; };
		8D0228E923E97004008EB3F4 /* RRLoginVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRLoginVC.m; sourceTree = "<group>"; };
		8D0228EC23E97152008EB3F4 /* RRTabbarVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRTabbarVC.h; sourceTree = "<group>"; };
		8D0228ED23E97152008EB3F4 /* RRTabbarVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRTabbarVC.m; sourceTree = "<group>"; };
		8D0228F023E9B400008EB3F4 /* RRHomeVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRHomeVC.h; sourceTree = "<group>"; };
		8D0228F123E9B400008EB3F4 /* RRHomeVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRHomeVC.m; sourceTree = "<group>"; };
		8D0228F323E9B492008EB3F4 /* RRNationalBaseVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRNationalBaseVC.h; sourceTree = "<group>"; };
		8D0228F423E9B492008EB3F4 /* RRNationalBaseVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRNationalBaseVC.m; sourceTree = "<group>"; };
		8D0228FA23E9B892008EB3F4 /* NSBundle+XNCategory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSBundle+XNCategory.h"; sourceTree = "<group>"; };
		8D0228FB23E9B893008EB3F4 /* XNLanguageConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XNLanguageConfig.h; sourceTree = "<group>"; };
		8D0228FC23E9B893008EB3F4 /* XNLanguageConfig.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XNLanguageConfig.m; sourceTree = "<group>"; };
		8D0228FD23E9B893008EB3F4 /* NSBundle+XNCategory.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSBundle+XNCategory.m"; sourceTree = "<group>"; };
		8D02290023E9B8E8008EB3F4 /* RRLeftVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRLeftVC.h; sourceTree = "<group>"; };
		8D02290123E9B8E8008EB3F4 /* RRLeftVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRLeftVC.m; sourceTree = "<group>"; };
		8D0F93B823FE52F200B04EAC /* RRSetLoginPwdVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRSetLoginPwdVC.h; sourceTree = "<group>"; };
		8D0F93B923FE52F300B04EAC /* RRSetLoginPwdVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRSetLoginPwdVC.m; sourceTree = "<group>"; };
		8D0F93BE23FE725E00B04EAC /* RRSplitBillVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRSplitBillVC.h; sourceTree = "<group>"; };
		8D0F93BF23FE725E00B04EAC /* RRSplitBillResultVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRSplitBillResultVC.h; sourceTree = "<group>"; };
		8D0F93C023FE725E00B04EAC /* RRSplitBillResultVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRSplitBillResultVC.m; sourceTree = "<group>"; };
		8D0F93C123FE725E00B04EAC /* RRSplitBillVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRSplitBillVC.m; sourceTree = "<group>"; };
		8D0F93C423FE74F400B04EAC /* RRBillContactListVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBillContactListVC.h; sourceTree = "<group>"; };
		8D0F93C523FE74F400B04EAC /* RRBillContactListVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBillContactListVC.m; sourceTree = "<group>"; };
		8D0F93C923FEC40000B04EAC /* RRBillPersonAmtView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBillPersonAmtView.h; sourceTree = "<group>"; };
		8D0F93CA23FEC40000B04EAC /* RRBillPersonAmtView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBillPersonAmtView.m; sourceTree = "<group>"; };
		8D0F93CC23FF6D2200B04EAC /* RRSplitIndividVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRSplitIndividVC.h; sourceTree = "<group>"; };
		8D0F93CD23FF6D2200B04EAC /* RRSplitIndividVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRSplitIndividVC.m; sourceTree = "<group>"; };
		8D0F93D0240010D500B04EAC /* RRPendingListVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRPendingListVC.h; sourceTree = "<group>"; };
		8D0F93D1240010D500B04EAC /* RRPendingListVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRPendingListVC.m; sourceTree = "<group>"; };
		8D0F93D4240011CE00B04EAC /* RRPendHeadView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRPendHeadView.h; sourceTree = "<group>"; };
		8D0F93D5240011CE00B04EAC /* RRPendHeadView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRPendHeadView.m; sourceTree = "<group>"; };
		8D0F93D824002B7A00B04EAC /* RRLssuedCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRLssuedCell.h; sourceTree = "<group>"; };
		8D0F93D924002B7A00B04EAC /* RRLssuedCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRLssuedCell.m; sourceTree = "<group>"; };
		8D0F93DB24002B9400B04EAC /* RRReceiveCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRReceiveCell.h; sourceTree = "<group>"; };
		8D0F93DC24002B9400B04EAC /* RRReceiveCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRReceiveCell.m; sourceTree = "<group>"; };
		8D0F93DE2400D9C000B04EAC /* RRLssuedDetailsVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRLssuedDetailsVC.h; sourceTree = "<group>"; };
		8D0F93DF2400D9C000B04EAC /* RRLssuedDetailsVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRLssuedDetailsVC.m; sourceTree = "<group>"; };
		8D0F93E12400D9DC00B04EAC /* RRReceivedDetailsVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRReceivedDetailsVC.h; sourceTree = "<group>"; };
		8D0F93E22400D9DC00B04EAC /* RRReceivedDetailsVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRReceivedDetailsVC.m; sourceTree = "<group>"; };
		8D10715C253ED10B0075A927 /* RRDeviceModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRDeviceModel.h; sourceTree = "<group>"; };
		8D10715D253ED10B0075A927 /* RRDeviceModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRDeviceModel.m; sourceTree = "<group>"; };
		8D1071F9254067120075A927 /* AppDelegate+JPushService.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "AppDelegate+JPushService.h"; sourceTree = "<group>"; };
		8D1071FA254067230075A927 /* AppDelegate+JPushService.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "AppDelegate+JPushService.m"; sourceTree = "<group>"; };
		8D109D17240C2DBF001261C8 /* RRPaymentQrCodeVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRPaymentQrCodeVC.h; sourceTree = "<group>"; };
		8D109D18240C2DBF001261C8 /* RRPaymentQrCodeVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRPaymentQrCodeVC.m; sourceTree = "<group>"; };
		8D109D1E240CE774001261C8 /* RRPaymentVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRPaymentVC.h; sourceTree = "<group>"; };
		8D109D1F240CE774001261C8 /* RRPaymentVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRPaymentVC.m; sourceTree = "<group>"; };
		8D109D21240CE789001261C8 /* RRPaymentResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRPaymentResultVC.h; sourceTree = "<group>"; };
		8D109D22240CE789001261C8 /* RRPaymentResultVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRPaymentResultVC.m; sourceTree = "<group>"; };
		8D1329952412688700CA6E11 /* RRBankCardListVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBankCardListVC.h; sourceTree = "<group>"; };
		8D1329962412688700CA6E11 /* RRBankCardListVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBankCardListVC.m; sourceTree = "<group>"; };
		8D13299924126AB800CA6E11 /* RRBankCardCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBankCardCell.h; sourceTree = "<group>"; };
		8D13299A24126AB800CA6E11 /* RRBankCardCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBankCardCell.m; sourceTree = "<group>"; };
		8D13299C2412815600CA6E11 /* RRAddBankCardVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRAddBankCardVC.h; sourceTree = "<group>"; };
		8D13299D2412815600CA6E11 /* RRAddBankCardVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRAddBankCardVC.m; sourceTree = "<group>"; };
		8D1329A02412880500CA6E11 /* RRBankNameVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBankNameVC.h; sourceTree = "<group>"; };
		8D1329A12412880500CA6E11 /* RRBankNameVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBankNameVC.m; sourceTree = "<group>"; };
		8D1329A3241289B000CA6E11 /* RRBankNameCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBankNameCell.h; sourceTree = "<group>"; };
		8D1329A4241289B000CA6E11 /* RRBankNameCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBankNameCell.m; sourceTree = "<group>"; };
		8D244C302447333D0031595E /* RRFlutterVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRFlutterVC.h; sourceTree = "<group>"; };
		8D244C312447333D0031595E /* RRFlutterVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRFlutterVC.m; sourceTree = "<group>"; };
		8D25D4BA24664BE3009F25BA /* configDev.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = configDev.h; sourceTree = "<group>"; };
		8D25D4BB24664BFA009F25BA /* configTest.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = configTest.h; sourceTree = "<group>"; };
		8D25D4BC24664C08009F25BA /* configFat.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = configFat.h; sourceTree = "<group>"; };
		8D25D4BD24664C15009F25BA /* configUat.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = configUat.h; sourceTree = "<group>"; };
		8D25D4C2246653D7009F25BA /* configPro.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = configPro.h; sourceTree = "<group>"; };
		8D25D4CA2466B321009F25BA /* RRUserInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRUserInfo.h; sourceTree = "<group>"; };
		8D25D4CB2466B321009F25BA /* RRUserInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRUserInfo.m; sourceTree = "<group>"; };
		8D25D4CE2466C412009F25BA /* RRModels.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRModels.h; sourceTree = "<group>"; };
		8D278C5C244F163900DB8DD9 /* RRNetworkModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRNetworkModel.h; sourceTree = "<group>"; };
		8D278C5D244F163900DB8DD9 /* RRNetworkModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRNetworkModel.m; sourceTree = "<group>"; };
		8D37C9A323EFCFC000E71E5D /* RRXNTextField.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRXNTextField.h; sourceTree = "<group>"; };
		8D37C9A423EFCFC000E71E5D /* RRXNTextField.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRXNTextField.m; sourceTree = "<group>"; };
		8D37C9A723EFF44000E71E5D /* RRCommonPickerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRCommonPickerView.h; sourceTree = "<group>"; };
		8D37C9A923EFF44000E71E5D /* RRCommonPickerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRCommonPickerView.m; sourceTree = "<group>"; };
		8D37C9AD23F0229C00E71E5D /* RRKeyBoardView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRKeyBoardView.m; sourceTree = "<group>"; };
		8D37C9AE23F0229C00E71E5D /* RRKeyBoardView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRKeyBoardView.h; sourceTree = "<group>"; };
		8D3F342123EABA6D009063F8 /* RRXNTextView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRXNTextView.h; sourceTree = "<group>"; };
		8D3F342223EABA6D009063F8 /* RRXNTextView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRXNTextView.m; sourceTree = "<group>"; };
		8D3F342523EBC12F009063F8 /* RRRegisterVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRRegisterVC.h; sourceTree = "<group>"; };
		8D3F342623EBC12F009063F8 /* RRRegisterVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRRegisterVC.m; sourceTree = "<group>"; };
		8D3F342823EBEDF5009063F8 /* RRXNTipLabel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRXNTipLabel.h; sourceTree = "<group>"; };
		8D3F342923EBEDF5009063F8 /* RRXNTipLabel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRXNTipLabel.m; sourceTree = "<group>"; };
		8D3F342F23EC3356009063F8 /* RRXNButton.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRXNButton.h; sourceTree = "<group>"; };
		8D3F343023EC3356009063F8 /* RRXNButton.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRXNButton.m; sourceTree = "<group>"; };
		8D3F343223EC6EFC009063F8 /* RRVerifyCodeVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRVerifyCodeVC.h; sourceTree = "<group>"; };
		8D3F343323EC6EFC009063F8 /* RRVerifyCodeVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRVerifyCodeVC.m; sourceTree = "<group>"; };
		8D3F343523EC797F009063F8 /* RRRegisterResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRRegisterResultVC.h; sourceTree = "<group>"; };
		8D3F343623EC797F009063F8 /* RRRegisterResultVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRRegisterResultVC.m; sourceTree = "<group>"; };
		8D3F343823ED0286009063F8 /* RRShadowButton.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRShadowButton.h; sourceTree = "<group>"; };
		8D3F343923ED0287009063F8 /* RRShadowButton.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRShadowButton.m; sourceTree = "<group>"; };
		8D3F343C23ED7493009063F8 /* RRAccountVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRAccountVC.h; sourceTree = "<group>"; };
		8D3F343D23ED7493009063F8 /* RRAccountVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRAccountVC.m; sourceTree = "<group>"; };
		8D3F344223ED9CCF009063F8 /* RRXNListBtn.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRXNListBtn.h; sourceTree = "<group>"; };
		8D3F344323ED9CCF009063F8 /* RRXNListBtn.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRXNListBtn.m; sourceTree = "<group>"; };
		8D3F344823EDC350009063F8 /* RRSecurityCenterVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRSecurityCenterVC.h; sourceTree = "<group>"; };
		8D3F344923EDC350009063F8 /* RRSecurityCenterVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRSecurityCenterVC.m; sourceTree = "<group>"; };
		8D3F344B23EE8EA9009063F8 /* RRPwdManageVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRPwdManageVC.h; sourceTree = "<group>"; };
		8D3F344C23EE8EA9009063F8 /* RRPwdManageVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRPwdManageVC.m; sourceTree = "<group>"; };
		8D3F344E23EE94D6009063F8 /* RREditLoginPwdVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RREditLoginPwdVC.h; sourceTree = "<group>"; };
		8D3F344F23EE94D6009063F8 /* RREditLoginPwdVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RREditLoginPwdVC.m; sourceTree = "<group>"; };
		8D3F345123EE94F1009063F8 /* RREditPayPwdVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RREditPayPwdVC.h; sourceTree = "<group>"; };
		8D3F345223EE94F1009063F8 /* RREditPayPwdVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RREditPayPwdVC.m; sourceTree = "<group>"; };
		8D3F345423EE9529009063F8 /* RRRetrievePayPwdVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRRetrievePayPwdVC.h; sourceTree = "<group>"; };
		8D3F345523EE9529009063F8 /* RRRetrievePayPwdVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRRetrievePayPwdVC.m; sourceTree = "<group>"; };
		8D3F345723EEBC70009063F8 /* RREditLoginPwdResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RREditLoginPwdResultVC.h; sourceTree = "<group>"; };
		8D3F345823EEBC70009063F8 /* RREditLoginPwdResultVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RREditLoginPwdResultVC.m; sourceTree = "<group>"; };
		8D3F345A23EEC702009063F8 /* RRMobileView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRMobileView.m; sourceTree = "<group>"; };
		8D3F345B23EEC702009063F8 /* RRMobileView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRMobileView.h; sourceTree = "<group>"; };
		8D3F345D23EECA51009063F8 /* RRAuthPayPwdVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRAuthPayPwdVC.h; sourceTree = "<group>"; };
		8D3F345E23EECA51009063F8 /* RRAuthPayPwdVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRAuthPayPwdVC.m; sourceTree = "<group>"; };
		8D3F346023EEE5A2009063F8 /* RREditPayPwdAgainVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RREditPayPwdAgainVC.h; sourceTree = "<group>"; };
		8D3F346123EEE5A3009063F8 /* RREditPayPwdAgainVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RREditPayPwdAgainVC.m; sourceTree = "<group>"; };
		8D3F346323EEE791009063F8 /* RREditPayPwdResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RREditPayPwdResultVC.h; sourceTree = "<group>"; };
		8D3F346423EEE791009063F8 /* RREditPayPwdResultVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RREditPayPwdResultVC.m; sourceTree = "<group>"; };
		8D56D5442484A91D0083608E /* RRHistoryDetailsModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRHistoryDetailsModel.h; sourceTree = "<group>"; };
		8D56D5452484A91D0083608E /* RRHistoryDetailsModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRHistoryDetailsModel.m; sourceTree = "<group>"; };
		8D5E74F22486303F00DC1C9E /* phoneCode_ru.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = phoneCode_ru.plist; sourceTree = "<group>"; };
		8D64EF502534043F00D2E08E /* RRTempDeviceLoginVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRTempDeviceLoginVC.h; sourceTree = "<group>"; };
		8D64EF512534043F00D2E08E /* RRTempDeviceLoginVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRTempDeviceLoginVC.m; sourceTree = "<group>"; };
		8D64EF552534307C00D2E08E /* RRAuthCodeUseListVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRAuthCodeUseListVC.h; sourceTree = "<group>"; };
		8D64EF562534307C00D2E08E /* RRAuthCodeUseListVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRAuthCodeUseListVC.m; sourceTree = "<group>"; };
		8D64EF5B253430CC00D2E08E /* RRAuthCodeUseListCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRAuthCodeUseListCell.h; sourceTree = "<group>"; };
		8D64EF5C253430CC00D2E08E /* RRAuthCodeUseListCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRAuthCodeUseListCell.m; sourceTree = "<group>"; };
		8D64EF622534310300D2E08E /* RRAuthCodeUseListModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRAuthCodeUseListModel.h; sourceTree = "<group>"; };
		8D64EF632534310300D2E08E /* RRAuthCodeUseListModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRAuthCodeUseListModel.m; sourceTree = "<group>"; };
		8D64EF7C25358FA200D2E08E /* RRChangeMasterDeviceResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRChangeMasterDeviceResultVC.h; sourceTree = "<group>"; };
		8D64EF7D25358FA200D2E08E /* RRChangeMasterDeviceResultVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRChangeMasterDeviceResultVC.m; sourceTree = "<group>"; };
		8D6685D9241C8E8E00AEC5DC /* XNExceptionHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XNExceptionHandler.h; sourceTree = "<group>"; };
		8D6685DA241C8E8E00AEC5DC /* XNExceptionHandler.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XNExceptionHandler.m; sourceTree = "<group>"; };
		8D6685DD241CB97400AEC5DC /* RRRetrieveUsernameVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRRetrieveUsernameVC.m; sourceTree = "<group>"; };
		8D6685DE241CB97400AEC5DC /* RRRetrieveResultVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRRetrieveResultVC.m; sourceTree = "<group>"; };
		8D6685DF241CB97400AEC5DC /* RRRetrievePwdVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRRetrievePwdVC.h; sourceTree = "<group>"; };
		8D6685E0241CB97400AEC5DC /* RRRetrieveUsernameVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRRetrieveUsernameVC.h; sourceTree = "<group>"; };
		8D6685E1241CB97400AEC5DC /* RRRetrieveResultVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRRetrieveResultVC.h; sourceTree = "<group>"; };
		8D6685E2241CB97400AEC5DC /* RRRetrievePwdVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRRetrievePwdVC.m; sourceTree = "<group>"; };
		8D6685EB241CEF3400AEC5DC /* RRAboutVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRAboutVC.h; sourceTree = "<group>"; };
		8D6685EC241CEF3400AEC5DC /* RRAboutVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRAboutVC.m; sourceTree = "<group>"; };
		8D66E0F9258394BA004853DD /* loading.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = loading.json; sourceTree = "<group>"; };
		8D6CA50F24B0287B00BCF643 /* RRTopUpVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRTopUpVC.h; sourceTree = "<group>"; };
		8D6CA51024B0287B00BCF643 /* RRTopUpVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRTopUpVC.m; sourceTree = "<group>"; };
		8D6CA51224B047DF00BCF643 /* RRTopUpResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRTopUpResultVC.h; sourceTree = "<group>"; };
		8D6CA51324B047DF00BCF643 /* RRTopUpResultVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRTopUpResultVC.m; sourceTree = "<group>"; };
		8D73D81E24F3E3FF0022ABBD /* UIView+CornerRadius.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+CornerRadius.h"; sourceTree = "<group>"; };
		8D73D81F24F3E3FF0022ABBD /* UIView+CornerRadius.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+CornerRadius.m"; sourceTree = "<group>"; };
		8D7C04002403698900FD57A3 /* RRFixedTransferVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRFixedTransferVC.h; sourceTree = "<group>"; };
		8D7C04012403698900FD57A3 /* RRFixedTransferVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRFixedTransferVC.m; sourceTree = "<group>"; };
		8D7C04042403AD4E00FD57A3 /* RRXNScanVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRXNScanVC.h; sourceTree = "<group>"; };
		8D7C04052403AD4E00FD57A3 /* RRXNScanVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRXNScanVC.m; sourceTree = "<group>"; };
		8D7C04072403D47900FD57A3 /* RRXNStyleDIY.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRXNStyleDIY.m; sourceTree = "<group>"; };
		8D7C04082403D47900FD57A3 /* RRXNStyleDIY.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRXNStyleDIY.h; sourceTree = "<group>"; };
		8D7D2FC2241D370700775E59 /* RRNavigationController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRNavigationController.h; sourceTree = "<group>"; };
		8D7D2FC3241D370700775E59 /* RRNavigationController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRNavigationController.m; sourceTree = "<group>"; };
		8D7D2FC5241D470400775E59 /* RRDatePicker.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRDatePicker.h; sourceTree = "<group>"; };
		8D7D2FC6241D470400775E59 /* RRDatePicker.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRDatePicker.m; sourceTree = "<group>"; };
		8D94697D2422849500F41711 /* RRPortraitSetVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRPortraitSetVC.m; sourceTree = "<group>"; };
		8D94697E2422849500F41711 /* RRPortraitSetVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRPortraitSetVC.h; sourceTree = "<group>"; };
		8D946981242284D600F41711 /* RRNativeCheckoutResultVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRNativeCheckoutResultVC.h; sourceTree = "<group>"; };
		8D946982242284D600F41711 /* RRNativeCheckoutVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRNativeCheckoutVC.h; sourceTree = "<group>"; };
		8D946984242284D600F41711 /* RRCheckoutView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRCheckoutView.m; sourceTree = "<group>"; };
		8D946985242284D600F41711 /* RRCheckoutView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRCheckoutView.h; sourceTree = "<group>"; };
		8D946986242284D600F41711 /* RRNativeCheckoutVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRNativeCheckoutVC.m; sourceTree = "<group>"; };
		8D946987242284D600F41711 /* RRNativeCheckoutResultVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRNativeCheckoutResultVC.m; sourceTree = "<group>"; };
		8D996C3F24109F480020E6EF /* UIButton+Category.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIButton+Category.h"; sourceTree = "<group>"; };
		8D996C4024109F480020E6EF /* UIButton+Category.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIButton+Category.m"; sourceTree = "<group>"; };
		8D9F057F23F1AC97004D0817 /* RRXNLoginInputView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRXNLoginInputView.h; sourceTree = "<group>"; };
		8D9F058023F1AC97004D0817 /* RRXNLoginInputView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRXNLoginInputView.m; sourceTree = "<group>"; };
		8DA0BE7724B7178F00EF4D4D /* RRVersionUpdateView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRVersionUpdateView.h; sourceTree = "<group>"; };
		8DA0BE7824B7178F00EF4D4D /* RRVersionUpdateView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRVersionUpdateView.m; sourceTree = "<group>"; };
		8DA3AAC72578D30100B7354C /* RRHomeFunCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRHomeFunCell.h; sourceTree = "<group>"; };
		8DA3AAC82578D30100B7354C /* RRHomeFunCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRHomeFunCell.m; sourceTree = "<group>"; };
		8DA88D1E23FBB9710057A82F /* RRHistoryListVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRHistoryListVC.h; sourceTree = "<group>"; };
		8DA88D1F23FBB9710057A82F /* RRHistoryListVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRHistoryListVC.m; sourceTree = "<group>"; };
		8DA88D2223FBE78A0057A82F /* RRHistoryListCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRHistoryListCell.h; sourceTree = "<group>"; };
		8DA88D2323FBE78A0057A82F /* RRHistoryListCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRHistoryListCell.m; sourceTree = "<group>"; };
		8DA88D2523FC028E0057A82F /* RRHistoryListDetailsVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRHistoryListDetailsVC.h; sourceTree = "<group>"; };
		8DA88D2623FC028E0057A82F /* RRHistoryListDetailsVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRHistoryListDetailsVC.m; sourceTree = "<group>"; };
		8DA88D2823FC15610057A82F /* OrderedDictionary.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OrderedDictionary.h; sourceTree = "<group>"; };
		8DA88D2923FC15610057A82F /* OrderedDictionary.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = OrderedDictionary.m; sourceTree = "<group>"; };
		8DB4A4D423F6EED80095A49F /* RRTransferResultVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRTransferResultVC.h; sourceTree = "<group>"; };
		8DB4A4D523F6EED90095A49F /* RRTransferResultVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRTransferResultVC.m; sourceTree = "<group>"; };
		8DB8894E250B2DE5006A5AC3 /* RRInviteFriendsWebVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRInviteFriendsWebVC.h; sourceTree = "<group>"; };
		8DB8894F250B2DE5006A5AC3 /* RRInviteFriendsWebVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRInviteFriendsWebVC.m; sourceTree = "<group>"; };
		8DB88953250C78FF006A5AC3 /* RRDeviceManageVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRDeviceManageVC.h; sourceTree = "<group>"; };
		8DB88954250C78FF006A5AC3 /* RRDeviceManageVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRDeviceManageVC.m; sourceTree = "<group>"; };
		8DB88956250CA922006A5AC3 /* RRFirstSetLoginPwdResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRFirstSetLoginPwdResultVC.h; sourceTree = "<group>"; };
		8DB88957250CA923006A5AC3 /* RRFirstSetLoginPwdResultVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRFirstSetLoginPwdResultVC.m; sourceTree = "<group>"; };
		8DB88959250CDB52006A5AC3 /* RRAuthCodeDisplayVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRAuthCodeDisplayVC.h; sourceTree = "<group>"; };
		8DB8895A250CDB52006A5AC3 /* RRAuthCodeDisplayVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRAuthCodeDisplayVC.m; sourceTree = "<group>"; };
		8DBB5B8023FB89EC00CFEF1A /* RRHUDView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRHUDView.h; sourceTree = "<group>"; };
		8DBB5B8123FB89ED00CFEF1A /* RRHUDView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRHUDView.m; sourceTree = "<group>"; };
		8DBB5BFC23FB8CE000CFEF1A /* mobileImg.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = mobileImg.bundle; sourceTree = "<group>"; };
		8DC42C2024358506003FF9BF /* phoneCode_en.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = phoneCode_en.plist; sourceTree = "<group>"; };
		8DC42C22243596DC003FF9BF /* phoneCode_zh.plist */ = {isa = PBXFileReference; lastKnownFileType = file.bplist; path = phoneCode_zh.plist; sourceTree = "<group>"; };
		8DC829A52477F55A007EBA45 /* xWalletPro_iOS.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = xWalletPro_iOS.entitlements; sourceTree = "<group>"; };
		8DC829A624791A60007EBA45 /* RRPaymentMethodModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRPaymentMethodModel.h; sourceTree = "<group>"; };
		8DC829A724791A60007EBA45 /* RRPaymentMethodModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRPaymentMethodModel.m; sourceTree = "<group>"; };
		8DCCDCA82546B36E008E1793 /* RRAVPlayer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRAVPlayer.m; sourceTree = "<group>"; };
		8DCCDCA92546B36E008E1793 /* RRAVPlayer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRAVPlayer.h; sourceTree = "<group>"; };
		8DCCDCAE2546B869008E1793 /* RRBroadcastVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBroadcastVC.h; sourceTree = "<group>"; };
		8DCCDCAF2546B869008E1793 /* RRBroadcastVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBroadcastVC.m; sourceTree = "<group>"; };
		8DCFC7DC25594218000EF0AB /* pushServiceExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = pushServiceExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		8DCFC7DE25594218000EF0AB /* NotificationService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NotificationService.h; sourceTree = "<group>"; };
		8DCFC7DF25594218000EF0AB /* NotificationService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NotificationService.m; sourceTree = "<group>"; };
		8DCFC7E125594218000EF0AB /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		8DCFC7F825595268000EF0AB /* 123.m4a */ = {isa = PBXFileReference; lastKnownFileType = file; path = 123.m4a; sourceTree = "<group>"; };
		8DCFC7FE255953ED000EF0AB /* 456.m4a */ = {isa = PBXFileReference; lastKnownFileType = file; path = 456.m4a; sourceTree = "<group>"; };
		8DD4F3182412AF6200FB55EF /* RRBindBankCardResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBindBankCardResultVC.h; sourceTree = "<group>"; };
		8DD4F3192412AF6200FB55EF /* RRBindBankCardResultVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBindBankCardResultVC.m; sourceTree = "<group>"; };
		8DD4F31C241377A600FB55EF /* RRWithdrawalVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRWithdrawalVC.h; sourceTree = "<group>"; };
		8DD4F31D241377A600FB55EF /* RRWithdrawalVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRWithdrawalVC.m; sourceTree = "<group>"; };
		8DD4F31F2413A55E00FB55EF /* RRWithdrawalResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRWithdrawalResultVC.h; sourceTree = "<group>"; };
		8DD4F3202413A55E00FB55EF /* RRWithdrawalResultVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRWithdrawalResultVC.m; sourceTree = "<group>"; };
		8DD4F3232413C39D00FB55EF /* RRWithdrawalStatusView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRWithdrawalStatusView.h; sourceTree = "<group>"; };
		8DD4F3242413C39D00FB55EF /* RRWithdrawalStatusView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRWithdrawalStatusView.m; sourceTree = "<group>"; };
		8DD4F3272413F57B00FB55EF /* RRPersonalInfoVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRPersonalInfoVC.h; sourceTree = "<group>"; };
		8DD4F3282413F57B00FB55EF /* RRPersonalInfoVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRPersonalInfoVC.m; sourceTree = "<group>"; };
		8DD4F32B241411B500FB55EF /* RRLoginLockVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRLoginLockVC.h; sourceTree = "<group>"; };
		8DD4F32C241411B500FB55EF /* RRLoginLockVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRLoginLockVC.m; sourceTree = "<group>"; };
		8DD4F32E2414148800FB55EF /* RRModifyNicknameVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRModifyNicknameVC.h; sourceTree = "<group>"; };
		8DD4F32F2414148800FB55EF /* RRModifyNicknameVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRModifyNicknameVC.m; sourceTree = "<group>"; };
		8DD4F331241414A900FB55EF /* RRModifyEmailVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRModifyEmailVC.h; sourceTree = "<group>"; };
		8DD4F332241414A900FB55EF /* RRModifyEmailVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRModifyEmailVC.m; sourceTree = "<group>"; };
		8DD4F33524141D0100FB55EF /* RRModifyMobileVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRModifyMobileVC.h; sourceTree = "<group>"; };
		8DD4F33624141D0100FB55EF /* RRModifyMobileVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRModifyMobileVC.m; sourceTree = "<group>"; };
		8DD4F3392415608500FB55EF /* RRLoginUnlockVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRLoginUnlockVC.h; sourceTree = "<group>"; };
		8DD4F33A2415608500FB55EF /* RRLoginUnlockVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRLoginUnlockVC.m; sourceTree = "<group>"; };
		8DD4F33C2415672C00FB55EF /* RRLoginPwdUnlockVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRLoginPwdUnlockVC.h; sourceTree = "<group>"; };
		8DD4F33D2415672C00FB55EF /* RRLoginPwdUnlockVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRLoginPwdUnlockVC.m; sourceTree = "<group>"; };
		8DD4F3432416572700FB55EF /* RRContactTransferRecordVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRContactTransferRecordVC.h; sourceTree = "<group>"; };
		8DD4F3442416572700FB55EF /* RRContactTransferRecordVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRContactTransferRecordVC.m; sourceTree = "<group>"; };
		8DD4F3462416788D00FB55EF /* RRModifyMobileResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRModifyMobileResultVC.h; sourceTree = "<group>"; };
		8DD4F3472416788D00FB55EF /* RRModifyMobileResultVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRModifyMobileResultVC.m; sourceTree = "<group>"; };
		8DE1DF3623F79148002D04CA /* UITextField+Category.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UITextField+Category.h"; sourceTree = "<group>"; };
		8DE1DF3723F79148002D04CA /* UITextField+Category.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UITextField+Category.m"; sourceTree = "<group>"; };
		8DE5C5932536A432000712E0 /* RRPromotionImgModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRPromotionImgModel.h; sourceTree = "<group>"; };
		8DE5C5942536A432000712E0 /* RRPromotionImgModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRPromotionImgModel.m; sourceTree = "<group>"; };
		8DE5C59E2536D573000712E0 /* RRFloatPictureView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRFloatPictureView.h; sourceTree = "<group>"; };
		8DE5C59F2536D573000712E0 /* RRFloatPictureView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRFloatPictureView.m; sourceTree = "<group>"; };
		8DE674A02468FEAF00E52F70 /* RRContact.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRContact.h; sourceTree = "<group>"; };
		8DE674A12468FEAF00E52F70 /* RRContact.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRContact.m; sourceTree = "<group>"; };
		8DE674A3246A8CDB00E52F70 /* RRHistoryModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRHistoryModel.h; sourceTree = "<group>"; };
		8DE674A4246A8CDB00E52F70 /* RRHistoryModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRHistoryModel.m; sourceTree = "<group>"; };
		8DE674A6246AA14400E52F70 /* RRLssuedModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRLssuedModel.h; sourceTree = "<group>"; };
		8DE674A7246AA14400E52F70 /* RRLssuedModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRLssuedModel.m; sourceTree = "<group>"; };
		8DE674A9246AA18900E52F70 /* RRReceiveModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRReceiveModel.h; sourceTree = "<group>"; };
		8DE674AA246AA18900E52F70 /* RRReceiveModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRReceiveModel.m; sourceTree = "<group>"; };
		8DE674AF246AA38000E52F70 /* RRCardModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRCardModel.h; sourceTree = "<group>"; };
		8DE674B0246AA38000E52F70 /* RRCardModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRCardModel.m; sourceTree = "<group>"; };
		8DE674B3246B9C3400E52F70 /* RRBillPersonAmtModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBillPersonAmtModel.h; sourceTree = "<group>"; };
		8DE674B4246B9C3400E52F70 /* RRBillPersonAmtModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBillPersonAmtModel.m; sourceTree = "<group>"; };
		8DEE998623F546B900C47CB3 /* RRReceiveQrCodeVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRReceiveQrCodeVC.h; sourceTree = "<group>"; };
		8DEE998723F546B900C47CB3 /* RRReceiveQrCodeVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRReceiveQrCodeVC.m; sourceTree = "<group>"; };
		8DEE998923F5642800C47CB3 /* RRReceiveQrCodeSetPriceVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRReceiveQrCodeSetPriceVC.h; sourceTree = "<group>"; };
		8DEE998A23F5642800C47CB3 /* RRReceiveQrCodeSetPriceVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRReceiveQrCodeSetPriceVC.m; sourceTree = "<group>"; };
		8DEE998D23F56E2800C47CB3 /* RRTransferListVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRTransferListVC.h; sourceTree = "<group>"; };
		8DEE998E23F56E2800C47CB3 /* RRTransferListVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRTransferListVC.m; sourceTree = "<group>"; };
		8DEE999023F58E6300C47CB3 /* RRTransferAccountVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRTransferAccountVC.h; sourceTree = "<group>"; };
		8DEE999123F58E6300C47CB3 /* RRTransferAccountVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRTransferAccountVC.m; sourceTree = "<group>"; };
		8DEE999323F5925700C47CB3 /* RRTransferVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRTransferVC.h; sourceTree = "<group>"; };
		8DEE999423F5925700C47CB3 /* RRTransferVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRTransferVC.m; sourceTree = "<group>"; };
		8DEE999723F5A4B300C47CB3 /* RRContactListVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRContactListVC.h; sourceTree = "<group>"; };
		8DEE999823F5A4B300C47CB3 /* RRContactListVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRContactListVC.m; sourceTree = "<group>"; };
		8DEE999A23F5AA9900C47CB3 /* RRContactAddVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRContactAddVC.h; sourceTree = "<group>"; };
		8DEE999B23F5AA9900C47CB3 /* RRContactAddVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRContactAddVC.m; sourceTree = "<group>"; };
		8DEE999D23F5ABFE00C47CB3 /* RRContactInfVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRContactInfVC.h; sourceTree = "<group>"; };
		8DEE999E23F5ABFE00C47CB3 /* RRContactInfVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRContactInfVC.m; sourceTree = "<group>"; };
		8DF9622F249883600005DB6E /* RRContactUsVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRContactUsVC.h; sourceTree = "<group>"; };
		8DF96230249883600005DB6E /* RRContactUsVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRContactUsVC.m; sourceTree = "<group>"; };
		B4016DAC2B4CFAB90072D554 /* RRConfirmPaymentVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRConfirmPaymentVC.h; sourceTree = "<group>"; };
		B4016DAD2B4CFAB90072D554 /* RRConfirmPaymentVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRConfirmPaymentVC.m; sourceTree = "<group>"; };
		B4079FA42AE229970076460D /* CouponSDK.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CouponSDK.h; sourceTree = "<group>"; };
		B4079FA52AE229970076460D /* CouponSDK.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CouponSDK.m; sourceTree = "<group>"; };
		B40FEDB0272BE2ED00640151 /* RRBlurView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBlurView.h; sourceTree = "<group>"; };
		B40FEDB1272BE2ED00640151 /* RRBlurView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBlurView.m; sourceTree = "<group>"; };
		B40FEDB3272C020A00640151 /* RRWarningView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRWarningView.h; sourceTree = "<group>"; };
		B40FEDB4272C020A00640151 /* RRWarningView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRWarningView.m; sourceTree = "<group>"; };
		B421BE782B3588DF00488ACF /* RRAirtimeBundleResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRAirtimeBundleResultVC.h; sourceTree = "<group>"; };
		B421BE792B3588DF00488ACF /* RRAirtimeBundleResultVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRAirtimeBundleResultVC.m; sourceTree = "<group>"; };
		B44A90132B441C7200E8B1A3 /* RRMoreFunctionVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRMoreFunctionVC.h; sourceTree = "<group>"; };
		B44A90142B441C7200E8B1A3 /* RRMoreFunctionVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRMoreFunctionVC.m; sourceTree = "<group>"; };
		B44A90162B441D5700E8B1A3 /* RRMoreFuctionCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRMoreFuctionCell.h; sourceTree = "<group>"; };
		B44A90172B441D5700E8B1A3 /* RRMoreFuctionCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRMoreFuctionCell.m; sourceTree = "<group>"; };
		B44C86FC2A09E3BE00FA25DB /* RRJWMerchantDetailsModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRJWMerchantDetailsModel.h; sourceTree = "<group>"; };
		B44C86FD2A09E3BE00FA25DB /* RRJWMerchantDetailsModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRJWMerchantDetailsModel.m; sourceTree = "<group>"; };
		B453B0C02B3292B7008D787C /* RRMobileTopView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRMobileTopView.h; sourceTree = "<group>"; };
		B453B0C12B3292B7008D787C /* RRMobileTopView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRMobileTopView.m; sourceTree = "<group>"; };
		B453B0C32B3292D1008D787C /* RRBundleTopView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBundleTopView.h; sourceTree = "<group>"; };
		B453B0C42B3292D1008D787C /* RRBundleTopView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBundleTopView.m; sourceTree = "<group>"; };
		B46813422AB94C8F002C3438 /* RRLoginLockView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRLoginLockView.h; sourceTree = "<group>"; };
		B46813432AB94C8F002C3438 /* RRLoginLockView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRLoginLockView.m; sourceTree = "<group>"; };
		B46813452AB98962002C3438 /* RRLoginPwdVerifyVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRLoginPwdVerifyVC.h; sourceTree = "<group>"; };
		B46813462AB98962002C3438 /* RRLoginPwdVerifyVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRLoginPwdVerifyVC.m; sourceTree = "<group>"; };
		B468134C2AB99A22002C3438 /* Header.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Header.h; sourceTree = "<group>"; };
		B468134F2AB99A22002C3438 /* AliPayItem.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AliPayItem.m; sourceTree = "<group>"; };
		B46813552AB99A22002C3438 /* AliPayItem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AliPayItem.h; sourceTree = "<group>"; };
		B468135C2AB99A34002C3438 /* RRGestureViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRGestureViewController.h; sourceTree = "<group>"; };
		B468135D2AB99A34002C3438 /* RRGestureViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRGestureViewController.m; sourceTree = "<group>"; };
		B468135F2AB9BF48002C3438 /* RRGestureLockView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRGestureLockView.h; sourceTree = "<group>"; };
		B46813602AB9BF48002C3438 /* RRGestureLockView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRGestureLockView.m; sourceTree = "<group>"; };
		B46C2C792A088C9B0075C196 /* UIView+JWExtension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+JWExtension.h"; sourceTree = "<group>"; };
		B46C2C7A2A088C9B0075C196 /* UIView+JWExtension.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+JWExtension.m"; sourceTree = "<group>"; };
		B46C2C7C2A088C9B0075C196 /* RRJWMerchantInfomationVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRJWMerchantInfomationVC.m; sourceTree = "<group>"; };
		B46C2C7D2A088C9B0075C196 /* RRJWMerchantListVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRJWMerchantListVC.m; sourceTree = "<group>"; };
		B46C2C7E2A088C9B0075C196 /* RRJWMerchantInfomationVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRJWMerchantInfomationVC.h; sourceTree = "<group>"; };
		B46C2C7F2A088C9B0075C196 /* RRJWMerchantListVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRJWMerchantListVC.h; sourceTree = "<group>"; };
		B46C2C812A088C9B0075C196 /* RRJWMerchantModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRJWMerchantModel.m; sourceTree = "<group>"; };
		B46C2C822A088C9B0075C196 /* RRJWMerchantModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRJWMerchantModel.h; sourceTree = "<group>"; };
		B46C2C842A088C9B0075C196 /* RRJWShopInfoView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRJWShopInfoView.h; sourceTree = "<group>"; };
		B46C2C852A088C9B0075C196 /* RRJWMerchantInfomationAddressCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRJWMerchantInfomationAddressCell.m; sourceTree = "<group>"; };
		B46C2C862A088C9B0075C196 /* RRJWMerchantInfomationHeaderView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRJWMerchantInfomationHeaderView.m; sourceTree = "<group>"; };
		B46C2C872A088C9B0075C196 /* RRJWMerchantListFiltrateView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRJWMerchantListFiltrateView.m; sourceTree = "<group>"; };
		B46C2C882A088C9B0075C196 /* RRJWBaseView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRJWBaseView.m; sourceTree = "<group>"; };
		B46C2C892A088C9B0075C196 /* RRJWMerchantInfomationCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRJWMerchantInfomationCell.m; sourceTree = "<group>"; };
		B46C2C8A2A088C9B0075C196 /* RRJWMerchantListCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRJWMerchantListCell.m; sourceTree = "<group>"; };
		B46C2C8B2A088C9B0075C196 /* RRJWMerchantInfomationAddressCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRJWMerchantInfomationAddressCell.h; sourceTree = "<group>"; };
		B46C2C8C2A088C9B0075C196 /* RRJWShopInfoView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRJWShopInfoView.m; sourceTree = "<group>"; };
		B46C2C8D2A088C9B0075C196 /* RRJWMerchantListFiltrateView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRJWMerchantListFiltrateView.h; sourceTree = "<group>"; };
		B46C2C8E2A088C9B0075C196 /* RRJWMerchantInfomationHeaderView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRJWMerchantInfomationHeaderView.h; sourceTree = "<group>"; };
		B46C2C8F2A088C9B0075C196 /* RRJWMerchantListCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRJWMerchantListCell.h; sourceTree = "<group>"; };
		B46C2C902A088C9B0075C196 /* RRJWMerchantInfomationCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRJWMerchantInfomationCell.h; sourceTree = "<group>"; };
		B46C2C912A088C9B0075C196 /* RRJWBaseView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRJWBaseView.h; sourceTree = "<group>"; };
		B470C7922B24957C000C47F1 /* RRBillManagerVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBillManagerVC.h; sourceTree = "<group>"; };
		B470C7932B24957C000C47F1 /* RRBillManagerVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBillManagerVC.m; sourceTree = "<group>"; };
		B470C7962B2496E2000C47F1 /* RRBIillTypeCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBIillTypeCell.h; sourceTree = "<group>"; };
		B470C7972B2496E2000C47F1 /* RRBIillTypeCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBIillTypeCell.m; sourceTree = "<group>"; };
		B470C7992B249A29000C47F1 /* RRBillInfoInputVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBillInfoInputVC.h; sourceTree = "<group>"; };
		B470C79A2B249A29000C47F1 /* RRBillInfoInputVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBillInfoInputVC.m; sourceTree = "<group>"; };
		B470C79C2B24A05B000C47F1 /* RRBillAmtInputVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBillAmtInputVC.h; sourceTree = "<group>"; };
		B470C79D2B24A05B000C47F1 /* RRBillAmtInputVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBillAmtInputVC.m; sourceTree = "<group>"; };
		B470C79F2B24AC80000C47F1 /* RRBillPaymentResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBillPaymentResultVC.h; sourceTree = "<group>"; };
		B470C7A02B24AC80000C47F1 /* RRBillPaymentResultVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBillPaymentResultVC.m; sourceTree = "<group>"; };
		B470C7A22B24ADD1000C47F1 /* RRBIllPaymentRecordsVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBIllPaymentRecordsVC.h; sourceTree = "<group>"; };
		B470C7A32B24ADD1000C47F1 /* RRBIllPaymentRecordsVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBIllPaymentRecordsVC.m; sourceTree = "<group>"; };
		B470C7A52B24ADFB000C47F1 /* RRBillPaymentRecordCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBillPaymentRecordCell.h; sourceTree = "<group>"; };
		B470C7A62B24ADFB000C47F1 /* RRBillPaymentRecordCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBillPaymentRecordCell.m; sourceTree = "<group>"; };
		B496F7102AEFC2DC0060DB92 /* sn-ZW */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "sn-ZW"; path = sn.lproj/Localizable.strings; sourceTree = "<group>"; };
		B496F7132AEFC2FC0060DB92 /* nd-ZW */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "nd-ZW"; path = nr.lproj/Localizable.strings; sourceTree = "<group>"; };
		B496F7142AEFC2FC0060DB92 /* nd-ZW */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "nd-ZW"; path = nr.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		B49C98732730E4B4007F2C9E /* func.list */ = {isa = PBXFileReference; lastKnownFileType = text; path = func.list; sourceTree = "<group>"; };
		B49C98742730E4B4007F2C9E /* confuse.sh */ = {isa = PBXFileReference; lastKnownFileType = text.script.sh; path = confuse.sh; sourceTree = "<group>"; };
		B49C987D273382F8007F2C9E /* md5.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = md5.json; sourceTree = "<group>"; };
		B4A9AF2C2AA8500D000CF6A1 /* RRCurrencyAccountView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRCurrencyAccountView.h; sourceTree = "<group>"; };
		B4A9AF2D2AA8500D000CF6A1 /* RRCurrencyAccountView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRCurrencyAccountView.m; sourceTree = "<group>"; };
		B4BBD29C2B5E3C0C00EAAD97 /* RRXNInputField.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRXNInputField.m; sourceTree = "<group>"; };
		B4BBD29D2B5E3C0C00EAAD97 /* RRXNInputField.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRXNInputField.h; sourceTree = "<group>"; };
		B4C1604C29A704DF002D5740 /* RRBalanceRecordModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBalanceRecordModel.h; sourceTree = "<group>"; };
		B4C1604D29A704DF002D5740 /* RRBalanceRecordModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBalanceRecordModel.m; sourceTree = "<group>"; };
		B4C1605229A70BBB002D5740 /* RRAccountRecordDetailsVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRAccountRecordDetailsVC.h; sourceTree = "<group>"; };
		B4C1605329A70BBB002D5740 /* RRAccountRecordDetailsVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRAccountRecordDetailsVC.m; sourceTree = "<group>"; };
		B4C3ED802B10966000FC3D53 /* RRWithdrawalAccountPickView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRWithdrawalAccountPickView.h; sourceTree = "<group>"; };
		B4C3ED812B10966000FC3D53 /* RRWithdrawalAccountPickView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRWithdrawalAccountPickView.m; sourceTree = "<group>"; };
		B4D17A182B2E91700090D568 /* RRAirTimeBundleTopVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRAirTimeBundleTopVC.h; sourceTree = "<group>"; };
		B4D17A192B2E91700090D568 /* RRAirTimeBundleTopVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRAirTimeBundleTopVC.m; sourceTree = "<group>"; };
		B4D17A1B2B2EF71C0090D568 /* RRBillerInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBillerInfoModel.h; sourceTree = "<group>"; };
		B4D17A1C2B2EF71C0090D568 /* RRBillerInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBillerInfoModel.m; sourceTree = "<group>"; };
		B4DB370829A5F2B3009B7529 /* RRAccountHistoryVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRAccountHistoryVC.h; sourceTree = "<group>"; };
		B4DB370929A5F2B3009B7529 /* RRAccountHistoryVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRAccountHistoryVC.m; sourceTree = "<group>"; };
		B4DB370B29A5F5B2009B7529 /* RRAccountHistoryListCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRAccountHistoryListCell.h; sourceTree = "<group>"; };
		B4DB370C29A5F5B2009B7529 /* RRAccountHistoryListCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRAccountHistoryListCell.m; sourceTree = "<group>"; };
		B4DB370E29A5FD25009B7529 /* RRBalanceView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBalanceView.h; sourceTree = "<group>"; };
		B4DB370F29A5FD25009B7529 /* RRBalanceView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBalanceView.m; sourceTree = "<group>"; };
		B4DC2B3529BF220C00FF8162 /* RRCommonWebVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRCommonWebVC.h; sourceTree = "<group>"; };
		B4DC2B3629BF220C00FF8162 /* RRCommonWebVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRCommonWebVC.m; sourceTree = "<group>"; };
		B4DC2B3829BF221700FF8162 /* RRTermProtocolVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRTermProtocolVC.h; sourceTree = "<group>"; };
		B4DC2B3929BF221700FF8162 /* RRTermProtocolVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRTermProtocolVC.m; sourceTree = "<group>"; };
		B4DFE1332AA99A97007BA3AC /* HMSegmentedControl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HMSegmentedControl.h; sourceTree = "<group>"; };
		B4DFE1342AA99A98007BA3AC /* HMSegmentedControl.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HMSegmentedControl.m; sourceTree = "<group>"; };
		B4DFE1362AA9A101007BA3AC /* RRActiveAccountVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRActiveAccountVC.h; sourceTree = "<group>"; };
		B4DFE1372AA9A101007BA3AC /* RRActiveAccountVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRActiveAccountVC.m; sourceTree = "<group>"; };
		B4E018332AEA3C5500531BD0 /* RRSelectLanguageVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRSelectLanguageVC.h; sourceTree = "<group>"; };
		B4E018342AEA3C5500531BD0 /* RRSelectLanguageVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRSelectLanguageVC.m; sourceTree = "<group>"; };
		B4E018372AEA4ADE00531BD0 /* RRRegisterProcessView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRRegisterProcessView.h; sourceTree = "<group>"; };
		B4E018382AEA4ADE00531BD0 /* RRRegisterProcessView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRRegisterProcessView.m; sourceTree = "<group>"; };
		B4E0183B2AEE0DC500531BD0 /* CXDatePickerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CXDatePickerView.m; sourceTree = "<group>"; };
		B4E0183C2AEE0DC500531BD0 /* CXDatePickerConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CXDatePickerConfig.h; sourceTree = "<group>"; };
		B4E0183D2AEE0DC500531BD0 /* NSDate+CXCategory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSDate+CXCategory.h"; sourceTree = "<group>"; };
		B4E0183E2AEE0DC500531BD0 /* CXDatePickerViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CXDatePickerViewManager.h; sourceTree = "<group>"; };
		B4E0183F2AEE0DC500531BD0 /* CXDatePickerStyle.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CXDatePickerStyle.h; sourceTree = "<group>"; };
		B4E018402AEE0DC500531BD0 /* CXDatePickerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CXDatePickerView.h; sourceTree = "<group>"; };
		B4E018412AEE0DC500531BD0 /* NSDate+CXCategory.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSDate+CXCategory.m"; sourceTree = "<group>"; };
		B4E018422AEE0DC500531BD0 /* CXDatePickerViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CXDatePickerViewManager.m; sourceTree = "<group>"; };
		B4E02F782B392564000D9856 /* RRAirtimeBundleModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRAirtimeBundleModel.h; sourceTree = "<group>"; };
		B4E02F792B392564000D9856 /* RRAirtimeBundleModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRAirtimeBundleModel.m; sourceTree = "<group>"; };
		B4ED8CC32B26DC7A00D4A3F7 /* RRUserQRCodeVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRUserQRCodeVC.h; sourceTree = "<group>"; };
		B4ED8CC42B26DC7A00D4A3F7 /* RRUserQRCodeVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRUserQRCodeVC.m; sourceTree = "<group>"; };
		B4ED8CC82B26ED0400D4A3F7 /* RRSaveQrCodeView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRSaveQrCodeView.h; sourceTree = "<group>"; };
		B4ED8CC92B26ED0400D4A3F7 /* RRSaveQrCodeView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRSaveQrCodeView.m; sourceTree = "<group>"; };
		B4F12ABB2B34138900896314 /* RRAmtItemView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRAmtItemView.h; sourceTree = "<group>"; };
		B4F12ABC2B34138900896314 /* RRAmtItemView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRAmtItemView.m; sourceTree = "<group>"; };
		B4F12ABE2B341DF600896314 /* RRBundleItemView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBundleItemView.h; sourceTree = "<group>"; };
		B4F12ABF2B341DF600896314 /* RRBundleItemView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBundleItemView.m; sourceTree = "<group>"; };
		B4F12AC12B3422BE00896314 /* RRBundlePlanVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBundlePlanVC.h; sourceTree = "<group>"; };
		B4F12AC22B3422BE00896314 /* RRBundlePlanVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBundlePlanVC.m; sourceTree = "<group>"; };
		B4F12AC42B34230900896314 /* RRBundleListView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBundleListView.h; sourceTree = "<group>"; };
		B4F12AC52B34230900896314 /* RRBundleListView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBundleListView.m; sourceTree = "<group>"; };
		B4F12AC72B34262900896314 /* RRBundleItemCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBundleItemCell.h; sourceTree = "<group>"; };
		B4F12AC82B34262900896314 /* RRBundleItemCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBundleItemCell.m; sourceTree = "<group>"; };
		B4F12ACA2B34350300896314 /* RRBundleAppCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRBundleAppCell.h; sourceTree = "<group>"; };
		B4F12ACB2B34350300896314 /* RRBundleAppCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRBundleAppCell.m; sourceTree = "<group>"; };
		B4F533FE2A08D55C002E1985 /* RRJWMerchantCategoryModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRJWMerchantCategoryModel.h; sourceTree = "<group>"; };
		B4F533FF2A08D55C002E1985 /* RRJWMerchantCategoryModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRJWMerchantCategoryModel.m; sourceTree = "<group>"; };
		B4F534012A08EBC0002E1985 /* RRJWMerchantInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RRJWMerchantInfoModel.h; sourceTree = "<group>"; };
		B4F534022A08EBC0002E1985 /* RRJWMerchantInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RRJWMerchantInfoModel.m; sourceTree = "<group>"; };
		CCB4995D92C61B68DB390649 /* libPods-xWalletPro_iOS.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-xWalletPro_iOS.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		E42680002C46158F003C199D /* pushServiceExtension.entitlements */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.entitlements; path = pushServiceExtension.entitlements; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		8D02288923E7D392008EB3F4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6820F13B98DEA44D6699052C /* libPods-xWalletPro_iOS.a in Frameworks */,
				690A079A2CE00AA8005C1EE4 /* YJCouponCenterSDK.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D0228A123E7D394008EB3F4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D0228AC23E7D394008EB3F4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8DCFC7D925594218000EF0AB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		695AED442C22B6C9005A4D31 /* ResetPIN */ = {
			isa = PBXGroup;
			children = (
				695AED452C22B7B2005A4D31 /* RRResetPINVC.h */,
				695AED462C22B7B2005A4D31 /* RRResetPINVC.m */,
			);
			path = ResetPIN;
			sourceTree = "<group>";
		};
		697047C32B8242FB009543EC /* View */ = {
			isa = PBXGroup;
			children = (
				697047C42B82430C009543EC /* RRPersonalInfoAvactorCell.h */,
				697047C52B82430C009543EC /* RRPersonalInfoAvactorCell.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		6977AC6A2B39135600879110 /* PaymentOrder */ = {
			isa = PBXGroup;
			children = (
				6977AC6C2B39135600879110 /* PaymentOrderVC.h */,
				6977AC6B2B39135600879110 /* PaymentOrderVC.m */,
			);
			path = PaymentOrder;
			sourceTree = "<group>";
		};
		6977AC6E2B392C4F00879110 /* BuyAirtimeBundle */ = {
			isa = PBXGroup;
			children = (
				69E2C77E2B39753300BE6EB2 /* BuyAirtimeBundleVC.h */,
				69E2C77F2B39753300BE6EB2 /* BuyAirtimeBundleVC.m */,
				69E2C7812B39826400BE6EB2 /* BuyAirtimeBundleResultVC.h */,
				69E2C7822B39826400BE6EB2 /* BuyAirtimeBundleResultVC.m */,
			);
			path = BuyAirtimeBundle;
			sourceTree = "<group>";
		};
		6997F4842BA91F35006E4E03 /* ZIPIT */ = {
			isa = PBXGroup;
			children = (
				6997F4852BA91F70006E4E03 /* RRZipitVC.h */,
				6997F4862BA91F70006E4E03 /* RRZipitVC.m */,
			);
			path = ZIPIT;
			sourceTree = "<group>";
		};
		69DDF33C2B63A587006DAE64 /* JuniorAccountManagement */ = {
			isa = PBXGroup;
			children = (
				69DDF33D2B63A5B8006DAE64 /* View */,
				69DDF3392B63A4C2006DAE64 /* RRJuniorAccountVC.h */,
				69DDF33A2B63A4C2006DAE64 /* RRJuniorAccountVC.m */,
				698DCBDB2B679520005D0205 /* RRJuniorAccountDetailVC.h */,
				698DCBDC2B679520005D0205 /* RRJuniorAccountDetailVC.m */,
				698DCBDE2B679E50005D0205 /* RRJuniorAccountChangeVC.h */,
				698DCBDF2B679E50005D0205 /* RRJuniorAccountChangeVC.m */,
				698CCF802B67AEA300FFD9CA /* RRJuniorAccountUpgradeVC.h */,
				698CCF812B67AEA300FFD9CA /* RRJuniorAccountUpgradeVC.m */,
				69366F252B70D43C0065BC73 /* RRCheckJuniorAccountVC.h */,
				69366F262B70D43C0065BC73 /* RRCheckJuniorAccountVC.m */,
				697047BD2B81EC8F009543EC /* RRCheckJuniorAccountDetailsVC.h */,
				697047BE2B81EC8F009543EC /* RRCheckJuniorAccountDetailsVC.m */,
				698CCF832B67E88C00FFD9CA /* RRJuniorUpgradeFResultVC.h */,
				698CCF842B67E88C00FFD9CA /* RRJuniorUpgradeFResultVC.m */,
			);
			path = JuniorAccountManagement;
			sourceTree = "<group>";
		};
		69DDF33D2B63A5B8006DAE64 /* View */ = {
			isa = PBXGroup;
			children = (
				69DDF33E2B63A5D8006DAE64 /* RRJuniorAccountCell.h */,
				69DDF33F2B63A5D8006DAE64 /* RRJuniorAccountCell.m */,
				69366F282B70E3C20065BC73 /* RRCheckJuniorAccountCell.h */,
				69366F292B70E3C20065BC73 /* RRCheckJuniorAccountCell.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		69F7DB3D2B1431E100D908D8 /* StatementEnquiry */ = {
			isa = PBXGroup;
			children = (
				69F7DB3E2B14323200D908D8 /* RRStatementEnquiryVC.h */,
				69F7DB3F2B14323200D908D8 /* RRStatementEnquiryVC.m */,
				69F7DB412B143C8200D908D8 /* RRStatementEnquiryCell.h */,
				69F7DB422B143C8200D908D8 /* RRStatementEnquiryCell.m */,
			);
			path = StatementEnquiry;
			sourceTree = "<group>";
		};
		6DD1CAA05A53F0943F08419F /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				CCB4995D92C61B68DB390649 /* libPods-xWalletPro_iOS.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		8D02288323E7D392008EB3F4 = {
			isa = PBXGroup;
			children = (
				B49C987D273382F8007F2C9E /* md5.json */,
				B49C98742730E4B4007F2C9E /* confuse.sh */,
				B49C98732730E4B4007F2C9E /* func.list */,
				8DBB5BFC23FB8CE000CFEF1A /* mobileImg.bundle */,
				8D0228E323E91AF1008EB3F4 /* PrefixHeader.pch */,
				8D02288E23E7D392008EB3F4 /* xWalletPro_iOS */,
				8D0228A723E7D394008EB3F4 /* xWalletPro_iOSTests */,
				8D0228B223E7D394008EB3F4 /* xWalletPro_iOSUITests */,
				8DCFC7DD25594218000EF0AB /* pushServiceExtension */,
				8D02288D23E7D392008EB3F4 /* Products */,
				E4D0694858A0A77EA23C4077 /* Pods */,
				6DD1CAA05A53F0943F08419F /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		8D02288D23E7D392008EB3F4 /* Products */ = {
			isa = PBXGroup;
			children = (
				8D02288C23E7D392008EB3F4 /* xWalletPro_iOS.app */,
				8D0228A423E7D394008EB3F4 /* xWalletPro_iOSTests.xctest */,
				8D0228AF23E7D394008EB3F4 /* xWalletPro_iOSUITests.xctest */,
				8DCFC7DC25594218000EF0AB /* pushServiceExtension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		8D02288E23E7D392008EB3F4 /* xWalletPro_iOS */ = {
			isa = PBXGroup;
			children = (
				8DC829A52477F55A007EBA45 /* xWalletPro_iOS.entitlements */,
				8D62BC982432E5900078E807 /* Resource */,
				8D0228F923E9B7FA008EB3F4 /* XNLanguage */,
				8D0228DE23E918E0008EB3F4 /* RRTools */,
				8D0228D623E9157E008EB3F4 /* RRNetwork */,
				8D0228E723E96FBA008EB3F4 /* viewControllers */,
				8D0228E223E91AD5008EB3F4 /* RRPublicHeader.h */,
				8D25D4BA24664BE3009F25BA /* configDev.h */,
				8D25D4BB24664BFA009F25BA /* configTest.h */,
				8D25D4BC24664C08009F25BA /* configFat.h */,
				8D25D4BD24664C15009F25BA /* configUat.h */,
				8D25D4C2246653D7009F25BA /* configPro.h */,
				8D02288F23E7D392008EB3F4 /* AppDelegate.h */,
				8D02289023E7D392008EB3F4 /* AppDelegate.m */,
				8D1071F9254067120075A927 /* AppDelegate+JPushService.h */,
				8D1071FA254067230075A927 /* AppDelegate+JPushService.m */,
				8D02289223E7D392008EB3F4 /* ViewController.h */,
				8D02289323E7D392008EB3F4 /* ViewController.m */,
				8D02289523E7D392008EB3F4 /* Main.storyboard */,
				8D02289823E7D394008EB3F4 /* Assets.xcassets */,
				8D02289D23E7D394008EB3F4 /* Info.plist */,
				694BB1FD2CD50D84008989BB /* GoogleService-Info.plist */,
				69CDA0992C1831EC0050440E /* LaunchScreen.storyboard */,
				8D02289E23E7D394008EB3F4 /* main.m */,
				8D0228C923E7DDDD008EB3F4 /* Localizable.strings */,
				8D0228CF23E7E040008EB3F4 /* InfoPlist.strings */,
			);
			path = xWalletPro_iOS;
			sourceTree = "<group>";
		};
		8D0228A723E7D394008EB3F4 /* xWalletPro_iOSTests */ = {
			isa = PBXGroup;
			children = (
				8D0228A823E7D394008EB3F4 /* xWalletPro_iOSTests.m */,
				8D0228AA23E7D394008EB3F4 /* Info.plist */,
			);
			path = xWalletPro_iOSTests;
			sourceTree = "<group>";
		};
		8D0228B223E7D394008EB3F4 /* xWalletPro_iOSUITests */ = {
			isa = PBXGroup;
			children = (
				8D0228B323E7D394008EB3F4 /* xWalletPro_iOSUITests.m */,
				8D0228B523E7D394008EB3F4 /* Info.plist */,
			);
			path = xWalletPro_iOSUITests;
			sourceTree = "<group>";
		};
		8D0228D623E9157E008EB3F4 /* RRNetwork */ = {
			isa = PBXGroup;
			children = (
				8D278C5B244F163900DB8DD9 /* Model */,
				8D0228D923E9178D008EB3F4 /* RRRequestCode.h */,
				8D0228D823E9178D008EB3F4 /* RRHttpRequest.h */,
				8D0228D723E9178D008EB3F4 /* RRHttpRequest.m */,
				8D0228DB23E918B5008EB3F4 /* SecurityUtility.h */,
				8D0228DC23E918B5008EB3F4 /* SecurityUtility.m */,
				8D0228E523E91CF7008EB3F4 /* JSONKit.h */,
				8D0228E423E91CF7008EB3F4 /* JSONKit.m */,
			);
			path = RRNetwork;
			sourceTree = "<group>";
		};
		8D0228DE23E918E0008EB3F4 /* RRTools */ = {
			isa = PBXGroup;
			children = (
				B4E0183A2AEE0DC500531BD0 /* CXDatePickerView */,
				8DCCDCA72546B355008E1793 /* AVPlayer */,
				8D6685D8241C8E5F00AEC5DC /* ExceptionHandler */,
				8DA88D2823FC15610057A82F /* OrderedDictionary.h */,
				8DA88D2923FC15610057A82F /* OrderedDictionary.m */,
				8D0228E023E91901008EB3F4 /* RRTools.h */,
				8D0228DF23E91901008EB3F4 /* RRTools.m */,
			);
			path = RRTools;
			sourceTree = "<group>";
		};
		8D0228E723E96FBA008EB3F4 /* viewControllers */ = {
			isa = PBXGroup;
			children = (
				B4079F9F2AE226E00076460D /* Coupon */,
				8D73D81D24F3E3FF0022ABBD /* Caregory */,
				8D25D4C92466B321009F25BA /* Models */,
				8D946980242284D600F41711 /* Checkout */,
				8D0228EB23E9710B008EB3F4 /* Common */,
				8D0228F823E9B4E6008EB3F4 /* Login */,
				8D0228EF23E97167008EB3F4 /* Home */,
				8D0F93CF240010AD00B04EAC /* Pending */,
				8D3F343B23ED7479009063F8 /* Account */,
			);
			path = viewControllers;
			sourceTree = "<group>";
		};
		8D0228EB23E9710B008EB3F4 /* Common */ = {
			isa = PBXGroup;
			children = (
				8D3F342423EABA73009063F8 /* View */,
				8D0228EC23E97152008EB3F4 /* RRTabbarVC.h */,
				8D0228ED23E97152008EB3F4 /* RRTabbarVC.m */,
				8D7D2FC2241D370700775E59 /* RRNavigationController.h */,
				8D7D2FC3241D370700775E59 /* RRNavigationController.m */,
				8D0228F323E9B492008EB3F4 /* RRNationalBaseVC.h */,
				8D0228F423E9B492008EB3F4 /* RRNationalBaseVC.m */,
				B4DC2B3529BF220C00FF8162 /* RRCommonWebVC.h */,
				B4DC2B3629BF220C00FF8162 /* RRCommonWebVC.m */,
				8D244C302447333D0031595E /* RRFlutterVC.h */,
				8D244C312447333D0031595E /* RRFlutterVC.m */,
				69F1388E2BDAB4D500E056F5 /* CustomAlertAction.h */,
				69F1388F2BDAB4D500E056F5 /* CustomAlertAction.m */,
				690A07942CDFF3B6005C1EE4 /* CustomMJRefreshNormalHeader.h */,
				690A07952CDFF3B6005C1EE4 /* CustomMJRefreshNormalHeader.m */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		8D0228EF23E97167008EB3F4 /* Home */ = {
			isa = PBXGroup;
			children = (
				6997F4842BA91F35006E4E03 /* ZIPIT */,
				B44A90112B441C2A00E8B1A3 /* More */,
				B4D17A162B2E90BE0090D568 /* BuyAirTime */,
				6977AC6E2B392C4F00879110 /* BuyAirtimeBundle */,
				B470C7912B24954D000C47F1 /* Bill */,
				B46C2C772A088C9B0075C196 /* MerchantPropotion */,
				8DCCDCAD2546B7F2008E1793 /* Broadcast */,
				8D6685EA241CEF1E00AEC5DC /* About */,
				8DEE999623F5A49A00C47CB3 /* Contact */,
				8DEE998523F5464C00C47CB3 /* ReceiveQR */,
				8D109D16240C2D86001261C8 /* PaymentQR */,
				8D0F93E42401443C00B04EAC /* Scan */,
				8DEE998C23F56DE800C47CB3 /* Transfer */,
				8D0228F023E9B400008EB3F4 /* RRHomeVC.h */,
				8D0228F123E9B400008EB3F4 /* RRHomeVC.m */,
				8DA3AAC72578D30100B7354C /* RRHomeFunCell.h */,
				8DA3AAC82578D30100B7354C /* RRHomeFunCell.m */,
				8D02290023E9B8E8008EB3F4 /* RRLeftVC.h */,
				8D02290123E9B8E8008EB3F4 /* RRLeftVC.m */,
				8DE5C59E2536D573000712E0 /* RRFloatPictureView.h */,
				8DE5C59F2536D573000712E0 /* RRFloatPictureView.m */,
			);
			path = Home;
			sourceTree = "<group>";
		};
		8D0228F823E9B4E6008EB3F4 /* Login */ = {
			isa = PBXGroup;
			children = (
				695AED442C22B6C9005A4D31 /* ResetPIN */,
				8D3F341E23EAB88A009063F8 /* Register */,
				8D6685DC241CB97400AEC5DC /* Retrieve */,
				8D0228E823E97004008EB3F4 /* RRLoginVC.h */,
				8D0228E923E97004008EB3F4 /* RRLoginVC.m */,
				8D64EF502534043F00D2E08E /* RRTempDeviceLoginVC.h */,
				8D64EF512534043F00D2E08E /* RRTempDeviceLoginVC.m */,
			);
			path = Login;
			sourceTree = "<group>";
		};
		8D0228F923E9B7FA008EB3F4 /* XNLanguage */ = {
			isa = PBXGroup;
			children = (
				8D0228FA23E9B892008EB3F4 /* NSBundle+XNCategory.h */,
				8D0228FD23E9B893008EB3F4 /* NSBundle+XNCategory.m */,
				8D0228FB23E9B893008EB3F4 /* XNLanguageConfig.h */,
				8D0228FC23E9B893008EB3F4 /* XNLanguageConfig.m */,
			);
			path = XNLanguage;
			sourceTree = "<group>";
		};
		8D0F93B723FE4A6900B04EAC /* SplitBill */ = {
			isa = PBXGroup;
			children = (
				8D0F93C823FEC3D400B04EAC /* View */,
				8D0F93BE23FE725E00B04EAC /* RRSplitBillVC.h */,
				8D0F93C123FE725E00B04EAC /* RRSplitBillVC.m */,
				8D0F93C423FE74F400B04EAC /* RRBillContactListVC.h */,
				8D0F93C523FE74F400B04EAC /* RRBillContactListVC.m */,
				8D0F93BF23FE725E00B04EAC /* RRSplitBillResultVC.h */,
				8D0F93C023FE725E00B04EAC /* RRSplitBillResultVC.m */,
				8D0F93CC23FF6D2200B04EAC /* RRSplitIndividVC.h */,
				8D0F93CD23FF6D2200B04EAC /* RRSplitIndividVC.m */,
			);
			path = SplitBill;
			sourceTree = "<group>";
		};
		8D0F93C823FEC3D400B04EAC /* View */ = {
			isa = PBXGroup;
			children = (
				8D0F93C923FEC40000B04EAC /* RRBillPersonAmtView.h */,
				8D0F93CA23FEC40000B04EAC /* RRBillPersonAmtView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		8D0F93CF240010AD00B04EAC /* Pending */ = {
			isa = PBXGroup;
			children = (
				8D0F93D3240011B200B04EAC /* View */,
				8D0F93D0240010D500B04EAC /* RRPendingListVC.h */,
				8D0F93D1240010D500B04EAC /* RRPendingListVC.m */,
				8D0F93DE2400D9C000B04EAC /* RRLssuedDetailsVC.h */,
				8D0F93DF2400D9C000B04EAC /* RRLssuedDetailsVC.m */,
				8D0F93E12400D9DC00B04EAC /* RRReceivedDetailsVC.h */,
				8D0F93E22400D9DC00B04EAC /* RRReceivedDetailsVC.m */,
			);
			path = Pending;
			sourceTree = "<group>";
		};
		8D0F93D3240011B200B04EAC /* View */ = {
			isa = PBXGroup;
			children = (
				8D0F93D4240011CE00B04EAC /* RRPendHeadView.h */,
				8D0F93D5240011CE00B04EAC /* RRPendHeadView.m */,
				8D0F93D824002B7A00B04EAC /* RRLssuedCell.h */,
				8D0F93D924002B7A00B04EAC /* RRLssuedCell.m */,
				8D0F93DB24002B9400B04EAC /* RRReceiveCell.h */,
				8D0F93DC24002B9400B04EAC /* RRReceiveCell.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		8D0F93E42401443C00B04EAC /* Scan */ = {
			isa = PBXGroup;
			children = (
				6977AC6A2B39135600879110 /* PaymentOrder */,
				8D109D1D240CE740001261C8 /* Payment */,
				8D7C04082403D47900FD57A3 /* RRXNStyleDIY.h */,
				8D7C04072403D47900FD57A3 /* RRXNStyleDIY.m */,
				8D7C04042403AD4E00FD57A3 /* RRXNScanVC.h */,
				8D7C04052403AD4E00FD57A3 /* RRXNScanVC.m */,
				8D7C04002403698900FD57A3 /* RRFixedTransferVC.h */,
				8D7C04012403698900FD57A3 /* RRFixedTransferVC.m */,
			);
			path = Scan;
			sourceTree = "<group>";
		};
		8D109D16240C2D86001261C8 /* PaymentQR */ = {
			isa = PBXGroup;
			children = (
				8D109D17240C2DBF001261C8 /* RRPaymentQrCodeVC.h */,
				8D109D18240C2DBF001261C8 /* RRPaymentQrCodeVC.m */,
				6936784B2C0482E900817B5A /* RRMerchantCodeView.h */,
				6936784C2C0482E900817B5A /* RRMerchantCodeView.m */,
			);
			path = PaymentQR;
			sourceTree = "<group>";
		};
		8D109D1D240CE740001261C8 /* Payment */ = {
			isa = PBXGroup;
			children = (
				8D109D1E240CE774001261C8 /* RRPaymentVC.h */,
				8D109D1F240CE774001261C8 /* RRPaymentVC.m */,
				8D109D21240CE789001261C8 /* RRPaymentResultVC.h */,
				8D109D22240CE789001261C8 /* RRPaymentResultVC.m */,
				B4016DAC2B4CFAB90072D554 /* RRConfirmPaymentVC.h */,
				B4016DAD2B4CFAB90072D554 /* RRConfirmPaymentVC.m */,
			);
			path = Payment;
			sourceTree = "<group>";
		};
		8D1329942412686300CA6E11 /* BankCard */ = {
			isa = PBXGroup;
			children = (
				8D13299824126A9700CA6E11 /* View */,
				8D1329952412688700CA6E11 /* RRBankCardListVC.h */,
				8D1329962412688700CA6E11 /* RRBankCardListVC.m */,
				8D13299C2412815600CA6E11 /* RRAddBankCardVC.h */,
				8D13299D2412815600CA6E11 /* RRAddBankCardVC.m */,
				8D1329A02412880500CA6E11 /* RRBankNameVC.h */,
				8D1329A12412880500CA6E11 /* RRBankNameVC.m */,
				8DD4F3182412AF6200FB55EF /* RRBindBankCardResultVC.h */,
				8DD4F3192412AF6200FB55EF /* RRBindBankCardResultVC.m */,
			);
			path = BankCard;
			sourceTree = "<group>";
		};
		8D13299824126A9700CA6E11 /* View */ = {
			isa = PBXGroup;
			children = (
				8D1329A3241289B000CA6E11 /* RRBankNameCell.h */,
				8D1329A4241289B000CA6E11 /* RRBankNameCell.m */,
				8D13299924126AB800CA6E11 /* RRBankCardCell.h */,
				8D13299A24126AB800CA6E11 /* RRBankCardCell.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		8D25D4C92466B321009F25BA /* Models */ = {
			isa = PBXGroup;
			children = (
				8D25D4CE2466C412009F25BA /* RRModels.h */,
				8D25D4CA2466B321009F25BA /* RRUserInfo.h */,
				8D25D4CB2466B321009F25BA /* RRUserInfo.m */,
				8DE674A02468FEAF00E52F70 /* RRContact.h */,
				8DE674A12468FEAF00E52F70 /* RRContact.m */,
				8DE674A3246A8CDB00E52F70 /* RRHistoryModel.h */,
				8DE674A4246A8CDB00E52F70 /* RRHistoryModel.m */,
				8D56D5442484A91D0083608E /* RRHistoryDetailsModel.h */,
				8D56D5452484A91D0083608E /* RRHistoryDetailsModel.m */,
				8DE674A6246AA14400E52F70 /* RRLssuedModel.h */,
				8DE674A7246AA14400E52F70 /* RRLssuedModel.m */,
				8DE674A9246AA18900E52F70 /* RRReceiveModel.h */,
				8DE674AA246AA18900E52F70 /* RRReceiveModel.m */,
				8DE674B3246B9C3400E52F70 /* RRBillPersonAmtModel.h */,
				8DE674B4246B9C3400E52F70 /* RRBillPersonAmtModel.m */,
				8DE674AF246AA38000E52F70 /* RRCardModel.h */,
				8DE674B0246AA38000E52F70 /* RRCardModel.m */,
				8DC829A624791A60007EBA45 /* RRPaymentMethodModel.h */,
				8DC829A724791A60007EBA45 /* RRPaymentMethodModel.m */,
				8D10715C253ED10B0075A927 /* RRDeviceModel.h */,
				8D10715D253ED10B0075A927 /* RRDeviceModel.m */,
				8D64EF622534310300D2E08E /* RRAuthCodeUseListModel.h */,
				8D64EF632534310300D2E08E /* RRAuthCodeUseListModel.m */,
				8DE5C5932536A432000712E0 /* RRPromotionImgModel.h */,
				8DE5C5942536A432000712E0 /* RRPromotionImgModel.m */,
				B4C1604C29A704DF002D5740 /* RRBalanceRecordModel.h */,
				B4C1604D29A704DF002D5740 /* RRBalanceRecordModel.m */,
				B4D17A1B2B2EF71C0090D568 /* RRBillerInfoModel.h */,
				B4D17A1C2B2EF71C0090D568 /* RRBillerInfoModel.m */,
				B4E02F782B392564000D9856 /* RRAirtimeBundleModel.h */,
				B4E02F792B392564000D9856 /* RRAirtimeBundleModel.m */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		8D278C5B244F163900DB8DD9 /* Model */ = {
			isa = PBXGroup;
			children = (
				8D278C5C244F163900DB8DD9 /* RRNetworkModel.h */,
				8D278C5D244F163900DB8DD9 /* RRNetworkModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		8D37C9AC23F0229C00E71E5D /* keyBoard */ = {
			isa = PBXGroup;
			children = (
				8D37C9AE23F0229C00E71E5D /* RRKeyBoardView.h */,
				8D37C9AD23F0229C00E71E5D /* RRKeyBoardView.m */,
			);
			path = keyBoard;
			sourceTree = "<group>";
		};
		8D3F341E23EAB88A009063F8 /* Register */ = {
			isa = PBXGroup;
			children = (
				B4E018362AEA4AAE00531BD0 /* view */,
				B4DC2B3829BF221700FF8162 /* RRTermProtocolVC.h */,
				B4DC2B3929BF221700FF8162 /* RRTermProtocolVC.m */,
				690A07A22CE7414A005C1EE4 /* RRRegisterMobileVC.h */,
				690A07A32CE7414A005C1EE4 /* RRRegisterMobileVC.m */,
				8D3F342523EBC12F009063F8 /* RRRegisterVC.h */,
				8D3F342623EBC12F009063F8 /* RRRegisterVC.m */,
				8D3F343223EC6EFC009063F8 /* RRVerifyCodeVC.h */,
				8D3F343323EC6EFC009063F8 /* RRVerifyCodeVC.m */,
				8D0F93B823FE52F200B04EAC /* RRSetLoginPwdVC.h */,
				8D0F93B923FE52F300B04EAC /* RRSetLoginPwdVC.m */,
				8D3F343523EC797F009063F8 /* RRRegisterResultVC.h */,
				8D3F343623EC797F009063F8 /* RRRegisterResultVC.m */,
				8DB88956250CA922006A5AC3 /* RRFirstSetLoginPwdResultVC.h */,
				8DB88957250CA923006A5AC3 /* RRFirstSetLoginPwdResultVC.m */,
				B4E018332AEA3C5500531BD0 /* RRSelectLanguageVC.h */,
				B4E018342AEA3C5500531BD0 /* RRSelectLanguageVC.m */,
			);
			path = Register;
			sourceTree = "<group>";
		};
		8D3F342423EABA73009063F8 /* View */ = {
			isa = PBXGroup;
			children = (
				8D37C9AC23F0229C00E71E5D /* keyBoard */,
				8DE1DF3523F78C6C002D04CA /* Category */,
				B4BBD29D2B5E3C0C00EAAD97 /* RRXNInputField.h */,
				B4BBD29C2B5E3C0C00EAAD97 /* RRXNInputField.m */,
				8DBB5B8023FB89EC00CFEF1A /* RRHUDView.h */,
				8DBB5B8123FB89ED00CFEF1A /* RRHUDView.m */,
				8D3F342123EABA6D009063F8 /* RRXNTextView.h */,
				8D3F342223EABA6D009063F8 /* RRXNTextView.m */,
				8D37C9A323EFCFC000E71E5D /* RRXNTextField.h */,
				8D37C9A423EFCFC000E71E5D /* RRXNTextField.m */,
				8D9F057F23F1AC97004D0817 /* RRXNLoginInputView.h */,
				8D9F058023F1AC97004D0817 /* RRXNLoginInputView.m */,
				8D37C9A723EFF44000E71E5D /* RRCommonPickerView.h */,
				8D37C9A923EFF44000E71E5D /* RRCommonPickerView.m */,
				695A24762C05684B00109FF5 /* RRCommonInputView.h */,
				695A24772C05684B00109FF5 /* RRCommonInputView.m */,
				695A24822C058CA000109FF5 /* RRCommonSelectView.h */,
				695A24832C058CA000109FF5 /* RRCommonSelectView.m */,
				695A247F2C0575B000109FF5 /* RRCommonAmountView.h */,
				695A24802C0575B000109FF5 /* RRCommonAmountView.m */,
				8D3F342823EBEDF5009063F8 /* RRXNTipLabel.h */,
				8D3F342923EBEDF5009063F8 /* RRXNTipLabel.m */,
				8D3F342F23EC3356009063F8 /* RRXNButton.h */,
				8D3F343023EC3356009063F8 /* RRXNButton.m */,
				8D3F343823ED0286009063F8 /* RRShadowButton.h */,
				8D3F343923ED0287009063F8 /* RRShadowButton.m */,
				8D3F344223ED9CCF009063F8 /* RRXNListBtn.h */,
				8D3F344323ED9CCF009063F8 /* RRXNListBtn.m */,
				8D3F345B23EEC702009063F8 /* RRMobileView.h */,
				8D3F345A23EEC702009063F8 /* RRMobileView.m */,
				8D7D2FC5241D470400775E59 /* RRDatePicker.h */,
				8D7D2FC6241D470400775E59 /* RRDatePicker.m */,
				8DA0BE7724B7178F00EF4D4D /* RRVersionUpdateView.h */,
				8DA0BE7824B7178F00EF4D4D /* RRVersionUpdateView.m */,
				B40FEDB0272BE2ED00640151 /* RRBlurView.h */,
				B40FEDB1272BE2ED00640151 /* RRBlurView.m */,
				B40FEDB3272C020A00640151 /* RRWarningView.h */,
				B40FEDB4272C020A00640151 /* RRWarningView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		8D3F343B23ED7479009063F8 /* Account */ = {
			isa = PBXGroup;
			children = (
				69F7DB3D2B1431E100D908D8 /* StatementEnquiry */,
				B4DB370729A5F226009B7529 /* AccountHistory */,
				8D3F343C23ED7493009063F8 /* RRAccountVC.h */,
				8D3F343D23ED7493009063F8 /* RRAccountVC.m */,
				B4A9AF2C2AA8500D000CF6A1 /* RRCurrencyAccountView.h */,
				B4A9AF2D2AA8500D000CF6A1 /* RRCurrencyAccountView.m */,
				697047C72B82E73B009543EC /* RRJuniorBalanceView.h */,
				697047C82B82E73B009543EC /* RRJuniorBalanceView.m */,
				8DB8894D250B2DA6006A5AC3 /* InviteFriends */,
				8DD4F3262413F53C00FB55EF /* PersonalInfo */,
				8DFCD78C23F41DD700317162 /* TopUp */,
				8DD4F31B2413778600FB55EF /* Withdrawal */,
				8D1329942412686300CA6E11 /* BankCard */,
				8DA88D1D23FBB82A0057A82F /* History */,
				8DFCD78523F3E81100317162 /* SecurityCenter */,
			);
			path = Account;
			sourceTree = "<group>";
		};
		8D62BC982432E5900078E807 /* Resource */ = {
			isa = PBXGroup;
			children = (
				8D66E0F9258394BA004853DD /* loading.json */,
				8D5E74F22486303F00DC1C9E /* phoneCode_ru.plist */,
				8DC42C2024358506003FF9BF /* phoneCode_en.plist */,
				8DC42C22243596DC003FF9BF /* phoneCode_zh.plist */,
			);
			path = Resource;
			sourceTree = "<group>";
		};
		8D64EF5A253430B300D2E08E /* View */ = {
			isa = PBXGroup;
			children = (
				8D64EF5B253430CC00D2E08E /* RRAuthCodeUseListCell.h */,
				8D64EF5C253430CC00D2E08E /* RRAuthCodeUseListCell.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		8D6685D8241C8E5F00AEC5DC /* ExceptionHandler */ = {
			isa = PBXGroup;
			children = (
				8D6685D9241C8E8E00AEC5DC /* XNExceptionHandler.h */,
				8D6685DA241C8E8E00AEC5DC /* XNExceptionHandler.m */,
			);
			path = ExceptionHandler;
			sourceTree = "<group>";
		};
		8D6685DC241CB97400AEC5DC /* Retrieve */ = {
			isa = PBXGroup;
			children = (
				8D6685E0241CB97400AEC5DC /* RRRetrieveUsernameVC.h */,
				8D6685DD241CB97400AEC5DC /* RRRetrieveUsernameVC.m */,
				8D6685DF241CB97400AEC5DC /* RRRetrievePwdVC.h */,
				8D6685E2241CB97400AEC5DC /* RRRetrievePwdVC.m */,
				8D6685E1241CB97400AEC5DC /* RRRetrieveResultVC.h */,
				8D6685DE241CB97400AEC5DC /* RRRetrieveResultVC.m */,
			);
			path = Retrieve;
			sourceTree = "<group>";
		};
		8D6685EA241CEF1E00AEC5DC /* About */ = {
			isa = PBXGroup;
			children = (
				8D6685EB241CEF3400AEC5DC /* RRAboutVC.h */,
				8D6685EC241CEF3400AEC5DC /* RRAboutVC.m */,
				8DF9622F249883600005DB6E /* RRContactUsVC.h */,
				8DF96230249883600005DB6E /* RRContactUsVC.m */,
			);
			path = About;
			sourceTree = "<group>";
		};
		8D73D81D24F3E3FF0022ABBD /* Caregory */ = {
			isa = PBXGroup;
			children = (
				8D73D81E24F3E3FF0022ABBD /* UIView+CornerRadius.h */,
				8D73D81F24F3E3FF0022ABBD /* UIView+CornerRadius.m */,
				6928D2932C25586D001D0802 /* UIDevice+VGAddition.h */,
				6928D2942C25586D001D0802 /* UIDevice+VGAddition.m */,
			);
			path = Caregory;
			sourceTree = "<group>";
		};
		8D94697C2422849500F41711 /* Avactor */ = {
			isa = PBXGroup;
			children = (
				8D94697E2422849500F41711 /* RRPortraitSetVC.h */,
				8D94697D2422849500F41711 /* RRPortraitSetVC.m */,
			);
			path = Avactor;
			sourceTree = "<group>";
		};
		8D946980242284D600F41711 /* Checkout */ = {
			isa = PBXGroup;
			children = (
				8D946983242284D600F41711 /* View */,
				8D946982242284D600F41711 /* RRNativeCheckoutVC.h */,
				8D946986242284D600F41711 /* RRNativeCheckoutVC.m */,
				8D946981242284D600F41711 /* RRNativeCheckoutResultVC.h */,
				8D946987242284D600F41711 /* RRNativeCheckoutResultVC.m */,
			);
			path = Checkout;
			sourceTree = "<group>";
		};
		8D946983242284D600F41711 /* View */ = {
			isa = PBXGroup;
			children = (
				69EF34242B3132DA00ADE484 /* RRPINValidateView.h */,
				69EF34252B3132DA00ADE484 /* RRPINValidateView.m */,
				8D946985242284D600F41711 /* RRCheckoutView.h */,
				8D946984242284D600F41711 /* RRCheckoutView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		8DA88D1D23FBB82A0057A82F /* History */ = {
			isa = PBXGroup;
			children = (
				8DA88D1E23FBB9710057A82F /* RRHistoryListVC.h */,
				8DA88D1F23FBB9710057A82F /* RRHistoryListVC.m */,
				8DA88D2223FBE78A0057A82F /* RRHistoryListCell.h */,
				8DA88D2323FBE78A0057A82F /* RRHistoryListCell.m */,
				8DA88D2523FC028E0057A82F /* RRHistoryListDetailsVC.h */,
				8DA88D2623FC028E0057A82F /* RRHistoryListDetailsVC.m */,
			);
			path = History;
			sourceTree = "<group>";
		};
		8DB8894D250B2DA6006A5AC3 /* InviteFriends */ = {
			isa = PBXGroup;
			children = (
				8DB8894E250B2DE5006A5AC3 /* RRInviteFriendsWebVC.h */,
				8DB8894F250B2DE5006A5AC3 /* RRInviteFriendsWebVC.m */,
			);
			path = InviteFriends;
			sourceTree = "<group>";
		};
		8DB88952250C78CF006A5AC3 /* DeviceManage */ = {
			isa = PBXGroup;
			children = (
				8D64EF5A253430B300D2E08E /* View */,
				8DB88953250C78FF006A5AC3 /* RRDeviceManageVC.h */,
				8DB88954250C78FF006A5AC3 /* RRDeviceManageVC.m */,
				8DB88959250CDB52006A5AC3 /* RRAuthCodeDisplayVC.h */,
				8DB8895A250CDB52006A5AC3 /* RRAuthCodeDisplayVC.m */,
				8D64EF552534307C00D2E08E /* RRAuthCodeUseListVC.h */,
				8D64EF562534307C00D2E08E /* RRAuthCodeUseListVC.m */,
				8D64EF7C25358FA200D2E08E /* RRChangeMasterDeviceResultVC.h */,
				8D64EF7D25358FA200D2E08E /* RRChangeMasterDeviceResultVC.m */,
			);
			path = DeviceManage;
			sourceTree = "<group>";
		};
		8DCCDCA72546B355008E1793 /* AVPlayer */ = {
			isa = PBXGroup;
			children = (
				8DCCDCA92546B36E008E1793 /* RRAVPlayer.h */,
				8DCCDCA82546B36E008E1793 /* RRAVPlayer.m */,
			);
			path = AVPlayer;
			sourceTree = "<group>";
		};
		8DCCDCAD2546B7F2008E1793 /* Broadcast */ = {
			isa = PBXGroup;
			children = (
				8DCCDCAE2546B869008E1793 /* RRBroadcastVC.h */,
				8DCCDCAF2546B869008E1793 /* RRBroadcastVC.m */,
			);
			path = Broadcast;
			sourceTree = "<group>";
		};
		8DCFC7DD25594218000EF0AB /* pushServiceExtension */ = {
			isa = PBXGroup;
			children = (
				E42680002C46158F003C199D /* pushServiceExtension.entitlements */,
				8DCFC7DE25594218000EF0AB /* NotificationService.h */,
				8DCFC7DF25594218000EF0AB /* NotificationService.m */,
				8DCFC7F825595268000EF0AB /* 123.m4a */,
				8DCFC7FE255953ED000EF0AB /* 456.m4a */,
				8DCFC7E125594218000EF0AB /* Info.plist */,
			);
			path = pushServiceExtension;
			sourceTree = "<group>";
		};
		8DD4F31B2413778600FB55EF /* Withdrawal */ = {
			isa = PBXGroup;
			children = (
				8DD4F3222413C37800FB55EF /* View */,
				8DD4F31C241377A600FB55EF /* RRWithdrawalVC.h */,
				8DD4F31D241377A600FB55EF /* RRWithdrawalVC.m */,
				8DD4F31F2413A55E00FB55EF /* RRWithdrawalResultVC.h */,
				8DD4F3202413A55E00FB55EF /* RRWithdrawalResultVC.m */,
			);
			path = Withdrawal;
			sourceTree = "<group>";
		};
		8DD4F3222413C37800FB55EF /* View */ = {
			isa = PBXGroup;
			children = (
				8DD4F3232413C39D00FB55EF /* RRWithdrawalStatusView.h */,
				8DD4F3242413C39D00FB55EF /* RRWithdrawalStatusView.m */,
				B4C3ED802B10966000FC3D53 /* RRWithdrawalAccountPickView.h */,
				B4C3ED812B10966000FC3D53 /* RRWithdrawalAccountPickView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		8DD4F3262413F53C00FB55EF /* PersonalInfo */ = {
			isa = PBXGroup;
			children = (
				697047C32B8242FB009543EC /* View */,
				8D94697C2422849500F41711 /* Avactor */,
				8DD4F3272413F57B00FB55EF /* RRPersonalInfoVC.h */,
				8DD4F3282413F57B00FB55EF /* RRPersonalInfoVC.m */,
				8DD4F32E2414148800FB55EF /* RRModifyNicknameVC.h */,
				8DD4F32F2414148800FB55EF /* RRModifyNicknameVC.m */,
				8DD4F331241414A900FB55EF /* RRModifyEmailVC.h */,
				8DD4F332241414A900FB55EF /* RRModifyEmailVC.m */,
				8DD4F33524141D0100FB55EF /* RRModifyMobileVC.h */,
				8DD4F33624141D0100FB55EF /* RRModifyMobileVC.m */,
				8DD4F3462416788D00FB55EF /* RRModifyMobileResultVC.h */,
				8DD4F3472416788D00FB55EF /* RRModifyMobileResultVC.m */,
				B4ED8CC32B26DC7A00D4A3F7 /* RRUserQRCodeVC.h */,
				B4ED8CC42B26DC7A00D4A3F7 /* RRUserQRCodeVC.m */,
				B4ED8CC82B26ED0400D4A3F7 /* RRSaveQrCodeView.h */,
				B4ED8CC92B26ED0400D4A3F7 /* RRSaveQrCodeView.m */,
			);
			path = PersonalInfo;
			sourceTree = "<group>";
		};
		8DD4F32A2414119A00FB55EF /* LoginLock */ = {
			isa = PBXGroup;
			children = (
				B46813482AB99A22002C3438 /* Lock */,
				B46813412AB94C2C002C3438 /* View */,
				8DD4F32B241411B500FB55EF /* RRLoginLockVC.h */,
				8DD4F32C241411B500FB55EF /* RRLoginLockVC.m */,
				8DD4F3392415608500FB55EF /* RRLoginUnlockVC.h */,
				8DD4F33A2415608500FB55EF /* RRLoginUnlockVC.m */,
				8DD4F33C2415672C00FB55EF /* RRLoginPwdUnlockVC.h */,
				8DD4F33D2415672C00FB55EF /* RRLoginPwdUnlockVC.m */,
				B468135C2AB99A34002C3438 /* RRGestureViewController.h */,
				B468135D2AB99A34002C3438 /* RRGestureViewController.m */,
				B46813452AB98962002C3438 /* RRLoginPwdVerifyVC.h */,
				B46813462AB98962002C3438 /* RRLoginPwdVerifyVC.m */,
			);
			path = LoginLock;
			sourceTree = "<group>";
		};
		8DE1DF3523F78C6C002D04CA /* Category */ = {
			isa = PBXGroup;
			children = (
				8DE1DF3623F79148002D04CA /* UITextField+Category.h */,
				8DE1DF3723F79148002D04CA /* UITextField+Category.m */,
				8D996C3F24109F480020E6EF /* UIButton+Category.h */,
				8D996C4024109F480020E6EF /* UIButton+Category.m */,
			);
			path = Category;
			sourceTree = "<group>";
		};
		8DEE998523F5464C00C47CB3 /* ReceiveQR */ = {
			isa = PBXGroup;
			children = (
				8DEE998623F546B900C47CB3 /* RRReceiveQrCodeVC.h */,
				8DEE998723F546B900C47CB3 /* RRReceiveQrCodeVC.m */,
				8DEE998923F5642800C47CB3 /* RRReceiveQrCodeSetPriceVC.h */,
				8DEE998A23F5642800C47CB3 /* RRReceiveQrCodeSetPriceVC.m */,
			);
			path = ReceiveQR;
			sourceTree = "<group>";
		};
		8DEE998C23F56DE800C47CB3 /* Transfer */ = {
			isa = PBXGroup;
			children = (
				8D0F93B723FE4A6900B04EAC /* SplitBill */,
				8DEE998D23F56E2800C47CB3 /* RRTransferListVC.h */,
				8DEE998E23F56E2800C47CB3 /* RRTransferListVC.m */,
				8DEE999023F58E6300C47CB3 /* RRTransferAccountVC.h */,
				8DEE999123F58E6300C47CB3 /* RRTransferAccountVC.m */,
				8DEE999323F5925700C47CB3 /* RRTransferVC.h */,
				8DEE999423F5925700C47CB3 /* RRTransferVC.m */,
				8DB4A4D423F6EED80095A49F /* RRTransferResultVC.h */,
				8DB4A4D523F6EED90095A49F /* RRTransferResultVC.m */,
				8DD4F3432416572700FB55EF /* RRContactTransferRecordVC.h */,
				8DD4F3442416572700FB55EF /* RRContactTransferRecordVC.m */,
			);
			path = Transfer;
			sourceTree = "<group>";
		};
		8DEE999623F5A49A00C47CB3 /* Contact */ = {
			isa = PBXGroup;
			children = (
				8DEE999723F5A4B300C47CB3 /* RRContactListVC.h */,
				8DEE999823F5A4B300C47CB3 /* RRContactListVC.m */,
				8DEE999A23F5AA9900C47CB3 /* RRContactAddVC.h */,
				8DEE999B23F5AA9900C47CB3 /* RRContactAddVC.m */,
				8DEE999D23F5ABFE00C47CB3 /* RRContactInfVC.h */,
				8DEE999E23F5ABFE00C47CB3 /* RRContactInfVC.m */,
			);
			path = Contact;
			sourceTree = "<group>";
		};
		8DFCD78523F3E81100317162 /* SecurityCenter */ = {
			isa = PBXGroup;
			children = (
				8DB88952250C78CF006A5AC3 /* DeviceManage */,
				8DD4F32A2414119A00FB55EF /* LoginLock */,
				8D3F344823EDC350009063F8 /* RRSecurityCenterVC.h */,
				8D3F344923EDC350009063F8 /* RRSecurityCenterVC.m */,
				6990B0802B2FDBDF007D917B /* RRPaymentSettingsVC.h */,
				6990B0812B2FDBDF007D917B /* RRPaymentSettingsVC.m */,
				6990B0832B2FEB4B007D917B /* RRNoPINPaymentVC.h */,
				6990B0842B2FEB4B007D917B /* RRNoPINPaymentVC.m */,
				6977AC6F2B392F1C00879110 /* RRAutomaticDebitVC.h */,
				6977AC702B392F1C00879110 /* RRAutomaticDebitVC.m */,
				6997F4882BA9CE0D006E4E03 /* RRPaymentAccountSequenceVC.h */,
				6997F4892BA9CE0D006E4E03 /* RRPaymentAccountSequenceVC.m */,
				6997F48B2BA9D677006E4E03 /* RRAccountSequenceCell.h */,
				6997F48C2BA9D677006E4E03 /* RRAccountSequenceCell.m */,
				69E2C77B2B396F2000BE6EB2 /* RRAutomaticDebitDetailVC.h */,
				69E2C77C2B396F2000BE6EB2 /* RRAutomaticDebitDetailVC.m */,
				6977AC722B39631100879110 /* RRAutomaticDebitCell.h */,
				6977AC732B39631100879110 /* RRAutomaticDebitCell.m */,
				6990B0862B302DE6007D917B /* RRNoPINPaymentMangeVC.h */,
				6990B0872B302DE6007D917B /* RRNoPINPaymentMangeVC.m */,
				69D75DBD2B31A10F00C76AE6 /* RRNoPINPaymentResultVC.h */,
				69D75DBE2B31A10F00C76AE6 /* RRNoPINPaymentResultVC.m */,
				8D3F344B23EE8EA9009063F8 /* RRPwdManageVC.h */,
				8D3F344C23EE8EA9009063F8 /* RRPwdManageVC.m */,
				8D3F344E23EE94D6009063F8 /* RREditLoginPwdVC.h */,
				8D3F344F23EE94D6009063F8 /* RREditLoginPwdVC.m */,
				8D3F345723EEBC70009063F8 /* RREditLoginPwdResultVC.h */,
				8D3F345823EEBC70009063F8 /* RREditLoginPwdResultVC.m */,
				69F7F7292B70869B003BB98E /* RRSecretWordsVC.h */,
				69F7F72A2B70869B003BB98E /* RRSecretWordsVC.m */,
				8D3F345D23EECA51009063F8 /* RRAuthPayPwdVC.h */,
				8D3F345E23EECA51009063F8 /* RRAuthPayPwdVC.m */,
				8D3F345123EE94F1009063F8 /* RREditPayPwdVC.h */,
				8D3F345223EE94F1009063F8 /* RREditPayPwdVC.m */,
				8D3F346023EEE5A2009063F8 /* RREditPayPwdAgainVC.h */,
				8D3F346123EEE5A3009063F8 /* RREditPayPwdAgainVC.m */,
				8D3F346323EEE791009063F8 /* RREditPayPwdResultVC.h */,
				8D3F346423EEE791009063F8 /* RREditPayPwdResultVC.m */,
				8D3F345423EE9529009063F8 /* RRRetrievePayPwdVC.h */,
				8D3F345523EE9529009063F8 /* RRRetrievePayPwdVC.m */,
			);
			path = SecurityCenter;
			sourceTree = "<group>";
		};
		8DFCD78C23F41DD700317162 /* TopUp */ = {
			isa = PBXGroup;
			children = (
				8D6CA50F24B0287B00BCF643 /* RRTopUpVC.h */,
				8D6CA51024B0287B00BCF643 /* RRTopUpVC.m */,
				8D6CA51224B047DF00BCF643 /* RRTopUpResultVC.h */,
				8D6CA51324B047DF00BCF643 /* RRTopUpResultVC.m */,
			);
			path = TopUp;
			sourceTree = "<group>";
		};
		B4079F9F2AE226E00076460D /* Coupon */ = {
			isa = PBXGroup;
			children = (
				690A079D2CE36DF9005C1EE4 /* resource.bundle */,
				690A07982CE00AA8005C1EE4 /* YJCouponCenterSDK.framework */,
				B4079FA42AE229970076460D /* CouponSDK.h */,
				B4079FA52AE229970076460D /* CouponSDK.m */,
			);
			path = Coupon;
			sourceTree = "<group>";
		};
		B44A90112B441C2A00E8B1A3 /* More */ = {
			isa = PBXGroup;
			children = (
				B44A90122B441C3400E8B1A3 /* view */,
				B44A90132B441C7200E8B1A3 /* RRMoreFunctionVC.h */,
				B44A90142B441C7200E8B1A3 /* RRMoreFunctionVC.m */,
				690A079F2CE4B624005C1EE4 /* RRCashOutVC.h */,
				690A07A02CE4B624005C1EE4 /* RRCashOutVC.m */,
				690A07A52CEB3A10005C1EE4 /* RCashOutResultVC.h */,
				690A07A62CEB3A10005C1EE4 /* RCashOutResultVC.m */,
			);
			path = More;
			sourceTree = "<group>";
		};
		B44A90122B441C3400E8B1A3 /* view */ = {
			isa = PBXGroup;
			children = (
				B44A90162B441D5700E8B1A3 /* RRMoreFuctionCell.h */,
				B44A90172B441D5700E8B1A3 /* RRMoreFuctionCell.m */,
			);
			path = view;
			sourceTree = "<group>";
		};
		B46813412AB94C2C002C3438 /* View */ = {
			isa = PBXGroup;
			children = (
				B46813422AB94C8F002C3438 /* RRLoginLockView.h */,
				B46813432AB94C8F002C3438 /* RRLoginLockView.m */,
				B468135F2AB9BF48002C3438 /* RRGestureLockView.h */,
				B46813602AB9BF48002C3438 /* RRGestureLockView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		B46813482AB99A22002C3438 /* Lock */ = {
			isa = PBXGroup;
			children = (
				B46813552AB99A22002C3438 /* AliPayItem.h */,
				B468134F2AB99A22002C3438 /* AliPayItem.m */,
				B468134C2AB99A22002C3438 /* Header.h */,
			);
			path = Lock;
			sourceTree = "<group>";
		};
		B46C2C772A088C9B0075C196 /* MerchantPropotion */ = {
			isa = PBXGroup;
			children = (
				69DDF33C2B63A587006DAE64 /* JuniorAccountManagement */,
				B46C2C782A088C9B0075C196 /* JWCategory */,
				B46C2C7B2A088C9B0075C196 /* Controller */,
				B46C2C802A088C9B0075C196 /* Model */,
				B46C2C832A088C9B0075C196 /* View */,
			);
			path = MerchantPropotion;
			sourceTree = "<group>";
		};
		B46C2C782A088C9B0075C196 /* JWCategory */ = {
			isa = PBXGroup;
			children = (
				B46C2C792A088C9B0075C196 /* UIView+JWExtension.h */,
				B46C2C7A2A088C9B0075C196 /* UIView+JWExtension.m */,
			);
			path = JWCategory;
			sourceTree = "<group>";
		};
		B46C2C7B2A088C9B0075C196 /* Controller */ = {
			isa = PBXGroup;
			children = (
				B46C2C7E2A088C9B0075C196 /* RRJWMerchantInfomationVC.h */,
				B46C2C7C2A088C9B0075C196 /* RRJWMerchantInfomationVC.m */,
				B46C2C7F2A088C9B0075C196 /* RRJWMerchantListVC.h */,
				B46C2C7D2A088C9B0075C196 /* RRJWMerchantListVC.m */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		B46C2C802A088C9B0075C196 /* Model */ = {
			isa = PBXGroup;
			children = (
				B46C2C812A088C9B0075C196 /* RRJWMerchantModel.m */,
				B46C2C822A088C9B0075C196 /* RRJWMerchantModel.h */,
				B4F533FE2A08D55C002E1985 /* RRJWMerchantCategoryModel.h */,
				B4F533FF2A08D55C002E1985 /* RRJWMerchantCategoryModel.m */,
				B4F534012A08EBC0002E1985 /* RRJWMerchantInfoModel.h */,
				B4F534022A08EBC0002E1985 /* RRJWMerchantInfoModel.m */,
				B44C86FC2A09E3BE00FA25DB /* RRJWMerchantDetailsModel.h */,
				B44C86FD2A09E3BE00FA25DB /* RRJWMerchantDetailsModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		B46C2C832A088C9B0075C196 /* View */ = {
			isa = PBXGroup;
			children = (
				B46C2C842A088C9B0075C196 /* RRJWShopInfoView.h */,
				B46C2C8C2A088C9B0075C196 /* RRJWShopInfoView.m */,
				B46C2C8B2A088C9B0075C196 /* RRJWMerchantInfomationAddressCell.h */,
				B46C2C852A088C9B0075C196 /* RRJWMerchantInfomationAddressCell.m */,
				B46C2C8E2A088C9B0075C196 /* RRJWMerchantInfomationHeaderView.h */,
				B46C2C862A088C9B0075C196 /* RRJWMerchantInfomationHeaderView.m */,
				B46C2C8D2A088C9B0075C196 /* RRJWMerchantListFiltrateView.h */,
				B46C2C872A088C9B0075C196 /* RRJWMerchantListFiltrateView.m */,
				B46C2C912A088C9B0075C196 /* RRJWBaseView.h */,
				B46C2C882A088C9B0075C196 /* RRJWBaseView.m */,
				B46C2C902A088C9B0075C196 /* RRJWMerchantInfomationCell.h */,
				B46C2C892A088C9B0075C196 /* RRJWMerchantInfomationCell.m */,
				B46C2C8F2A088C9B0075C196 /* RRJWMerchantListCell.h */,
				B46C2C8A2A088C9B0075C196 /* RRJWMerchantListCell.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		B470C7912B24954D000C47F1 /* Bill */ = {
			isa = PBXGroup;
			children = (
				B470C7952B2496A5000C47F1 /* View */,
				B470C7922B24957C000C47F1 /* RRBillManagerVC.h */,
				B470C7932B24957C000C47F1 /* RRBillManagerVC.m */,
				B470C7992B249A29000C47F1 /* RRBillInfoInputVC.h */,
				B470C79A2B249A29000C47F1 /* RRBillInfoInputVC.m */,
				B470C79C2B24A05B000C47F1 /* RRBillAmtInputVC.h */,
				B470C79D2B24A05B000C47F1 /* RRBillAmtInputVC.m */,
				695A247C2C056DDD00109FF5 /* RRBillerCodeInputVC.h */,
				695A247D2C056DDD00109FF5 /* RRBillerCodeInputVC.m */,
				B470C79F2B24AC80000C47F1 /* RRBillPaymentResultVC.h */,
				B470C7A02B24AC80000C47F1 /* RRBillPaymentResultVC.m */,
				B470C7A22B24ADD1000C47F1 /* RRBIllPaymentRecordsVC.h */,
				B470C7A32B24ADD1000C47F1 /* RRBIllPaymentRecordsVC.m */,
			);
			path = Bill;
			sourceTree = "<group>";
		};
		B470C7952B2496A5000C47F1 /* View */ = {
			isa = PBXGroup;
			children = (
				B470C7962B2496E2000C47F1 /* RRBIillTypeCell.h */,
				B470C7972B2496E2000C47F1 /* RRBIillTypeCell.m */,
				B470C7A52B24ADFB000C47F1 /* RRBillPaymentRecordCell.h */,
				B470C7A62B24ADFB000C47F1 /* RRBillPaymentRecordCell.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		B4D17A162B2E90BE0090D568 /* BuyAirTime */ = {
			isa = PBXGroup;
			children = (
				B4D17A172B2E90DF0090D568 /* View */,
				B4D17A182B2E91700090D568 /* RRAirTimeBundleTopVC.h */,
				B4D17A192B2E91700090D568 /* RRAirTimeBundleTopVC.m */,
				B4F12AC12B3422BE00896314 /* RRBundlePlanVC.h */,
				B4F12AC22B3422BE00896314 /* RRBundlePlanVC.m */,
				B421BE782B3588DF00488ACF /* RRAirtimeBundleResultVC.h */,
				B421BE792B3588DF00488ACF /* RRAirtimeBundleResultVC.m */,
			);
			path = BuyAirTime;
			sourceTree = "<group>";
		};
		B4D17A172B2E90DF0090D568 /* View */ = {
			isa = PBXGroup;
			children = (
				B453B0C02B3292B7008D787C /* RRMobileTopView.h */,
				B453B0C12B3292B7008D787C /* RRMobileTopView.m */,
				B453B0C32B3292D1008D787C /* RRBundleTopView.h */,
				B453B0C42B3292D1008D787C /* RRBundleTopView.m */,
				B4F12ABB2B34138900896314 /* RRAmtItemView.h */,
				B4F12ABC2B34138900896314 /* RRAmtItemView.m */,
				B4F12ABE2B341DF600896314 /* RRBundleItemView.h */,
				B4F12ABF2B341DF600896314 /* RRBundleItemView.m */,
				B4F12AC42B34230900896314 /* RRBundleListView.h */,
				B4F12AC52B34230900896314 /* RRBundleListView.m */,
				B4F12AC72B34262900896314 /* RRBundleItemCell.h */,
				B4F12AC82B34262900896314 /* RRBundleItemCell.m */,
				B4F12ACA2B34350300896314 /* RRBundleAppCell.h */,
				B4F12ACB2B34350300896314 /* RRBundleAppCell.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		B4DB370729A5F226009B7529 /* AccountHistory */ = {
			isa = PBXGroup;
			children = (
				B4DFE1332AA99A97007BA3AC /* HMSegmentedControl.h */,
				B4DFE1342AA99A98007BA3AC /* HMSegmentedControl.m */,
				B4DB370829A5F2B3009B7529 /* RRAccountHistoryVC.h */,
				B4DB370929A5F2B3009B7529 /* RRAccountHistoryVC.m */,
				B4DB370B29A5F5B2009B7529 /* RRAccountHistoryListCell.h */,
				B4DB370C29A5F5B2009B7529 /* RRAccountHistoryListCell.m */,
				B4DB370E29A5FD25009B7529 /* RRBalanceView.h */,
				B4DB370F29A5FD25009B7529 /* RRBalanceView.m */,
				B4C1605229A70BBB002D5740 /* RRAccountRecordDetailsVC.h */,
				B4C1605329A70BBB002D5740 /* RRAccountRecordDetailsVC.m */,
				B4DFE1362AA9A101007BA3AC /* RRActiveAccountVC.h */,
				B4DFE1372AA9A101007BA3AC /* RRActiveAccountVC.m */,
			);
			path = AccountHistory;
			sourceTree = "<group>";
		};
		B4E018362AEA4AAE00531BD0 /* view */ = {
			isa = PBXGroup;
			children = (
				B4E018372AEA4ADE00531BD0 /* RRRegisterProcessView.h */,
				B4E018382AEA4ADE00531BD0 /* RRRegisterProcessView.m */,
			);
			path = view;
			sourceTree = "<group>";
		};
		B4E0183A2AEE0DC500531BD0 /* CXDatePickerView */ = {
			isa = PBXGroup;
			children = (
				B4E0183B2AEE0DC500531BD0 /* CXDatePickerView.m */,
				B4E0183C2AEE0DC500531BD0 /* CXDatePickerConfig.h */,
				B4E0183D2AEE0DC500531BD0 /* NSDate+CXCategory.h */,
				B4E0183E2AEE0DC500531BD0 /* CXDatePickerViewManager.h */,
				B4E0183F2AEE0DC500531BD0 /* CXDatePickerStyle.h */,
				B4E018402AEE0DC500531BD0 /* CXDatePickerView.h */,
				B4E018412AEE0DC500531BD0 /* NSDate+CXCategory.m */,
				B4E018422AEE0DC500531BD0 /* CXDatePickerViewManager.m */,
			);
			path = CXDatePickerView;
			sourceTree = "<group>";
		};
		E4D0694858A0A77EA23C4077 /* Pods */ = {
			isa = PBXGroup;
			children = (
				72D3AD5752D61CC3355A87F8 /* Pods-xWalletPro_iOS.debug.xcconfig */,
				57466F3357655E842D2ABDF8 /* Pods-xWalletPro_iOS.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		8D02288B23E7D392008EB3F4 /* xWalletPro_iOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8D0228B823E7D394008EB3F4 /* Build configuration list for PBXNativeTarget "xWalletPro_iOS" */;
			buildPhases = (
				C2F6124C0A927BF630F518F6 /* [CP] Check Pods Manifest.lock */,
				7D01EC51E2F31F5D765AFF8B /* [CP-User] Run Flutter Build xwalletpro_flutter Script */,
				8D02288823E7D392008EB3F4 /* Sources */,
				8D02288923E7D392008EB3F4 /* Frameworks */,
				8D02288A23E7D392008EB3F4 /* Resources */,
				8D0228D223E812EB008EB3F4 /* ShellScript */,
				8DCCDCC52546DC16008E1793 /* Embed App Extensions */,
				053308593255DD7AC47C01DF /* [CP] Embed Pods Frameworks */,
				B40286B329D58C0400BD1C33 /* ShellScript */,
				92D5CE80508B8BC46A4AD65C /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8DCFC7E325594218000EF0AB /* PBXTargetDependency */,
			);
			name = xWalletPro_iOS;
			productName = xWalletPro_iOS;
			productReference = 8D02288C23E7D392008EB3F4 /* xWalletPro_iOS.app */;
			productType = "com.apple.product-type.application";
		};
		8D0228A323E7D394008EB3F4 /* xWalletPro_iOSTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8D0228BB23E7D394008EB3F4 /* Build configuration list for PBXNativeTarget "xWalletPro_iOSTests" */;
			buildPhases = (
				8D0228A023E7D394008EB3F4 /* Sources */,
				8D0228A123E7D394008EB3F4 /* Frameworks */,
				8D0228A223E7D394008EB3F4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8D0228A623E7D394008EB3F4 /* PBXTargetDependency */,
			);
			name = xWalletPro_iOSTests;
			productName = xWalletPro_iOSTests;
			productReference = 8D0228A423E7D394008EB3F4 /* xWalletPro_iOSTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		8D0228AE23E7D394008EB3F4 /* xWalletPro_iOSUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8D0228BE23E7D394008EB3F4 /* Build configuration list for PBXNativeTarget "xWalletPro_iOSUITests" */;
			buildPhases = (
				8D0228AB23E7D394008EB3F4 /* Sources */,
				8D0228AC23E7D394008EB3F4 /* Frameworks */,
				8D0228AD23E7D394008EB3F4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8D0228B123E7D394008EB3F4 /* PBXTargetDependency */,
			);
			name = xWalletPro_iOSUITests;
			productName = xWalletPro_iOSUITests;
			productReference = 8D0228AF23E7D394008EB3F4 /* xWalletPro_iOSUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
		8DCFC7DB25594218000EF0AB /* pushServiceExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8DCFC7E725594218000EF0AB /* Build configuration list for PBXNativeTarget "pushServiceExtension" */;
			buildPhases = (
				8DCFC7D825594218000EF0AB /* Sources */,
				8DCFC7D925594218000EF0AB /* Frameworks */,
				8DCFC7DA25594218000EF0AB /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = pushServiceExtension;
			productName = pushServiceExtension;
			productReference = 8DCFC7DC25594218000EF0AB /* pushServiceExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		8D02288423E7D392008EB3F4 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1030;
				ORGANIZATIONNAME = Shirley;
				TargetAttributes = {
					8D02288B23E7D392008EB3F4 = {
						CreatedOnToolsVersion = 10.3;
					};
					8D0228A323E7D394008EB3F4 = {
						CreatedOnToolsVersion = 10.3;
						TestTargetID = 8D02288B23E7D392008EB3F4;
					};
					8D0228AE23E7D394008EB3F4 = {
						CreatedOnToolsVersion = 10.3;
						TestTargetID = 8D02288B23E7D392008EB3F4;
					};
					8DCFC7DB25594218000EF0AB = {
						CreatedOnToolsVersion = 12.0;
					};
				};
			};
			buildConfigurationList = 8D02288723E7D392008EB3F4 /* Build configuration list for PBXProject "xWalletPro_iOS" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				sn,
				nr,
			);
			mainGroup = 8D02288323E7D392008EB3F4;
			productRefGroup = 8D02288D23E7D392008EB3F4 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8D02288B23E7D392008EB3F4 /* xWalletPro_iOS */,
				8D0228A323E7D394008EB3F4 /* xWalletPro_iOSTests */,
				8D0228AE23E7D394008EB3F4 /* xWalletPro_iOSUITests */,
				8DCFC7DB25594218000EF0AB /* pushServiceExtension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8D02288A23E7D392008EB3F4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8D0228C723E7DDDD008EB3F4 /* Localizable.strings in Resources */,
				8DC42C23243596DC003FF9BF /* phoneCode_zh.plist in Resources */,
				8DC42C2124358506003FF9BF /* phoneCode_en.plist in Resources */,
				8D5E74F32486303F00DC1C9E /* phoneCode_ru.plist in Resources */,
				69CDA09B2C1831ED0050440E /* LaunchScreen.storyboard in Resources */,
				8D66E0FA258394BA004853DD /* loading.json in Resources */,
				8DCFC7FF255953ED000EF0AB /* 456.m4a in Resources */,
				B49C987E273382F8007F2C9E /* md5.json in Resources */,
				694BB1FE2CD50D84008989BB /* GoogleService-Info.plist in Resources */,
				8D02289723E7D392008EB3F4 /* Main.storyboard in Resources */,
				8DCFC7F925595268000EF0AB /* 123.m4a in Resources */,
				8DBB5BFD23FB8CE000CFEF1A /* mobileImg.bundle in Resources */,
				8D0228CD23E7E040008EB3F4 /* InfoPlist.strings in Resources */,
				69FA3E232C18455C005C9650 /* Assets.xcassets in Resources */,
				690A079E2CE36DF9005C1EE4 /* resource.bundle in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D0228A223E7D394008EB3F4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D0228AD23E7D394008EB3F4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8DCFC7DA25594218000EF0AB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8DCFC800255953ED000EF0AB /* 456.m4a in Resources */,
				8DCFC7FA25595268000EF0AB /* 123.m4a in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		053308593255DD7AC47C01DF /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-xWalletPro_iOS/Pods-xWalletPro_iOS-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-xWalletPro_iOS/Pods-xWalletPro_iOS-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-xWalletPro_iOS/Pods-xWalletPro_iOS-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		7D01EC51E2F31F5D765AFF8B /* [CP-User] Run Flutter Build xwalletpro_flutter Script */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			name = "[CP-User] Run Flutter Build xwalletpro_flutter Script";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\nset -u\nsource \"${SRCROOT}/../../xwalletpro_flutter/.ios/Flutter/flutter_export_environment.sh\"\nexport VERBOSE_SCRIPT_LOGGING=1 && \"$FLUTTER_ROOT\"/packages/flutter_tools/bin/xcode_backend.sh build";
		};
		8D0228D223E812EB008EB3F4 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 12;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#\"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" build\n#\"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" embed\nKEYWORDS=\"TODO:|FIXME:|DevTeam:|XXX:\"find \"${SRCROOT}\" \\( -name \"*.h\" -or -name \"*.m\" \\) -print0 | xargs -0 egrep --with-filename --line-number --only-matching \"($KEYWORDS).*\\$\" | perl -p -e \"s/($KEYWORDS)/ warning: \\$1/\"\n";
		};
		92D5CE80508B8BC46A4AD65C /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-xWalletPro_iOS/Pods-xWalletPro_iOS-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-xWalletPro_iOS/Pods-xWalletPro_iOS-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-xWalletPro_iOS/Pods-xWalletPro_iOS-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		B40286B329D58C0400BD1C33 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Type a script or drag a script file from your workspace to insert its path.\n# Type a script or drag a script file from your workspace to insert its path.\ninstall_framework()\n{\n  if [ -r \"${BUILT_PRODUCTS_DIR}/$1\" ]; then\n    local source=\"${BUILT_PRODUCTS_DIR}/$1\"\n  elif [ -r \"${BUILT_PRODUCTS_DIR}/$(basename \"$1\")\" ]; then\n    local source=\"${BUILT_PRODUCTS_DIR}/$(basename \"$1\")\"\n  elif [ -r \"$1\" ]; then\n    local source=\"$1\"\n  fi\n\n  local destination=\"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}\"\n\n  if [ -L \"${source}\" ]; then\n    echo \"Symlinked...\"\n    source=\"$(readlink \"${source}\")\"\n  fi\n\n  if [ -d \"${source}/${BCSYMBOLMAP_DIR}\" ]; then\n    # Locate and install any .bcsymbolmaps if present, and remove them from the .framework before the framework is copied\n    find \"${source}/${BCSYMBOLMAP_DIR}\" -name \"*.bcsymbolmap\"|while read f; do\n      echo \"Installing $f\"\n      install_bcsymbolmap \"$f\" \"$destination\"\n      rm \"$f\"\n    done\n    rmdir \"${source}/${BCSYMBOLMAP_DIR}\"\n  fi\n\n  # Use filter instead of exclude so missing patterns don't throw errors.\n  echo \"rsync --delete -av \"${RSYNC_PROTECT_TMP_FILES[@]}\" --links --filter \\\"- CVS/\\\" --filter \\\"- .svn/\\\" --filter \\\"- .git/\\\" --filter \\\"- .hg/\\\" --filter \\\"- Headers\\\" --filter \\\"- PrivateHeaders\\\" --filter \\\"- Modules\\\" \\\"${source}\\\" \\\"${destination}\\\"\"\n  rsync --delete -av \"${RSYNC_PROTECT_TMP_FILES[@]}\" --links --filter \"- CVS/\" --filter \"- .svn/\" --filter \"- .git/\" --filter \"- .hg/\" --filter \"- Headers\" --filter \"- PrivateHeaders\" --filter \"- Modules\" \"${source}\" \"${destination}\"\n\n  local basename\n  basename=\"$(basename -s .framework \"$1\")\"\n  binary=\"${destination}/${basename}.framework/${basename}\"\n\n  if ! [ -r \"$binary\" ]; then\n    binary=\"${destination}/${basename}\"\n  elif [ -L \"${binary}\" ]; then\n    echo \"Destination binary is symlinked...\"\n    dirname=\"$(dirname \"${binary}\")\"\n    binary=\"${dirname}/$(readlink \"${binary}\")\"\n  fi\n\n  # Strip invalid architectures so \"fat\" simulator / device frameworks work on device\n  if [[ \"$(file \"$binary\")\" == *\"dynamically linked shared library\"* ]]; then\n    strip_invalid_archs \"$binary\"\n  fi\n\n  # Resign the code if required by the build settings to avoid unstable apps\n  code_sign_if_enabled \"${destination}/$(basename \"$1\")\"\n\n  # Embed linked Swift runtime libraries. No longer necessary as of Xcode 7.\n  if [ \"${XCODE_VERSION_MAJOR}\" -lt 7 ]; then\n    local swift_runtime_libs\n    swift_runtime_libs=$(xcrun otool -LX \"$binary\" | grep --color=never @rpath/libswift | sed -E s/@rpath\\\\/\\(.+dylib\\).*/\\\\1/g | uniq -u)\n    for lib in $swift_runtime_libs; do\n      echo \"rsync -auv \\\"${SWIFT_STDLIB_PATH}/${lib}\\\" \\\"${destination}\\\"\"\n      rsync -auv \"${SWIFT_STDLIB_PATH}/${lib}\" \"${destination}\"\n      code_sign_if_enabled \"${destination}/${lib}\"\n    done\n  fi\n}\n\ninstall_framework \"../../xwalletpro_flutter/.ios/Flutter/App.framework\"\n\n";
		};
		C2F6124C0A927BF630F518F6 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-xWalletPro_iOS-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		8D02288823E7D392008EB3F4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8D3F345C23EEC703009063F8 /* RRMobileView.m in Sources */,
				8D946989242284D600F41711 /* RRNativeCheckoutVC.m in Sources */,
				8D3F345023EE94D6009063F8 /* RREditLoginPwdVC.m in Sources */,
				8D3F345623EE9529009063F8 /* RRRetrievePayPwdVC.m in Sources */,
				8D0228EE23E97152008EB3F4 /* RRTabbarVC.m in Sources */,
				8D3F346223EEE5A3009063F8 /* RREditPayPwdAgainVC.m in Sources */,
				B4F12AC62B34230900896314 /* RRBundleListView.m in Sources */,
				8DD4F32D241411B500FB55EF /* RRLoginLockVC.m in Sources */,
				8D64EF572534307C00D2E08E /* RRAuthCodeUseListVC.m in Sources */,
				8D0F93E02400D9C000B04EAC /* RRLssuedDetailsVC.m in Sources */,
				6990B0882B302DE6007D917B /* RRNoPINPaymentMangeVC.m in Sources */,
				B46C2C922A088C9B0075C196 /* UIView+JWExtension.m in Sources */,
				8DB88950250B2DE5006A5AC3 /* RRInviteFriendsWebVC.m in Sources */,
				B4E018432AEE0DC500531BD0 /* CXDatePickerView.m in Sources */,
				B4DFE1352AA99A98007BA3AC /* HMSegmentedControl.m in Sources */,
				8D6CA51124B0287B00BCF643 /* RRTopUpVC.m in Sources */,
				8D3F344A23EDC350009063F8 /* RRSecurityCenterVC.m in Sources */,
				8D3F343123EC3357009063F8 /* RRXNButton.m in Sources */,
				69D75DBF2B31A11000C76AE6 /* RRNoPINPaymentResultVC.m in Sources */,
				B4F12ABD2B34138900896314 /* RRAmtItemView.m in Sources */,
				8D3F342723EBC12F009063F8 /* RRRegisterVC.m in Sources */,
				8DB4A4D623F6EED90095A49F /* RRTransferResultVC.m in Sources */,
				6990B0822B2FDBDF007D917B /* RRPaymentSettingsVC.m in Sources */,
				8D10715E253ED10B0075A927 /* RRDeviceModel.m in Sources */,
				695AED472C22B7B2005A4D31 /* RRResetPINVC.m in Sources */,
				8D0F93DA24002B7A00B04EAC /* RRLssuedCell.m in Sources */,
				B4BBD29E2B5E3C0C00EAAD97 /* RRXNInputField.m in Sources */,
				8D3F345323EE94F1009063F8 /* RREditPayPwdVC.m in Sources */,
				B4DC2B3729BF220C00FF8162 /* RRCommonWebVC.m in Sources */,
				B46C2C972A088C9B0075C196 /* RRJWMerchantInfomationHeaderView.m in Sources */,
				8D278C5E244F163900DB8DD9 /* RRNetworkModel.m in Sources */,
				8D0F93D6240011CE00B04EAC /* RRPendHeadView.m in Sources */,
				B44C86FE2A09E3BE00FA25DB /* RRJWMerchantDetailsModel.m in Sources */,
				8D0F93D2240010D500B04EAC /* RRPendingListVC.m in Sources */,
				8D0F93C323FE725E00B04EAC /* RRSplitBillVC.m in Sources */,
				8D0F93CE23FF6D2200B04EAC /* RRSplitIndividVC.m in Sources */,
				69366F2A2B70E3C20065BC73 /* RRCheckJuniorAccountCell.m in Sources */,
				B4C1605429A70BBB002D5740 /* RRAccountRecordDetailsVC.m in Sources */,
				B44A90152B441C7200E8B1A3 /* RRMoreFunctionVC.m in Sources */,
				8DB88958250CA923006A5AC3 /* RRFirstSetLoginPwdResultVC.m in Sources */,
				B470C79E2B24A05B000C47F1 /* RRBillAmtInputVC.m in Sources */,
				B4F12AC32B3422BE00896314 /* RRBundlePlanVC.m in Sources */,
				8D0F93C623FE74F400B04EAC /* RRBillContactListVC.m in Sources */,
				8DE5C5A02536D573000712E0 /* RRFloatPictureView.m in Sources */,
				B4ED8CCA2B26ED0400D4A3F7 /* RRSaveQrCodeView.m in Sources */,
				6997F4872BA91F70006E4E03 /* RRZipitVC.m in Sources */,
				B470C7942B24957C000C47F1 /* RRBillManagerVC.m in Sources */,
				8D64EF5D253430CC00D2E08E /* RRAuthCodeUseListCell.m in Sources */,
				B4C3ED822B10966000FC3D53 /* RRWithdrawalAccountPickView.m in Sources */,
				8D73D82024F3E3FF0022ABBD /* UIView+CornerRadius.m in Sources */,
				8D0F93DD24002B9400B04EAC /* RRReceiveCell.m in Sources */,
				B470C79B2B249A29000C47F1 /* RRBillInfoInputVC.m in Sources */,
				8DD4F3292413F57B00FB55EF /* RRPersonalInfoVC.m in Sources */,
				8D7C040B2403D47A00FD57A3 /* RRXNStyleDIY.m in Sources */,
				B470C7A72B24ADFB000C47F1 /* RRBillPaymentRecordCell.m in Sources */,
				6997F48A2BA9CE0D006E4E03 /* RRPaymentAccountSequenceVC.m in Sources */,
				B46813592AB99A22002C3438 /* AliPayItem.m in Sources */,
				B46C2C992A088C9B0075C196 /* RRJWBaseView.m in Sources */,
				B4016DAE2B4CFAB90072D554 /* RRConfirmPaymentVC.m in Sources */,
				8D13299E2412815600CA6E11 /* RRAddBankCardVC.m in Sources */,
				8DB88955250C78FF006A5AC3 /* RRDeviceManageVC.m in Sources */,
				B4D17A1A2B2E91700090D568 /* RRAirTimeBundleTopVC.m in Sources */,
				8DEE999F23F5ABFE00C47CB3 /* RRContactInfVC.m in Sources */,
				8D244C322447333D0031595E /* RRFlutterVC.m in Sources */,
				8D109D23240CE789001261C8 /* RRPaymentResultVC.m in Sources */,
				8DEE999923F5A4B300C47CB3 /* RRContactListVC.m in Sources */,
				8D02290223E9B8E8008EB3F4 /* RRLeftVC.m in Sources */,
				8D0228E123E91901008EB3F4 /* RRTools.m in Sources */,
				8D1329A5241289B000CA6E11 /* RRBankNameCell.m in Sources */,
				695A24782C05684B00109FF5 /* RRCommonInputView.m in Sources */,
				8D7D2FC4241D370700775E59 /* RRNavigationController.m in Sources */,
				8D3F342A23EBEDF5009063F8 /* RRXNTipLabel.m in Sources */,
				8D3F345F23EECA51009063F8 /* RRAuthPayPwdVC.m in Sources */,
				B4A9AF2E2AA8500D000CF6A1 /* RRCurrencyAccountView.m in Sources */,
				8D3F342323EABA6D009063F8 /* RRXNTextView.m in Sources */,
				6936784D2C0482E900817B5A /* RRMerchantCodeView.m in Sources */,
				69DDF33B2B63A4C2006DAE64 /* RRJuniorAccountVC.m in Sources */,
				6977AC712B392F1C00879110 /* RRAutomaticDebitVC.m in Sources */,
				B40FEDB2272BE2ED00640151 /* RRBlurView.m in Sources */,
				8DEE999223F58E6300C47CB3 /* RRTransferAccountVC.m in Sources */,
				B4E02F7A2B392564000D9856 /* RRAirtimeBundleModel.m in Sources */,
				8D3F344D23EE8EA9009063F8 /* RRPwdManageVC.m in Sources */,
				8D0228EA23E97004008EB3F4 /* RRLoginVC.m in Sources */,
				8D6685ED241CEF3400AEC5DC /* RRAboutVC.m in Sources */,
				B4D17A1D2B2EF71C0090D568 /* RRBillerInfoModel.m in Sources */,
				697047C62B82430C009543EC /* RRPersonalInfoAvactorCell.m in Sources */,
				8DD4F3302414148800FB55EF /* RRModifyNicknameVC.m in Sources */,
				B4E018392AEA4ADE00531BD0 /* RRRegisterProcessView.m in Sources */,
				69E2C7832B39826400BE6EB2 /* BuyAirtimeBundleResultVC.m in Sources */,
				B4DB371029A5FD25009B7529 /* RRBalanceView.m in Sources */,
				6990B0852B2FEB4B007D917B /* RRNoPINPaymentVC.m in Sources */,
				698CCF852B67E88C00FFD9CA /* RRJuniorUpgradeFResultVC.m in Sources */,
				8D64EF522534043F00D2E08E /* RRTempDeviceLoginVC.m in Sources */,
				8D94698A242284D600F41711 /* RRNativeCheckoutResultVC.m in Sources */,
				690A07A72CEB3A10005C1EE4 /* RCashOutResultVC.m in Sources */,
				B4E018352AEA3C5500531BD0 /* RRSelectLanguageVC.m in Sources */,
				6928D2952C25586D001D0802 /* UIDevice+VGAddition.m in Sources */,
				B4C1604E29A704DF002D5740 /* RRBalanceRecordModel.m in Sources */,
				8DD4F333241414A900FB55EF /* RRModifyEmailVC.m in Sources */,
				697047BF2B81EC8F009543EC /* RRCheckJuniorAccountDetailsVC.m in Sources */,
				8D3F343423EC6EFC009063F8 /* RRVerifyCodeVC.m in Sources */,
				8D0F93BA23FE52F300B04EAC /* RRSetLoginPwdVC.m in Sources */,
				8DE674A8246AA14400E52F70 /* RRLssuedModel.m in Sources */,
				8D996C4124109F480020E6EF /* UIButton+Category.m in Sources */,
				8D1329972412688700CA6E11 /* RRBankCardListVC.m in Sources */,
				8DD4F3482416788D00FB55EF /* RRModifyMobileResultVC.m in Sources */,
				B4E018442AEE0DC500531BD0 /* NSDate+CXCategory.m in Sources */,
				B46813472AB98963002C3438 /* RRLoginPwdVerifyVC.m in Sources */,
				B453B0C52B3292D1008D787C /* RRBundleTopView.m in Sources */,
				8DE1DF3823F79148002D04CA /* UITextField+Category.m in Sources */,
				8D109D20240CE774001261C8 /* RRPaymentVC.m in Sources */,
				8D946988242284D600F41711 /* RRCheckoutView.m in Sources */,
				8D0228F523E9B492008EB3F4 /* RRNationalBaseVC.m in Sources */,
				B40FEDB5272C020A00640151 /* RRWarningView.m in Sources */,
				8DD4F33E2415672C00FB55EF /* RRLoginPwdUnlockVC.m in Sources */,
				69E2C7802B39753300BE6EB2 /* BuyAirtimeBundleVC.m in Sources */,
				8DEE998823F546B900C47CB3 /* RRReceiveQrCodeVC.m in Sources */,
				8D3F343E23ED7493009063F8 /* RRAccountVC.m in Sources */,
				8D13299B24126AB800CA6E11 /* RRBankCardCell.m in Sources */,
				B4DFE1382AA9A101007BA3AC /* RRActiveAccountVC.m in Sources */,
				8D94697F2422849500F41711 /* RRPortraitSetVC.m in Sources */,
				8DE674A22468FEAF00E52F70 /* RRContact.m in Sources */,
				8D64EF642534310300D2E08E /* RRAuthCodeUseListModel.m in Sources */,
				8D0228FF23E9B893008EB3F4 /* NSBundle+XNCategory.m in Sources */,
				695A247E2C056DDD00109FF5 /* RRBillerCodeInputVC.m in Sources */,
				69E2C77D2B396F2000BE6EB2 /* RRAutomaticDebitDetailVC.m in Sources */,
				8D6685E3241CB97400AEC5DC /* RRRetrieveUsernameVC.m in Sources */,
				8DE5C5952536A432000712E0 /* RRPromotionImgModel.m in Sources */,
				B4E018452AEE0DC500531BD0 /* CXDatePickerViewManager.m in Sources */,
				6977AC742B39631100879110 /* RRAutomaticDebitCell.m in Sources */,
				B4ED8CC52B26DC7A00D4A3F7 /* RRUserQRCodeVC.m in Sources */,
				69F7F72B2B70869B003BB98E /* RRSecretWordsVC.m in Sources */,
				8D0228FE23E9B893008EB3F4 /* XNLanguageConfig.m in Sources */,
				8DD4F3452416572700FB55EF /* RRContactTransferRecordVC.m in Sources */,
				B4F12ACC2B34350300896314 /* RRBundleAppCell.m in Sources */,
				8DEE999C23F5AA9900C47CB3 /* RRContactAddVC.m in Sources */,
				8DC829A824791A60007EBA45 /* RRPaymentMethodModel.m in Sources */,
				8DE674B5246B9C3400E52F70 /* RRBillPersonAmtModel.m in Sources */,
				B4079FA62AE229970076460D /* CouponSDK.m in Sources */,
				8D25D4CC2466B321009F25BA /* RRUserInfo.m in Sources */,
				B46C2C962A088C9B0075C196 /* RRJWMerchantInfomationAddressCell.m in Sources */,
				698DCBDD2B679520005D0205 /* RRJuniorAccountDetailVC.m in Sources */,
				695A24842C058CA000109FF5 /* RRCommonSelectView.m in Sources */,
				B4F12AC92B34262900896314 /* RRBundleItemCell.m in Sources */,
				8D64EF7E25358FA200D2E08E /* RRChangeMasterDeviceResultVC.m in Sources */,
				8DE674AB246AA18900E52F70 /* RRReceiveModel.m in Sources */,
				6977AC6D2B39135600879110 /* PaymentOrderVC.m in Sources */,
				8DD4F31A2412AF6200FB55EF /* RRBindBankCardResultVC.m in Sources */,
				8D02289423E7D392008EB3F4 /* ViewController.m in Sources */,
				8D6685E5241CB97400AEC5DC /* RRRetrievePwdVC.m in Sources */,
				69EF34262B3132DA00ADE484 /* RRPINValidateView.m in Sources */,
				8D0228DA23E9178D008EB3F4 /* RRHttpRequest.m in Sources */,
				8DBB5B8223FB89ED00CFEF1A /* RRHUDView.m in Sources */,
				B470C7A12B24AC80000C47F1 /* RRBillPaymentResultVC.m in Sources */,
				8D0228DD23E918B5008EB3F4 /* SecurityUtility.m in Sources */,
				8D7C04022403698900FD57A3 /* RRFixedTransferVC.m in Sources */,
				8D3F345923EEBC70009063F8 /* RREditLoginPwdResultVC.m in Sources */,
				B470C7982B2496E2000C47F1 /* RRBIillTypeCell.m in Sources */,
				B421BE7A2B3588DF00488ACF /* RRAirtimeBundleResultVC.m in Sources */,
				8D0F93CB23FEC40000B04EAC /* RRBillPersonAmtView.m in Sources */,
				698CCF822B67AEA300FFD9CA /* RRJuniorAccountUpgradeVC.m in Sources */,
				8DD4F33B2415608500FB55EF /* RRLoginUnlockVC.m in Sources */,
				B46813612AB9BF48002C3438 /* RRGestureLockView.m in Sources */,
				8D7D2FC7241D470400775E59 /* RRDatePicker.m in Sources */,
				8D6685E4241CB97400AEC5DC /* RRRetrieveResultVC.m in Sources */,
				8DA88D2A23FC15610057A82F /* OrderedDictionary.m in Sources */,
				8D02289F23E7D394008EB3F4 /* main.m in Sources */,
				8D0F93C223FE725E00B04EAC /* RRSplitBillResultVC.m in Sources */,
				B46813442AB94C8F002C3438 /* RRLoginLockView.m in Sources */,
				8DEE998B23F5642800C47CB3 /* RRReceiveQrCodeSetPriceVC.m in Sources */,
				8DD4F3252413C39D00FB55EF /* RRWithdrawalStatusView.m in Sources */,
				B46C2C9C2A088C9C0075C196 /* RRJWShopInfoView.m in Sources */,
				8D56D5462484A91D0083608E /* RRHistoryDetailsModel.m in Sources */,
				69F7DB402B14323200D908D8 /* RRStatementEnquiryVC.m in Sources */,
				B46C2C932A088C9B0075C196 /* RRJWMerchantInfomationVC.m in Sources */,
				8DD4F3212413A55E00FB55EF /* RRWithdrawalResultVC.m in Sources */,
				8DEE998F23F56E2800C47CB3 /* RRTransferListVC.m in Sources */,
				B4F534032A08EBC0002E1985 /* RRJWMerchantInfoModel.m in Sources */,
				8D3F344423ED9CCF009063F8 /* RRXNListBtn.m in Sources */,
				B46C2C9B2A088C9B0075C196 /* RRJWMerchantListCell.m in Sources */,
				8D9F058123F1AC97004D0817 /* RRXNLoginInputView.m in Sources */,
				B4F12AC02B341DF600896314 /* RRBundleItemView.m in Sources */,
				8DA88D2423FBE78A0057A82F /* RRHistoryListCell.m in Sources */,
				B46C2C9A2A088C9B0075C196 /* RRJWMerchantInfomationCell.m in Sources */,
				8D37C9A523EFCFC000E71E5D /* RRXNTextField.m in Sources */,
				B4DB370D29A5F5B2009B7529 /* RRAccountHistoryListCell.m in Sources */,
				8DA88D2723FC028E0057A82F /* RRHistoryListDetailsVC.m in Sources */,
				B46C2C952A088C9B0075C196 /* RRJWMerchantModel.m in Sources */,
				69DDF3402B63A5D8006DAE64 /* RRJuniorAccountCell.m in Sources */,
				8D3F343723EC797F009063F8 /* RRRegisterResultVC.m in Sources */,
				8D109D19240C2DBF001261C8 /* RRPaymentQrCodeVC.m in Sources */,
				8D1329A22412880500CA6E11 /* RRBankNameVC.m in Sources */,
				8DA3AAC92578D30100B7354C /* RRHomeFunCell.m in Sources */,
				6997F48D2BA9D677006E4E03 /* RRAccountSequenceCell.m in Sources */,
				8D0228E623E91CF7008EB3F4 /* JSONKit.m in Sources */,
				8D6685DB241C8E8E00AEC5DC /* XNExceptionHandler.m in Sources */,
				8DD4F31E241377A600FB55EF /* RRWithdrawalVC.m in Sources */,
				8DF96231249883600005DB6E /* RRContactUsVC.m in Sources */,
				69F138902BDAB4D500E056F5 /* CustomAlertAction.m in Sources */,
				8DD4F33724141D0100FB55EF /* RRModifyMobileVC.m in Sources */,
				B46C2C942A088C9B0075C196 /* RRJWMerchantListVC.m in Sources */,
				B470C7A42B24ADD1000C47F1 /* RRBIllPaymentRecordsVC.m in Sources */,
				69F7DB432B143C8200D908D8 /* RRStatementEnquiryCell.m in Sources */,
				8DE674A5246A8CDB00E52F70 /* RRHistoryModel.m in Sources */,
				690A07962CDFF3B6005C1EE4 /* CustomMJRefreshNormalHeader.m in Sources */,
				8DCCDCB02546B869008E1793 /* RRBroadcastVC.m in Sources */,
				8D6CA51424B047DF00BCF643 /* RRTopUpResultVC.m in Sources */,
				B4DB370A29A5F2B3009B7529 /* RRAccountHistoryVC.m in Sources */,
				8D0228F223E9B400008EB3F4 /* RRHomeVC.m in Sources */,
				690A07A12CE4B624005C1EE4 /* RRCashOutVC.m in Sources */,
				69366F272B70D43C0065BC73 /* RRCheckJuniorAccountVC.m in Sources */,
				8D0F93E32400D9DC00B04EAC /* RRReceivedDetailsVC.m in Sources */,
				690A07A42CE7414A005C1EE4 /* RRRegisterMobileVC.m in Sources */,
				8D02289123E7D392008EB3F4 /* AppDelegate.m in Sources */,
				697047C92B82E73B009543EC /* RRJuniorBalanceView.m in Sources */,
				8DA88D2023FBB9710057A82F /* RRHistoryListVC.m in Sources */,
				8D37C9AB23EFF44000E71E5D /* RRCommonPickerView.m in Sources */,
				B44A90182B441D5700E8B1A3 /* RRMoreFuctionCell.m in Sources */,
				8DB8895B250CDB52006A5AC3 /* RRAuthCodeDisplayVC.m in Sources */,
				8DEE999523F5925700C47CB3 /* RRTransferVC.m in Sources */,
				B4F534002A08D55C002E1985 /* RRJWMerchantCategoryModel.m in Sources */,
				698DCBE02B679E50005D0205 /* RRJuniorAccountChangeVC.m in Sources */,
				695A24812C0575B000109FF5 /* RRCommonAmountView.m in Sources */,
				B4DC2B3A29BF221700FF8162 /* RRTermProtocolVC.m in Sources */,
				8D3F343A23ED0287009063F8 /* RRShadowButton.m in Sources */,
				B453B0C22B3292B7008D787C /* RRMobileTopView.m in Sources */,
				8D3F346523EEE791009063F8 /* RREditPayPwdResultVC.m in Sources */,
				B468135E2AB99A34002C3438 /* RRGestureViewController.m in Sources */,
				8DA0BE7924B7178F00EF4D4D /* RRVersionUpdateView.m in Sources */,
				8DE674B1246AA38000E52F70 /* RRCardModel.m in Sources */,
				8D37C9AF23F0229C00E71E5D /* RRKeyBoardView.m in Sources */,
				8D7C04062403AD4E00FD57A3 /* RRXNScanVC.m in Sources */,
				8D1071FB254067230075A927 /* AppDelegate+JPushService.m in Sources */,
				B46C2C982A088C9B0075C196 /* RRJWMerchantListFiltrateView.m in Sources */,
				8DCCDCAA2546B36E008E1793 /* RRAVPlayer.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D0228A023E7D394008EB3F4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8D0228A923E7D394008EB3F4 /* xWalletPro_iOSTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D0228AB23E7D394008EB3F4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8D0228B423E7D394008EB3F4 /* xWalletPro_iOSUITests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8DCFC7D825594218000EF0AB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8DCFC7E025594218000EF0AB /* NotificationService.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		8D0228A623E7D394008EB3F4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8D02288B23E7D392008EB3F4 /* xWalletPro_iOS */;
			targetProxy = 8D0228A523E7D394008EB3F4 /* PBXContainerItemProxy */;
		};
		8D0228B123E7D394008EB3F4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8D02288B23E7D392008EB3F4 /* xWalletPro_iOS */;
			targetProxy = 8D0228B023E7D394008EB3F4 /* PBXContainerItemProxy */;
		};
		8DCFC7E325594218000EF0AB /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8DCFC7DB25594218000EF0AB /* pushServiceExtension */;
			targetProxy = 8DCFC7E225594218000EF0AB /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		69CDA0992C1831EC0050440E /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				69CDA09A2C1831EC0050440E /* Base */,
				69FA3E1E2C18442F005C9650 /* en */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
			usesTabs = 0;
		};
		8D02289523E7D392008EB3F4 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				8D02289623E7D392008EB3F4 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		8D0228C923E7DDDD008EB3F4 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				8D0228C823E7DDDD008EB3F4 /* en */,
				B496F7102AEFC2DC0060DB92 /* sn-ZW */,
				B496F7132AEFC2FC0060DB92 /* nd-ZW */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
		8D0228CF23E7E040008EB3F4 /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				8D0228CE23E7E040008EB3F4 /* en */,
				B496F7142AEFC2FC0060DB92 /* nd-ZW */,
				69FF451A2CDB7FF400CF6194 /* sn */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		8D0228B623E7D394008EB3F4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		8D0228B723E7D394008EB3F4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		8D0228B923E7D394008EB3F4 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 72D3AD5752D61CC3355A87F8 /* Pods-xWalletPro_iOS.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CODE_SIGN_ENTITLEMENTS = xWalletPro_iOS/xWalletPro_iOS.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1000;
				DEVELOPMENT_TEAM = FYF9VWJGK3;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/xWalletPro_iOS/viewControllers/Coupon",
					"$(PROJECT_DIR)/xWalletPro_iOS/viewControllers",
				);
				GCC_PREFIX_HEADER = $SRCROOT/PrefixHeader.pch;
				INFOPLIST_FILE = xWalletPro_iOS/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "OneMoney Mobile";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/xWalletPro_iOS",
					"$(PROJECT_DIR)/xWalletPro_iOS/viewControllers",
					"$(PROJECT_DIR)/xWalletPro_iOS/viewControllers/BillPayment",
				);
				MARKETING_VERSION = 1.1.1;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-l\"CRBoxInputView\"",
					"-l\"CWLateralSlide\"",
					"-l\"FirebaseCore\"",
					"-l\"FirebaseCoreDiagnostics\"",
					"-l\"FirebaseInstallations\"",
					"-l\"FirebaseMessaging\"",
					"-l\"FlutterPluginRegistrant\"",
					"-l\"GMObjC\"",
					"-l\"GoogleDataTransport\"",
					"-l\"GoogleUtilities\"",
					"-l\"LBXScan\"",
					"-l\"MBProgressHUD\"",
					"-l\"MJExtension\"",
					"-l\"MJRefresh\"",
					"-l\"Masonry\"",
					"-l\"PromisesObjC\"",
					"-l\"RRXNNetwork\"",
					"-l\"SDCycleScrollView\"",
					"-l\"SDWebImage\"",
					"-l\"c++\"",
					"-l\"lottie-ios\"",
					"-l\"nanopb\"",
					"-l\"sqlite3\"",
					"-l\"z\"",
					"-framework",
					"\"AVFoundation\"",
					"-framework",
					"\"Accelerate\"",
					"-framework",
					"\"App\"",
					"-framework",
					"\"CoreData\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreImage\"",
					"-framework",
					"\"CoreLocation\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"CoreText\"",
					"-framework",
					"\"Flutter\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"GLKit\"",
					"-framework",
					"\"GoogleMaps\"",
					"-framework",
					"\"GoogleMapsBase\"",
					"-framework",
					"\"GoogleMapsCore\"",
					"-framework",
					"\"GooglePlaces\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"Metal\"",
					"-framework",
					"\"OpenGLES\"",
					"-framework",
					"\"OpenSSL\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"UIKit\"",
					"-weak_framework",
					"\"UserNotifications\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = zw.co.onemoney;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		8D0228BA23E7D394008EB3F4 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 57466F3357655E842D2ABDF8 /* Pods-xWalletPro_iOS.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CODE_SIGN_ENTITLEMENTS = xWalletPro_iOS/xWalletPro_iOS.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1000;
				DEVELOPMENT_TEAM = FYF9VWJGK3;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/xWalletPro_iOS/viewControllers/Coupon",
					"$(PROJECT_DIR)/xWalletPro_iOS/viewControllers",
				);
				GCC_PREFIX_HEADER = $SRCROOT/PrefixHeader.pch;
				INFOPLIST_FILE = xWalletPro_iOS/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "OneMoney Mobile";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/xWalletPro_iOS",
					"$(PROJECT_DIR)/xWalletPro_iOS/viewControllers",
					"$(PROJECT_DIR)/xWalletPro_iOS/viewControllers/BillPayment",
				);
				MARKETING_VERSION = 1.1.1;
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-l\"CRBoxInputView\"",
					"-l\"CWLateralSlide\"",
					"-l\"FirebaseCore\"",
					"-l\"FirebaseCoreDiagnostics\"",
					"-l\"FirebaseInstallations\"",
					"-l\"FirebaseMessaging\"",
					"-l\"FlutterPluginRegistrant\"",
					"-l\"GMObjC\"",
					"-l\"GoogleDataTransport\"",
					"-l\"GoogleUtilities\"",
					"-l\"LBXScan\"",
					"-l\"MBProgressHUD\"",
					"-l\"MJExtension\"",
					"-l\"MJRefresh\"",
					"-l\"Masonry\"",
					"-l\"PromisesObjC\"",
					"-l\"RRXNNetwork\"",
					"-l\"SDCycleScrollView\"",
					"-l\"SDWebImage\"",
					"-l\"c++\"",
					"-l\"lottie-ios\"",
					"-l\"nanopb\"",
					"-l\"sqlite3\"",
					"-l\"z\"",
					"-framework",
					"\"AVFoundation\"",
					"-framework",
					"\"Accelerate\"",
					"-framework",
					"\"App\"",
					"-framework",
					"\"CoreData\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreImage\"",
					"-framework",
					"\"CoreLocation\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"CoreText\"",
					"-framework",
					"\"Flutter\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"GLKit\"",
					"-framework",
					"\"GoogleMaps\"",
					"-framework",
					"\"GoogleMapsBase\"",
					"-framework",
					"\"GoogleMapsCore\"",
					"-framework",
					"\"GooglePlaces\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"Metal\"",
					"-framework",
					"\"OpenGLES\"",
					"-framework",
					"\"OpenSSL\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"UIKit\"",
					"-weak_framework",
					"\"UserNotifications\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = zw.co.onemoney;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		8D0228BC23E7D394008EB3F4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = FYF9VWJGK3;
				INFOPLIST_FILE = xWalletPro_iOSTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "zw.co.onemoney-iOSTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/xWalletPro_iOS.app/xWalletPro_iOS";
			};
			name = Debug;
		};
		8D0228BD23E7D394008EB3F4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = FYF9VWJGK3;
				INFOPLIST_FILE = xWalletPro_iOSTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "zw.co.onemoney-iOSTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/xWalletPro_iOS.app/xWalletPro_iOS";
			};
			name = Release;
		};
		8D0228BF23E7D394008EB3F4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = FYF9VWJGK3;
				INFOPLIST_FILE = xWalletPro_iOSUITests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "zw.co.onemoney-iOSUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = xWalletPro_iOS;
			};
			name = Debug;
		};
		8D0228C023E7D394008EB3F4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = FYF9VWJGK3;
				INFOPLIST_FILE = xWalletPro_iOSUITests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "zw.co.onemoney-iOSUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = xWalletPro_iOS;
			};
			name = Release;
		};
		8DCFC7E525594218000EF0AB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_ENTITLEMENTS = pushServiceExtension/pushServiceExtension.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = FYF9VWJGK3;
				INFOPLIST_FILE = pushServiceExtension/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = zw.co.onemoney.pushServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		8DCFC7E625594218000EF0AB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_ENTITLEMENTS = pushServiceExtension/pushServiceExtension.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = FYF9VWJGK3;
				INFOPLIST_FILE = pushServiceExtension/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = zw.co.onemoney.pushServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		8D02288723E7D392008EB3F4 /* Build configuration list for PBXProject "xWalletPro_iOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8D0228B623E7D394008EB3F4 /* Debug */,
				8D0228B723E7D394008EB3F4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8D0228B823E7D394008EB3F4 /* Build configuration list for PBXNativeTarget "xWalletPro_iOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8D0228B923E7D394008EB3F4 /* Debug */,
				8D0228BA23E7D394008EB3F4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8D0228BB23E7D394008EB3F4 /* Build configuration list for PBXNativeTarget "xWalletPro_iOSTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8D0228BC23E7D394008EB3F4 /* Debug */,
				8D0228BD23E7D394008EB3F4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8D0228BE23E7D394008EB3F4 /* Build configuration list for PBXNativeTarget "xWalletPro_iOSUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8D0228BF23E7D394008EB3F4 /* Debug */,
				8D0228C023E7D394008EB3F4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8DCFC7E725594218000EF0AB /* Build configuration list for PBXNativeTarget "pushServiceExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8DCFC7E525594218000EF0AB /* Debug */,
				8DCFC7E625594218000EF0AB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 8D02288423E7D392008EB3F4 /* Project object */;
}
